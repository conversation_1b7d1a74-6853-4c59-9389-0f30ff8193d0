-- MySQL dump 10.13  Distrib 5.6.40, for linux-glibc2.12 (x86_64)
--
-- Host: localhost    Database: sot_flow
-- ------------------------------------------------------
-- Server version	5.6.40

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `sound_identy_basic_info`
--

DROP TABLE IF EXISTS `sound_identy_basic_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sound_identy_basic_info` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(30) DEFAULT NULL COMMENT '工单编号',
  `identy_type` varchar(30) DEFAULT NULL COMMENT '工单类型',
  `identy_subtype` varchar(30) DEFAULT NULL COMMENT '工单子类型',
  `identy_detail` varchar(100) DEFAULT NULL COMMENT '工单细类',
  `title` varchar(100) DEFAULT NULL COMMENT '工单标题',
  `content` varchar(200) DEFAULT NULL COMMENT '工单内容',
  `origin_unit` varchar(30) DEFAULT NULL COMMENT '工单发起方',
  `receiver_unit` varchar(30) DEFAULT NULL COMMENT '工单接收方',
  `creat_time` varchar(30) DEFAULT NULL COMMENT '工单创建时间',
  `process_time` varchar(30) DEFAULT NULL COMMENT '工单要求处理时间',
  `creator` varchar(30) DEFAULT NULL COMMENT '工单创建人',
  `creator_contact_info` varchar(30) DEFAULT NULL COMMENT '创建人联系方式',
  `attach_list` varchar(500) DEFAULT NULL COMMENT '附件',
  `identy_status` varchar(10) DEFAULT NULL COMMENT '任务状态',
  `warning_level` varchar(10) DEFAULT NULL COMMENT '预警级别',
  `sender_flag` tinyint(2) DEFAULT NULL COMMENT '发送方向 1集团 0省份',
  `reply_flag` varchar(10) DEFAULT NULL COMMENT '申请单状态',
  `creat_date` date DEFAULT NULL COMMENT '数据日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COMMENT='工单创建信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sound_identy_basic_info`
--

LOCK TABLES `sound_identy_basic_info` WRITE;
/*!40000 ALTER TABLE `sound_identy_basic_info` DISABLE KEYS */;
INSERT INTO `sound_identy_basic_info` VALUES (7,'20220224GZL43112771728','03','0301','03030101','吉林省24日测试申请单','吉林省24日测试申请单','431','0057','20220224222611','20220228000000','金东勋','15948313130','GZLSEND_1001_431_20220224_001.xlsx','04',NULL,0,NULL,'2022-02-24'),(8,'20220224GZL43116946989','03','0301','03030101','吉林24日测试第2份申请单','吉林24日测试第2份申请单','431','0057','20220224233546','20220227000000','金东勋','15948313130','GZLSEND_1001_431_20220224_001.xlsx','03',NULL,0,NULL,'2022-02-24'),(9,'20220302GZL00570045623','00','0001','00000101','吉林20220221-20220227重点政策投诉预测预警单','2022年08周（20220221-20220227）电信诈骗投诉量73件，违规外呼投诉量21件，请在2022-03-07 09:00:45前反馈预警原因和处理结果。','0057','431','20220302090045','20220307090045','系统创建',NULL,'GZLSEND_1001_431_20220302_000.xlsx',NULL,NULL,1,NULL,'2022-03-02'),(10,'20220217GZL62310002278','00','0005','00000501','吉林2022年1月不知情定制预警单','2022年02月17日，吉林下发不知情定制预警单如下，请核查并反馈，谢谢。','0057','431','20220217151825','20220305160000','系统创建',NULL,'GZLSEND_1001_431_20220217_000.xlsx',NULL,NULL,1,NULL,'2022-03-02'),(11,'20220307GZL00570046689','00','0001','00000101','吉林20220228-20220306重点政策投诉预测预警单','2022年09周（20220228-20220306）电信诈骗投诉量67件，违规外呼投诉量33件，请在2022-03-10 16:00:46前反馈预警原因和处理结果。','0057','431','20220307160046','20220310160046','系统创建',NULL,'GZLSEND_1001_431_20220307_000.xlsx',NULL,NULL,1,NULL,'2022-03-07'),(12,'20220314GZL00570049084','00','0001','00000101','吉林20220307-20220313重点政策投诉预测预警单','2022年10周（20220307-20220313）电信诈骗投诉量63件，违规外呼投诉量23件，请在2022-03-17 16:00:48前反馈预警原因和处理结果。','0057','431','20220314160049','20220317160048','系统创建',NULL,'GZLSEND_1001_431_20220314_000.xlsx','03',NULL,1,NULL,'2022-03-14'),(13,'20220319GZL62310002334','00','0005','00000501','吉林2022年2月不知情定制预警单','2022年03月19日，吉林下发不知情定制预警单如下，请核查并反馈，谢谢。','0057','431','20220319201641','20220401235959','系统创建',NULL,'GZLSEND_1001_431_20220319_000.xlsx',NULL,NULL,1,NULL,'2022-03-19'),(14,'20220321GZL00570048970','00','0001','00000101','吉林20220314-20220320重点政策投诉预测预警单','2022年11周（20220314-20220320）新老不同权投诉量3件，电信诈骗投诉量63件，违规外呼投诉量9件，请在2022-03-24 16:00:48前反馈预警原因和处理结果。','0057','431','20220321160049','20220324160048','系统创建',NULL,'GZLSEND_1001_431_20220321_000.xlsx','03',NULL,1,NULL,'2022-03-21'),(15,'20220331GZL00572750818','00','0001','00000101','吉林20220321-20220327重点政策投诉预测预警单','2022年12周（20220321-20220327）电信诈骗投诉量273件，违规外呼投诉量9件，请在2022-04-03 00:27:50前反馈预警原因和处理结果。','0057','431','20220331002751','20220403002750','系统创建',NULL,'GZLSEND_1001_431_20220331_000.xlsx',NULL,NULL,1,NULL,'2022-03-31'),(16,'20220331GZL00570046487','00','0001','00000101','吉林20220321-20220327重点政策投诉预测预警单','2022年12周（20220321-20220327）电信诈骗投诉量273件，违规外呼投诉量9件，请在2022-04-03 09:00:46前反馈预警原因和处理结果。','0057','431','20220331090046','20220403090046','系统创建',NULL,'GZLSEND_1001_431_20220331_001.xlsx',NULL,NULL,1,NULL,'2022-03-31');
/*!40000 ALTER TABLE `sound_identy_basic_info` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-03-31 12:01:36
