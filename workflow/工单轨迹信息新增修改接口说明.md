# 工单轨迹信息新增和修改接口说明

## 概述

新增了用于新增、修改和删除工单轨迹信息的接口，支持完整的CRUD操作，提供了灵活的参数校验和数据转换功能。

## 接口列表

### 1. 新增工单轨迹信息

#### 接口信息
- **接口地址**: `POST /apply/createTrackinforInfo`
- **功能描述**: 新增工单轨迹信息记录
- **请求参数**: `TrackinforCreateReq`
- **响应数据**: 返回新增记录的ID

#### 请求参数说明
```json
{
  "identifier": "工单编号",                    // 必填
  "processInstanceId": "流程实例ID",           // 可选
  "taskId": "任务ID",                        // 可选
  "interfaceLink": "01",                     // 必填，01：工单派发环节 02：工单处理环节
  "trackType": "01",                         // 必填，01：流转环节 02：审核环节 03：其他环节
  "handingTime": "20250111120000",           // 可选，格式：YYYYMMDDHHMMSS，不传则使用当前时间
  "handingDepartment": "处理部门",            // 可选
  "handler": "处理人",                       // 必填
  "handlerLeadLevel": "01",                  // 可选，01：省专公司领导 02：二级领导 03：三级领导
  "handlerRank": "处理人职级",                // 可选
  "handlerContactInfor": "联系方式",          // 可选
  "handingOpinions": "处理意见",              // 必填
  "attachNameList": ["附件1.pdf", "附件2.doc"], // 可选，附件名称列表
  "attachList": ["file1", "file2"],          // 可选，附件列表
  "attachFileNameList": ["file1.pdf", "file2.doc"], // 可选，附件文件名列表
  "isReply": 0                               // 可选，0：轨迹 1：回复，默认0
}
```

#### 响应示例
```json
{
  "code": "200",
  "message": "操作成功",
  "data": 1234567890123456789
}
```

### 2. 修改工单轨迹信息

#### 接口信息
- **接口地址**: `POST /apply/updateTrackinforInfo`
- **功能描述**: 根据ID修改工单轨迹信息记录
- **请求参数**: `TrackinforUpdateReq`
- **响应数据**: 返回修改是否成功

#### 请求参数说明
```json
{
  "id": 1234567890123456789,                 // 必填，轨迹记录ID
  "identifier": "工单编号",                   // 可选
  "processInstanceId": "流程实例ID",          // 可选
  "taskId": "任务ID",                        // 可选
  "interfaceLink": "02",                     // 可选，01：工单派发环节 02：工单处理环节
  "trackType": "02",                         // 可选，01：流转环节 02：审核环节 03：其他环节
  "handingTime": "20250111130000",           // 可选，格式：YYYYMMDDHHMMSS
  "handingDepartment": "新处理部门",          // 可选
  "handler": "新处理人",                     // 可选
  "handlerLeadLevel": "02",                  // 可选，01：省专公司领导 02：二级领导 03：三级领导
  "handlerRank": "新处理人职级",              // 可选
  "handlerContactInfor": "新联系方式",        // 可选
  "handingOpinions": "修改后的处理意见",       // 可选
  "attachNameList": ["新附件1.pdf"],         // 可选，附件名称列表
  "attachList": ["newfile1"],                // 可选，附件列表
  "attachFileNameList": ["newfile1.pdf"],    // 可选，附件文件名列表
  "isReply": 1,                              // 可选，0：轨迹 1：回复
  "updateOnlyNonNull": true                  // 可选，是否只更新非空字段，默认true
}
```

#### 响应示例
```json
{
  "code": "200",
  "message": "操作成功",
  "data": true
}
```

### 3. 根据ID删除工单轨迹信息

#### 接口信息
- **接口地址**: `POST /apply/deleteTrackinforInfoById`
- **功能描述**: 根据轨迹记录ID删除工单轨迹信息
- **请求参数**: `id` (Long类型，通过RequestParam传递)
- **响应数据**: 返回删除是否成功

#### 请求示例
```
POST /apply/deleteTrackinforInfoById?id=1234567890123456789
```

#### 响应示例
```json
{
  "code": "200",
  "message": "操作成功",
  "data": true
}
```

### 4. 根据工单编号删除轨迹信息

#### 接口信息
- **接口地址**: `POST /apply/deleteTrackinforInfoByIdentifier`
- **功能描述**: 根据工单编号删除所有相关的轨迹信息
- **请求参数**: `identifier` (String类型，通过RequestParam传递)
- **响应数据**: 返回删除的记录数

#### 请求示例
```
POST /apply/deleteTrackinforInfoByIdentifier?identifier=WO202501110001
```

#### 响应示例
```json
{
  "code": "200",
  "message": "操作成功",
  "data": 5
}
```

## 参数校验规则

### 新增接口校验规则
1. **必填字段校验**：
   - `identifier`：工单编号不能为空
   - `interfaceLink`：接口环节类型不能为空
   - `trackType`：轨迹类型不能为空
   - `handler`：工单处理人不能为空
   - `handingOpinions`：工单处理意见不能为空

2. **枚举值校验**：
   - `interfaceLink`：只支持 01、02
   - `trackType`：只支持 01、02、03
   - `handlerLeadLevel`：只支持 01、02、03
   - `isReply`：只支持 0、1

### 修改接口校验规则
1. **必填字段校验**：
   - `id`：轨迹记录ID不能为空

2. **枚举值校验**：与新增接口相同（如果字段有值的话）

3. **记录存在性校验**：修改前会先查询记录是否存在

## 数据转换说明

### 附件字段处理
- **输入**：List<String> 格式的附件列表
- **存储**：转换为分隔符分隔的字符串
  - `attachNameList` 和 `attachList`：使用 `|` 分隔
  - `attachFileNameList`：使用 `,` 分隔

### 时间字段处理
- **输入格式**：YYYYMMDDHHMMSS（如：20250111120000）
- **自动填充**：新增时如果不传 `handingTime`，自动使用当前时间

### 更新策略
- **默认策略**：只更新非空字段（`updateOnlyNonNull=true`）
- **全量更新**：设置 `updateOnlyNonNull=false` 可更新所有字段（包括空值）

## 错误处理

### 常见错误码和处理
1. **参数校验失败**：返回具体的校验错误信息
2. **记录不存在**：修改和删除操作会先检查记录是否存在
3. **数据库操作失败**：返回相应的错误信息
4. **系统异常**：统一异常处理，返回友好的错误信息

### 错误响应示例
```json
{
  "code": "500",
  "message": "工单编号不能为空",
  "data": null
}
```

## 日志记录

所有接口都包含详细的日志记录：
- **操作开始日志**：记录请求参数
- **操作成功日志**：记录操作结果
- **异常日志**：记录详细的异常信息和堆栈

## 使用示例

### 新增轨迹记录
```bash
curl -X POST "http://localhost:8080/apply/createTrackinforInfo" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "WO202501110001",
    "interfaceLink": "02",
    "trackType": "01",
    "handler": "张三",
    "handingOpinions": "已处理完成",
    "handingDepartment": "技术部",
    "attachNameList": ["处理报告.pdf"]
  }'
```

### 修改轨迹记录
```bash
curl -X POST "http://localhost:8080/apply/updateTrackinforInfo" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1234567890123456789,
    "handingOpinions": "修改后的处理意见",
    "isReply": 1
  }'
```

### 删除轨迹记录
```bash
curl -X POST "http://localhost:8080/apply/deleteTrackinforInfoById?id=1234567890123456789"
```

## 注意事项

1. **数据一致性**：删除操作不可逆，请谨慎使用
2. **权限控制**：建议在实际使用中添加相应的权限控制
3. **并发处理**：修改操作建议添加乐观锁或悲观锁机制
4. **数据备份**：重要数据建议在删除前进行备份
5. **性能优化**：大批量操作建议使用批处理接口
