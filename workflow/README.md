## 项目展示页面
- 本地环境：

    http://localhost/demo?userName=jindongxun&redirect=/workflow

    http://localhost:8888/demo?userName=jindongxun&redirect=/workflow
    【用这个】

- 测试环境：

    http://basenx-workflow.asiainfo.work/demo?userName=jindongxun&redirect=/workflow
- 灰度环境：

    http://**************:18090/demo?userName=jindongxun&redirect=/workflow
    【跟生产环境的数据库一样；端口不一样】

- 正式环境：

    http://**************:8090/demo?userName=jindongxun&redirect=/workflow    

    http://**************:8090/demo?userName=pq_liujie&redirect=/workflow

    http://**************:8090/demo?userName=chenjing2&redirect=/workflow

    http://**************:8090/demo?userName=pq_zhouzhaoqin&redirect=/workflow
## swagger
- 本地环境：

  http://localhost:10101/demo?userName=jindongxun

  http://localhost:10101/doc.html
- 测试环境：

  http://basenx-workflow-api.asiainfo.work/demo?userName=jindongxun

  http://basenx-workflow-api.asiainfo.work/doc.html
- 灰度环境：

  http://**************:18090/graydoc/demo?userName=jindongxun

  http://**************:18090/graydoc/doc.html
- 正式环境：

  http://**************:8090/swaggerdoc/demo?userName=jindongxun

  http://**************:8090/swaggerdoc/doc.html
    
## 短信开关
生产上调试工作流之前，必须先关闭短信，然后再打开短信
直接在浏览器上打开下面的地址就可以了

- 关闭发送短信：

    http://**************:8090/swaggerdoc/workflowLoad/sendClose
- 打开发送短信：

    http://**************:8090/swaggerdoc/workflowLoad/sendOpen


## 19下载文件接口
http://**************:8080/nxportal/portal/sys-attach/testDownload/fileName.xxx
## 17下载文件接口
http://**************:18090/test/demo2?name=fileName.xxx
## 17上传文件页面
http://**************:18090/test/doc.html
## 生成工单统计信息
http://**************:8090/swaggerdoc/workflowBoard/analyse?time=202307
http://**************:8090/swaggerdoc/workflowBoard/analyse?time=202307&startTime=202301
## 统计工单时工单名为null的解决,调用此接口
http://**************:8090/swaggerdoc/workflowBoard/insertDimWorkflow

## 生产环境下载日志
http://**************:8090/swaggerdoc/workflowBoard/testDownload  下载log.log

http://**************:8090/swaggerdoc/workflowBoard/testDownload?date=20231109 下载指定日期的日志
## 灰度环境下载日志
http://**************:18090/graydoc/workflowBoard/testDownload  下载log.log

http://**************:18090/graydoc/workflowBoard/testDownload?date=20231109 下载指定日期的日志


## 修改账号后工单从老账号迁移到新账号sql:
update ACT_RU_VARIABLE set TEXT_='新账号' WHERE TEXT_='老账号';

update ACT_HI_VARINST set TEXT_='新账号' WHERE TEXT_='老账号';

update ACT_RU_TASK set ASSIGNEE_='新账号' WHERE ASSIGNEE_='老账号';

update ACT_HI_TASKINST set ASSIGNEE_='新账号' WHERE ASSIGNEE_='老账号';

update ACT_HI_ACTINST set ASSIGNEE_='新账号' WHERE ASSIGNEE_='老账号';

update ACT_HI_ATTACHMENT set USER_ID_='新账号' WHERE USER_ID_='老账号';

update ACT_HI_COMMENT set USER_ID_='新账号' WHERE USER_ID_='老账号';

update ACT_HI_IDENTITYLINK set USER_ID_='新账号' WHERE USER_ID_='老账号';

## 测试环境与集团联调时查看回复集团的附件名
select * from order_track_record where identifier = 'xxxxx'  查看attach_list字段

## 同步附件到集团时 重复执行scheduledDisposeData.disposeXXX()
## 当工单在order_track_task_log表中的记录条数少于配置的条数就执行,详见scheduledDisposeData.disposeXXX()中的cssDao.selectTrackStatus(status);
delete form order_track_task_log where identifier='xxx'

## 项目展示页面(moa,网格通)
login_moa_group接口,一次nginx代理,直接访问
http://127.0.0.1:82/#/login_moa_group?appToken=jindongxun
login_moa_group接口,一次nginx代理,通过jump页访问
http://127.0.0.1:82/?route=login_moa_group&appToken=jindongxun

login_moa_group接口,两次nginx代理,直接访问
http://127.0.0.1:821/groupsndypt/#/login_moa_group?appToken=jindongxun
login_moa_group接口,两次nginx代理,通过jump页访问
http://127.0.0.1:821/groupsndypt/?route=login_moa_group&appToken=jindongxun
---------------------------------------------------------------
login_moa接口,一次nginx代理,直接访问,其它方式和上面一样
http://127.0.0.1:82/#/login_moa?apiToken=xxx&data=VhdsPzNg3erbCl4A/ImjZcMrMMgrntzyl1acS9XikRCrW7WVy9RIloneXhtYAdPSaSIuKEGGkWwmJsYmMHslcg==
login_moa2接口,一次nginx代理,直接访问,其它方式和上面一样
http://127.0.0.1:82/#/login_moa2?apiToken=xxx&uid=jindongxun
login_grid接口,一次nginx代理,直接访问,其它方式和上面一样
http://127.0.0.1:82/#/login_grid?token=xxx&account=TVt25ghZQ+kqRozRgoVsHg==

```nginx配置
    server {
		listen 821;		
		location /groupsndypt {
			rewrite ^.+groupsndypt/?(.*)$ /$1 break;
			include uwsgi_params;
			proxy_pass http://127.0.0.1:82;
		}
	}
	server {
		listen 82;
		
		location / {
			root   C:\Project\workflow\mobile\dist;
			index   index.html index.htm;
			try_files $uri $uri/ /index.html;
		}
		location /api {
			rewrite  ^.+api/?(.*)$ /$1 break;
			include  uwsgi_params;
       		proxy_pass  http://127.0.0.1:10101;
		}
		location /api2 {
			rewrite  ^.+api2/?(.*)$ /$1 break;
			include  uwsgi_params;
       		proxy_pass  http://127.0.0.1:8085;
		}
	}
```


## 主要业务逻辑说明及其它说明
* 工作流流程配置文件路径：./src/main/resources/flow
* 工作流流程的自定义表单路径：./src/main/resources/form
* 集团工单对接的接口文档：【腾讯文档】中国移动网状网基础服务-大音平台业务规范-工作流接口规范V1.3
  https://docs.qq.com/doc/DUUdaVHRCR3duSW9Q
* 集团工单对接的技术文档：【腾讯文档】中国移动网状网基础服务平台技术规范2.0.8
  https://docs.qq.com/doc/DUUxXR1pWRXNyU1Nm
* 集团工单对接的报文样例：【腾讯文档】大音平台工作流报文样例
  https://docs.qq.com/doc/DUVlmdldOcndiZklL
* 省内工单的需求文档：【腾讯文档】大音平台宁夏模块工作流方案@20211027
  https://docs.qq.com/doc/DUUFuc0d1RkhuQ21p
* 给OA推送待办任务的接口文档：《统一待办接口接入规范_v3.0.docx》
*
* 对接短信平台的文档：【腾讯文档】能力开放平台接入规范3.0
  https://docs.qq.com/doc/DUWRTQnJ3b1ZZQ1NL

## 相关第三方教程
https://zhuanlan.zhihu.com/p/417014073

## 动态表单内容说明
* 组件类型
  - dropdown 操作选择，选择处理与回退, 1：处理， 3：回退。默认为处理的参数，回退参数标记：replyFlag=1
  - dropdown_notice 短信通知
  - text 文本
  - select 下拉框，fieldType:OptionFormField,选项options，默认值value
  - date 日期,默认是type=time,YYYYMMDDHHMMSS，type=date 是YYYYMMDD
  - upload 附件上传与下载
  - workflowHandlerList 选择多个用户，配置角色id。配置方式：userRoleId=角色id
  - workflowHandler 选择单个用户,配置角色id。配置方式：userRoleId=角色id
  - handlerList 选择多个用户，从全量的用户信息中获取
  - handler 选择一个用户，从全量的用户信息获取
  - codeSelect 获取对应枚举表的枚举，生成下拉框,只读属性有效。参数说明，selectId 枚举id;default 默认值;defaultvari 从流程变量获取默认值
  - stringDate yyyy-MM-dd 格式的日期，不选日期有默认汉字输出
  - systemText 获取只能后端生成的一些内容（如：流水号），根据id去查询queryIdentyTextContent接口
  - variableText 获取流程里面的变量填写输入框，根据id去获取的。无id使用variable配置对应值
  - variableTextArea 获取流程里面的变量填写输入框，根据id去获取的。无id使用variable配置对应值
  - variableDate 获取流程的变量，按照日期-时间格式来展示
  - modelDown 模板文件下载，value 存储文件名,调用 downModelAttachList 接口
  - attachDown 附件下载，附件名从流程变量attachList获取，调用获取附件downAttachList接口
  - textarea 多行文本框，rows 控制行数。variableText-area 反显
  - processAllFile 获取流程附件，后端根据id:attachSelects去获取
  - selectByVariable 根据变量variable的值获取，下拉框的值，具体下拉框的值从后端获取
  - moreSelect 二级查询联动，查询出两级，两级混选，一个组件。selectId有值用它，无值使用id
  - deptSelect 部门选择，调用/user/getDepartmentChilds 接口查询部门。
  - deptHandler 输入部门查询用户，调用/user/getDepartmentUsers 接口查询，userNum:more选择多个用户，userNum:one选择单个用户。
* 属性说明
- showType 创建页面，开始节点的动态表单中，区分哪些是创建时展示，哪些是详情展示。通常与order连用，order进行排序
  取值： 2,创建与详情都展示；0,只详情展示；1,只创建展示。
- syncFlag 通常在开始节点的动态表单，配置工单同步信息所需的属性，通常值为 对应SyncDataType
- dispatch 创建页面，配置要派发出去的参数信息内容
- selectId 下拉框对应的枚举id
- default 默认值
- userRoleId 工单处理的角色id
- replyFlag=1, acceptType=3时显示的操作

## 开发说明
* 工单信息同步接口 （SyncData） ExtIdentylogList一些信息生成规则
  - 环节类型（PhaseType） 流程节点最后带”审批“ 视为审批节点，其余视为处理节点
  - 审核结果（ApprovalResults）审批节点受理方式 id:passFlag, '1'、通过，'2'、驳回，'3' 下一步审批人
* 集团派发工单，最终回到派发人
  - 在派发节点配置complete事件，实现方法：com.asiainfo.sound.service.flow.MesgSaveEventListener。
    最终处理节点配置用户${actUserId}






基础服务平台，就是端到端配置
登录基础服务平台，https://117.134.46.40:1120/
helan/HeLa@1009 测试和生产用的是同一套，修改时注意下。

网状网生产qq群：
45903302
基础服务平台联调群：
498867647
13811034101





## 其他业务知识点

管理员用户表
identy_user_role

sound_dim_identy_common_code


删除工单
-- 运行时任务节点表核心表 ID_为taskId
SELECT *   from ACT_RU_TASK WHERE PROC_INST_ID_='09cb4138-00fb-11ed-9697-fa94c2fa8a20';
-- 运行时流程执行实例表核心表 PROC_INST_ID_
SELECT * from ACT_RU_EXECUTION WHERE PROC_INST_ID_='09cb4138-00fb-11ed-9697-fa94c2fa8a20';


--查看数据库大小
select table_rows,t.DATA_LENGTH, table_name ,table_schema from information_schema.tables t order by t.table_rows desc limit 10;

--统计登录用户
select left(oper_param,locate(' ',oper_param)-1) ,count(left(oper_param,locate(' ',oper_param)-1)) from sys_oper_log where (method like '%postfromfoura%' or oper_url like '%postfromfoura%') and oper_time between  '2022-01-01' and  '2022-08-02'   group by left(oper_param,locate(' ',oper_param)-1);
SELECT oper_name,DATE_FORMAT( oper_time, '%Y-%m' ),count( 1 ) num  FROM `sys_oper_log` where oper_name is not null GROUP BY oper_name, DATE_FORMAT( oper_time, '%Y-%m' ) order by num desc;

--网状网权限验证curl(测试环境)
curl -i -X POST \
-H "Content-Type:application/x-www-form-urlencoded" \
-d \
'appId=CSVB9510&appSecret=qSi4hwZdg7' \
'http://10.254.123.224:8096/api/token/authinternal'


--网状网权限验证curl(正式环境1)
curl -i -X POST \
-H "Content-Type:application/x-www-form-urlencoded" \
-d \
'appId=CSVB9510&appSecret=qSi4hwZdg7' \
'http://10.250.78.73:2282/api/token/authinternal'
