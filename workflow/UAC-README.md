r### 宁夏大音UAT环境使用说明
整体情况：
- UAT和生产共用服务器，共用nginx
- 工作流数据库和生产环境分开（通过指定profile来指定配置，可以将数据库切为生产）
- 门户数据库和生产环境分开（通过指定profile来指定配置，可以将数据库切为生产）
#### 地址
- 门户 : http://**************:18080/nxportal/portal/login/getfromfoura
- 工作流 :见readme
#### 更新说明
- nginx
  - 服务器IP：*************
  - 配置文件路径：/home/<USER>/nginx/conf/gray.conf
- 前端服务器(和生产环境公用）IP：*************
  - 门户路径：/usr/share/nginx/html/gray/workflow
    - 更新方式：直接替换
  - 工作流路径：
    - 更新方式：直接替换
- 后端服务器(和生产环境公用）IP：*************
  - 门户
    - 路径 /home/<USER>/gray/portal
    - 更新方式：替换文件后执行启动脚本
    - 更新需替换的文件
      - unified-portal-nx-gray.jar
    - 启动脚本:
      - 路径 /home/<USER>/deploy-gray-portal.sh
      - 执行参数 无
  - 工作流路径
    - 路径 /home/<USER>/gray/workflow
    - 更新方式：替换文件后执行启动脚本
    - 更新需替换的文件
      - app.jar
    - 启动脚本:
      - 路径 /home/<USER>/deploy-gray-workflow.sh
      - 执行参数 无
- 数据库
  - 登录脚本路径：xxxx
  - UAT数据库：xxx_gray