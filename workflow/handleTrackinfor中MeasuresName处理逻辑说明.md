# handleTrackinfor中MeasuresName处理逻辑说明

## 概述

在 `WorkOrderTrackinforServiceImpl.handleTrackinfor` 方法中新增了 `MeasuresName` 字段的处理逻辑，用于处理智能研判预警单（0024）的特殊参数需求。

## 功能说明

### 1. 触发条件

当满足以下条件时，会自动添加 `MeasuresName` 参数：
- 工单子类型为智能研判预警单（`IdentySubtype.warning_24.getValue()` = "0024"）
- `ReplyCssReq` 对象的 `ParaList` 中不存在 `MeasuresName` 参数

### 2. 处理流程

1. **工单子类型检查**：
   - 通过 `identyInfoDao.getIdentySubType(identifier)` 查询工单的子类型
   - 判断是否为智能研判预警单（0024）

2. **参数检查**：
   - 检查 `cssReq.getParaList()` 中是否已存在 `ParaID` 为 "MeasuresName" 的参数
   - 如果不存在，则进行下一步处理

3. **附件名称提取**：
   - 调用 `extractMeasuresNameFromTrackingInfo()` 方法提取附件名称
   - 按优先级顺序获取附件名称

4. **参数添加**：
   - 创建新的 `ParaList` 对象，设置 `ParaID` 为 "MeasuresName"
   - 设置 `ParaVal` 为提取到的附件名称
   - 添加到 `cssReq.getParaList()` 中

### 3. 附件名称提取逻辑

`extractMeasuresNameFromTrackingInfo()` 方法按以下优先级提取附件名称：

#### 优先级1：从 cssReq 中获取
- `cssReq.getAttachNameList()`：附件实际名称
- `cssReq.getAttachList()`：附件文件名

#### 优先级2：从轨迹记录中获取
- 查询工单的所有轨迹记录：`orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier)`
- 从最新的轨迹记录开始，依次查找：
  - `record.getAttachNameList()`：附件实际名称
  - `record.getAttachList()`：附件文件名

#### 优先级3：从 ParaList 中获取
- 查找 `ParaID` 为以下值的参数：
  - "AttachmentName"
  - "FileName"
  - "DocumentName"

#### 附件名称处理
1. **多文件处理**：如果附件名称包含多个文件（用 `|` 分隔），取第一个作为整改目标附件名称
2. **路径处理**：如果是文件路径，提取文件名部分

## 代码实现

### 主要方法

#### 1. handleMeasuresNameForTrackinfor()
```java
private void handleMeasuresNameForTrackinfor(ReplyCssReq cssReq)
```
- **功能**：处理轨迹信息上报中的MeasuresName逻辑
- **参数**：`cssReq` - 工单回复请求对象
- **调用位置**：`handleTrackinfor()` 方法开始处理时

#### 2. extractMeasuresNameFromTrackingInfo()
```java
private String extractMeasuresNameFromTrackingInfo(String identifier, ReplyCssReq cssReq)
```
- **功能**：从轨迹信息中提取整改目标附件名称
- **参数**：
  - `identifier` - 工单编号
  - `cssReq` - 工单回复请求对象
- **返回值**：提取到的附件名称，如果没有找到则返回空字符串

### 调用流程

```java
@Override
public void handleTrackinfor(ReplyCssReq cssReq, OrderTrackTaskLog trackTaskLog) {
    log.info("工单轨迹信息上报处理开始");
    String identifier = null;
    try {
        if (cssReq == null) {
            throw new RuntimeException("cssReq为空");
        }
        identifier = cssReq.getIdentifier();
        
        // 处理MeasuresName逻辑
        handleMeasuresNameForTrackinfor(cssReq);
        
        // ... 原有的轨迹信息上报逻辑
    } catch (Exception e) {
        // ... 异常处理
    }
}
```

## 日志记录

### 正常处理日志
- `"处理智能研判预警单(0024)工单轨迹信息上报，工单号：{}"`
- `"为智能研判预警单(0024)轨迹信息上报添加MeasuresName参数，工单号：{}，附件名称：{}"`
- `"从cssReq.attachNameList获取到附件名称：{}"`
- `"从轨迹记录attachNameList获取到附件名称：{}"`
- `"从ParaList参数{}获取到附件名称：{}"`
- `"从多个附件中选择第一个作为整改目标附件名称：{}"`
- `"从文件路径中提取文件名：{}"`

### 异常处理日志
- `"处理智能研判预警单(0024)轨迹信息上报特殊参数时发生异常，工单号：{}，异常信息：{}"`
- `"从轨迹信息中提取附件名称时发生异常，工单号：{}，异常信息：{}"`

## 异常处理

### 异常处理策略
- 所有异常都会被捕获并记录日志
- **不会抛出异常**，避免影响正常的轨迹信息上报流程
- 发生异常时返回空字符串作为附件名称

### 容错机制
1. **空值检查**：对所有可能为空的对象进行检查
2. **集合检查**：使用 `CollectionUtils.isNotEmpty()` 检查集合
3. **字符串检查**：使用 `StringUtils.isNotBlank()` 检查字符串
4. **异常隔离**：异常不会影响主流程的执行

## 依赖关系

### 新增导入
```java
import com.asiainfo.sound.domain.enums.IdentySubtype;
import com.asiainfo.sound.domain.req.ParaList;
```

### 使用的服务和DAO
- `identyInfoDao`：查询工单子类型
- `orderTrackRecordMapper`：查询轨迹记录

## 测试建议

### 测试场景

1. **正常场景**：
   - 智能研判预警单（0024）工单轨迹信息上报
   - 存在附件信息的情况

2. **边界场景**：
   - 非智能研判预警单的工单
   - 已存在MeasuresName参数的情况
   - 没有附件信息的情况

3. **异常场景**：
   - 工单编号为空
   - 数据库查询异常
   - 轨迹记录为空

### 验证点

1. **参数添加验证**：
   - 检查 `ParaList` 中是否正确添加了 `MeasuresName` 参数
   - 验证参数值是否正确

2. **日志验证**：
   - 检查相关日志是否正确输出
   - 验证异常日志是否记录

3. **流程验证**：
   - 确认不影响原有的轨迹信息上报流程
   - 验证异常情况下的容错处理

## 注意事项

1. **性能影响**：新增的处理逻辑会增加少量的数据库查询和处理时间
2. **数据一致性**：确保附件名称的提取逻辑与业务需求一致
3. **向后兼容**：不影响现有的轨迹信息上报功能
4. **扩展性**：如果需要支持其他工单类型，可以扩展条件判断逻辑

## 相关文件

- `WorkOrderTrackinforServiceImpl.java`：主要实现文件
- `IdentySubtype.java`：工单子类型枚举
- `ParaList.java`：参数列表实体类
- `ReplyCssReq.java`：工单回复请求类
- `OrderTrackRecord.java`：轨迹记录实体类
