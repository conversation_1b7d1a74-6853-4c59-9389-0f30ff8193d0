#!/bin/sh

if [ "$2" == 'ui' ]; then
  context="ui"
  mkdir -p $1/${context}
  cp ${context}/Dockerfile $1/${context}
  cp -r ${context}/dist $1/${context}/
  mv ${context}/../mobile/dist $1/${context}/distmobile
  cp -r ${context}/default.conf $1/${context}/
else
  context="."
  mkdir -p $1/${context}/target
  cp ${context}/Dockerfile $1/${context}
  cp ${context}/target/*.jar $1/${context}/target
  cp -r ${context}/target/resources $1/${context}/target
  cp -r ${context}/target/lib $1/${context}/target
fi



cp build-and-push.sh $1/

cp clean-docker-in-docker.sh $1/

cp kubernetes.yaml $1/
