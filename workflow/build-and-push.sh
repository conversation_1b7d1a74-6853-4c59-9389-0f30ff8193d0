#!/bin/sh

version="$1"
if [[ -z $version ]]; then
    version="v1.0";
fi


docker build -t docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-client:basenx-workflow-$version ui
docker push docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-client:basenx-workflow-$version


docker build -t docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-server:basenx-workflow-$version .
docker push docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-server:basenx-workflow-$version