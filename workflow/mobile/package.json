{"name": "mobile-template", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:stage": "vue-cli-service build", "demo": "vue-cli-service build --report", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "clipboard": "^2.0.11", "core-js": "^3.6.4", "echarts": "^5.3.1", "element-ui": "^2.15.13", "js-cookie": "^3.0.5", "lib-flexible": "^0.3.2", "moment": "^2.29.4", "postcss-px2rem-exclude": "^0.0.6", "vant": "^2.12.5", "vue": "^2.6.11", "vue-awesome-swiper": "^3.1.3", "vue-class-component": "^7.2.3", "vue-property-decorator": "^9.1.2", "vue-router": "^3.1.6", "vuex": "^3.1.3"}, "devDependencies": {"@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1", "@types/chai": "^4.2.11", "@types/mocha": "^5.2.4", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-plugin-typescript": "~4.5.13", "@vue/cli-plugin-unit-mocha": "^4.3.0", "@vue/cli-service": "^4.3.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "1.0.0-beta.31", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "chai": "^4.1.2", "chokidar": "^3.3.1", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "husky": "^4.0.0", "lint-staged": "^10.5.4", "node-sass": "npm:sass@^1.58.2", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.2.1", "sass-loader": "^8.0.2", "terser-webpack-plugin": "^2.3.5", "typescript": "~4.1.5", "vue-template-compiler": "^2.6.11"}, "lint-staged": {"src/**/*.{vue,js,json,css,md,ts,tsx}": ["prettier --write", "eslint --fix", "git add"]}}