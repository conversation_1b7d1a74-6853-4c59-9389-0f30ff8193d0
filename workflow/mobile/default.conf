server {

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /bjjf-server {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    location /bjjf-service {
        client_max_body_size 500m;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        include uwsgi_params;
        proxy_pass http://127.0.0.1:8080;
        #生产打包
        #proxy_pass http://svc-cloud.ns-fim:8080;
    }
    # location /api-baidu {
    #     proxy_pass https://api.map.baidu.com/place/v2/search;
    # }
}


