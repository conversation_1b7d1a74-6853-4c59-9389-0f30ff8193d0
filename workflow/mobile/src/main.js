import Vue from 'vue';
import 'lib-flexible/flexible';
import App from './App.vue';
import * as echarts from 'echarts';
// 注册全局组件
import '@/components/index.js';
import VueAwesomeSwiper from 'vue-awesome-swiper';
import {parseTime} from '@/utils/index.js';
Vue.use(VueAwesomeSwiper /* { 全局组件的默认选项 } */);
// ***********************************保留2位小数针对本项目做的补丁********************

function isInt(n) {
  if (n && typeof n === 'string') {
    n = Number(n);
    return Number(n) === n && n % 1 === 0;
  }
  return Number(n) === n && n % 1 === 0;
}
Number.prototype.toFixed2 = function() {
  // 保留2
  // console.log('this:', this)
  // console.log('arguments[0]:', arguments[0])

  if (this === 0 || this === 0.00 || this == 0.0) {
    return 0;
  }

  if (isNaN(this)) {
    return '';
  }

  if (arguments[0] == 2 && !isNaN(this)) {
    if (!isInt(this)) { // 不是整数
      const y = String(this).indexOf('.') + 1;// 获取小数点的位置
      const count = String(this).length - y;// 获取小数点后的个数

      if (count >= 2) { // 2位小数
        if (this < 0 && this > -0.005) {
          return -0.01;
        }
        if (this > 0 && this < 0.005) {
          return 0.01;
        }
        return Number.prototype.toFixed.call(this, 2);
      }
      if (count == 1) { // 1 位小数
        return this;
      }
    } else {
      return this;
    }
  }
};
Vue.prototype.parseTime = parseTime;
import {
  Popup,
  Progress,
  Form,
  Field,
  Empty,
  Picker,
  Toast,
  Dialog,
  Checkbox,
  CheckboxGroup,
  Cell,
  CellGroup,
  Collapse,
  CollapseItem,
  DatetimePicker,
  Cascader,
  Icon,
  NavBar,
  Skeleton,
  ActionSheet,
  Col, Row,
  Search,
  List,
  Pagination 
} from 'vant';
Vue.use(Popup)
  .use(Progress)
  .use(Form)
  .use(Field)
  .use(Picker)
  .use(Empty)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Cell)
  .use(CellGroup)
  .use(Collapse)
  .use(CollapseItem)
  .use(DatetimePicker)
  .use(Cascader)
  .use(Icon)
  .use(NavBar)
  .use(Skeleton)
  .use(ActionSheet)
  .use(Col)
  .use(Row)
  .use(Search)
  .use(List)
  .use(Pagination);
import '@/assets/common.scss';
// 全局过滤器
import filter from '@/utils/filter';
for (const key in filter) {
  Vue.filter(key, filter[key]);
}
// Vue.prototype.$echarts = echarts;

const moment = require('moment');
Vue.prototype.$moment = moment;
Vue.prototype.$echarts = echarts;

// 静态配置路由
import router from './router/staticRouter';
// 自动配置路由-根据页面关系配置
// import router from './router/autoRouter';
import store from './store';
// 全局注册
Vue.use(Dialog).use(Toast);
Vue.prototype.$Toast = Toast;
// Toast.setDefaultOptions({
//   position: 'bottom',
// });

// import 'vant/lib/index.css';
import ElementUI, {Message, MessageBox} from 'element-ui';
Vue.prototype.$message = Message;
Vue.prototype.$confirm = MessageBox.confirm;

Vue.use(ElementUI);
// 自动注册公共组件
import './components/index';


Vue.config.productionTip = false;
Vue.prototype.$EventBus = new Vue();



new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
});