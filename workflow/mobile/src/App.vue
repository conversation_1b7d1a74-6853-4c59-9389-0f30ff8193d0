<template>
  <div id="app" class="">
    <div><router-view></router-view></div>
  </div>
</template>
<script>
export default {
  provide: () => ({
    mapPermission: {'cityId':'640000','cityid':'640000','name':'宁夏','mapLevel':1,'parentCityId':'0','cityId2':'95','name2':'宁夏','mapLevel2':1,'parentCityId2':'0'}
  }),
  data() {
    return {};
  },
  mounted() {
    // this.$store.commit('getLabelData',JSON.parse(sessionStorage.getItem('allCode')));
  },
  methods: {},
};
</script>

<style lang="scss">

* {
  margin: 0;
  padding: 0;
}
.van-picker__toolbar {
  > .van-picker__confirm {
    color: #4a69ff;
  }
}

.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #4a69ff;
}
.van-nav-bar {
  background: #2f94f9;
  .van-icon {
    color: #fff;
  }
  .van-nav-bar__title {
    color: #fff;
    font-weight: 700;
  }
}
</style>
