<template>
  <div class="home_wrap" style="padding-bottom: 42px;">
    <header>
      {{ title }}
      <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')" />
    </header>
    <div class="my-index-content">
      <div class="search-content">
        <van-search v-model="myIndexName" placeholder="请输入搜索关键词" style="width: 280px;" />
        <div class="search-btn" @click="search">搜索</div>
      </div>
      <div class="search-list">
        <p>热门搜索词</p>
        <div class="list-content">
          <div class="list-item" v-for="(item, index) in searchList" :key="index" @click="selectQuery(item)">{{ item }}
          </div>
        </div>
      </div>
    </div>
    <div class="map-area">
      <div class="charts-title"><span class="split-line"></span>指标地图</div>
      <img src="../../../assets/images/map.png" alt="">
    </div>
    <div class="index-list">
      <div class="charts-title"><span class="split-line"></span>收藏指标</div>
      <div v-for="item in resultList" :key="item.id" class="index-item">
        <p>指标名称：{{ item.indexName }}</p>
        <p class="time-text">创建时间：{{ item.indexTime }}</p>
        <span class="collect" @click="collect(item)">取消收藏</span>
      </div>
      <van-pagination v-model="currentPage" :total-items="indexList.length" :items-per-page="8" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      title: '我的指标',
      myIndexName: '',
      searchList: ['家宽满意度', '移动业务满意度', '固定上网满意度', '语音通话满意度', '资费满意度'],
      indexList: [
        {
          id: 1,
          indexName: '家宽满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 2,
          indexName: '移动业务满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 3,
          indexName: '固定上网满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 4,
          indexName: '语音通话满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 5,
          indexName: '投诉满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 6,
          indexName: '移机满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 7,
          indexName: '配送服务满意度',
          indexTime: '2024-12-23',
          status: true
        },
        {
          id: 8,
          indexName: '资费满意度',
          indexTime: '2024-12-23',
          status: true
        },
      ],
      resultList: [],
      currentPage: 1
    };
  },

  mounted() {
    this.resultList = this.indexList
  },

  methods: {
    selectQuery(name) {
      this.myIndexName = name
    },
    search() {
      this.resultList = this.indexList.filter(item =>
        item.indexName.toLowerCase().includes(this.myIndexName)
      );
    },
    collect(data) {
      let index = this.indexList.findIndex(item => item.id == data.id)
      this.indexList.splice(index, 1)
      this.resultList = this.indexList
    }
  },
  watch: {
    myIndexName(a, b) {
      if (!a) {
        this.resultList = this.indexList
      }
    }
  }
};
</script>

<style lang="scss" scoped>
$titleColor: #262626;
$borderColor: #e6e6e6;
$border: none;

.my-index-content {
  background-color: #fff;
  padding: 16px 0 16px 16px;

  .search-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

    .search-btn {
      width: 50px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background-color: #FE9900;
      border-radius: 10px;
      color: #fff;
    }
  }

  .search-list {
    padding: 0 0 0 20px;
    color: #bbb;

    .list-content {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      .list-item {
        padding: 0 5px;
        width: 60px;
        height: 20px;
        background: #ccc;
        text-align: center;
        line-height: 20px;
        color: #fff;
        border-radius: 10px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}

.map-area {
  background-color: #fff;
  margin-top: 10px;
  padding: 16px 0 16px 16px;
  .charts-title {
    margin-bottom: 16px;
    line-height: 24px;
    font-weight: 500;
    color: $titleColor;
    font-size: 16px;
    border-bottom: $border;
  }

  .split-line {
    display: inline-block;
    margin-top: 1px;
    width: 2.14px;
    height: 14px;
    margin-right: 8.5px;
    background-color: #FE9900;
  }
}

.index-list {
  margin-top: 10px;
  background-color: #fff;
  padding: 20px;

  .charts-title {
    margin-bottom: 16px;
    line-height: 24px;
    font-weight: 500;
    color: $titleColor;
    font-size: 16px;
    border-bottom: $border;
  }

  .split-line {
    display: inline-block;
    margin-top: 1px;
    width: 2.14px;
    height: 14px;
    margin-right: 8.5px;
    background-color: #FE9900;
  }

  .index-item {
    margin: 20px auto;
    height: 90px;
    width: 300px;
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    color: #555;
    box-shadow: -4px 0px 6px -1px rgba(0, 0, 0, 0.2),
      0px 2px 4px -1px rgba(0, 0, 0, 0.08);

    .time-text {
      margin-top: 10px;
    }

    .collect {
      position: absolute;
      top: 36px;
      right: 20px;
      color: #FE9900;
    }
  }
}
</style>
<style>
.van-pagination__item--active {
  background: #FE9900;
}
</style>