<template>
  <div ref="chart" class="charts" />
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'LineCharts',
  props: {
    data: Array,
    // 设置dataZoomStyle里面的end属性，不为0显示dataZoom
    dataZoomEnd: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      chart: null,
      dataZoomStyle: {
        show: true,
        type: 'slider',
        end: 40,
        handleSize: 0, // 滑动条的 左右2个滑动条的大小
        height: 8, // 组件高度
        left: 0, // 左边的距离
        right: 0, // 右边的距离
        bottom: 10, // 右边的距离
        handleColor: '#eee', // h滑动图标的颜色
        handleStyle: {
          borderColor: '#eee',
          borderWidth: '1',
          shadowBlur: 2,
          background: '#eee',
          shadowColor: '#eee',
        },
        backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
        showDataShadow: false, // 是否显示数据阴影 默认auto
        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        handleIcon:
          'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
        // filterMode: "filter",
        moveHandleStyle: {color: '#eee'},
      },
    };
  },
  computed: {
    xAxisData() {
      const arr = [];
      this.data.forEach((item) => {
        arr.push(item.statDate);
      });
      return arr;
    },
    yAxisData() {
      const arr = [];
      this.data.forEach((item) => {
        arr.push(item.score);
      });
      return arr;
    },
    maxNum() {
      let max = 0;
      this.data.forEach((item, index) => {
        if (index == 0) {
          max = Math.ceil(item.score);
        }
        if (item.score > max) {
          max = Math.ceil(item.score);
        }

        max = Math.ceil(max / 2) * 2 > 100 ? 100 : Math.ceil(max / 2) * 2;
      });
      return max;
    },
    minNum() {
      let min = 0;
      this.data.forEach((item, index) => {
        if (index == 0) {
          min = Math.floor(item.score);
        }
        if (item.score < min) {
          min = Math.floor(item.score);
        }

        min = parseInt(min / 2) * 2;
      });
      return min;
    }
  },
  watch: {
    data() {
      this.initChart();
    }
  },
  created() {},
  mounted() {
    this.initChart();
  },
  methods: {
    // eslint-disable-next-line camelcase
    insert_flg(str, flg, sn) {
      let newstr = '';
      let len = 0;
      len = str.length;
      for (let i = 0; i < len; i += sn) {
        const tmp = str.substring(i, i + sn);
        newstr += tmp + flg;
      }

      return newstr;
    },

    initChart() {
      if (
        this.chart !== null &&
        this.chart !== '' &&
        this.chart !== undefined
      ) {
        this.chart.dispose();
      }
      this.chart = echarts.init(this.$refs.chart, null, {
        renderer: 'svg'
      });
      this.updateOption();
      window.addEventListener('resize', () => {
        this.chart.resize();
      });
    },

    updateOption() {
      const option = {
        // title: {
        //   text: '满意度变化趋势图',
        //   show: true,
        //   textStyle: {
        //     fontSize: 15,
        //     fontWeight: 'normal',
        //     lineHeight: 40,
        //     color: '#6B6B6B',
        //     fontFamily: 'SourceHanSansSC-Regular'
        //   },
        //   left: -5,
        //   top: -15,
        //   subtext: '单位：分',
        //   subtextStyle: {
        //     fontWeight: 'normal',
        //     color: 'rgb(140,140,140)',
        //     fontSize: 13,
        //     lineHeight: 1,
        //     fontFamily: 'SourceHanSansSC-Normal'
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          formatter(params) {
            return (
              `${params[0].name}<br />${params[0].marker}${params[0].value}`
            );
          }
        },
        dataZoom: this.dataZoomEnd ?
          [{...this.dataZoomStyle, end: this.dataZoomEnd}]
          : [],
        legend: {
          data: ['满意度'],
          right: 30,
          top: 25,
          symbol: 'react',
          itemWidth: 13.68,
          itemHeight: 2,
          textStyle:{
            fontFamily:' Source Han Sans SC',
            fontSize: 12,
            fontWeight: 400,
            color: '#8C8C8C',
          }
        },
        grid: {
          left: 0,
          right: 0,
          top: 70,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          axisTick: {
            show: false,
            alignWithLabel: true
          },
          axisLabel: {
            interval: 0,
            color: 'rgb(157, 157, 157)',
            lineHeight: 12,
            formatter: (value) => value
            // value.slice(5, 7) > 9
            //   ? value.slice(5, 7) + '月'
            //   : value.slice(6, 7) + '月'
          }
        },
        yAxis: {
          type: 'value',
          // max:this.maxNum,
          max: this.maxNum - this.minNum > 8 ? this.maxNum : this.minNum + 8,
          // min:(this.maxNum - this.minNum > 8) ? this.minNum : ((this.maxNum - 8) >= 0 ? (this.maxNum - 8) : 0),
          min: this.minNum,
          minInterval: 2,
          boundaryGap: ['100%', '100%'],
          splitLine: {
            lineStyle: {
              color: '#ccc',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '满意度',
            data: this.yAxisData,
            type: 'line',
            smooth: true,
            symbol: 'none',
            itemStyle: {
              color: 'rgb(246, 175, 56)'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(246, 175, 56)'
                },
                {
                  offset: 1,
                  color: 'rgb(255, 255, 255)'
                }
              ])
            },
            symbolSize: 9,
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      };
      this.chart.clear();
      this.chart.setOption(option, true);
    }
  }
};
</script>

<style lang="scss" scoped>
// .bar {
// 	// width:100%;
// 	height:100%;
// }
</style>
