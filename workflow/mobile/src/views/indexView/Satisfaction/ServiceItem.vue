<template>
  <div class="service-item" v-loading="loading">
    <div class="service-header">
      <div>
        <!-- <div class="service-header-tab">
          <span v-for="item in tabs" :key="item.targetId" @click="changeTab(item)" :class="['tab-items', item.targetId === targetId && 'tab-checked']">
            {{ item.targetName }}
          </span>
        </div> -->
      </div>
    </div>
    <div class="service-box">
      <serve-detail ref="serve-detail" @changeDate="changeDate" @updatelocal="updatelocal"></serve-detail>
    </div>
  </div>
</template>

<script>
import ajaxRequest from '@/api/satisfaction/index.js';
import serveDetail from './serveDetail.vue';
import {obj as newDataEmpty} from './demo.js';
export default {
  name: 'ServiceItem',
  components: {
    serveDetail
  },
  inject: ['mapPermission'],
  data() {
    return {
      dateTypes: {
        日: '1',
        周: '2',
        月: '3',
        季度: '4',
        日累计: '5'
      },
      defaultType: '月',
 
  
      targets: [],
      /**
       * 101 3ZUcYVPR 手机客户-
       * 102 HmGzfYsO 家宽客户
       */
      restSerialNo: {
        '101': '3ZUcYVPR',
        '102': 'HmGzfYsO'
      },
      loading: false,
      tabs: [],
      targetId: '10404',
      filterId: '3',
      parentId: '',
    };
  },
  created() {},
  methods: {
    // 获取指标
    async getTargets(filterId = '3',localName = null) {
      const [statType, statDate] = this.date;
      try {
        this.loading = true;
        const {code, data} = await ajaxRequest('nPiF0fnE', {
          statType: this.dateTypes[statType],
          statDate,
          type: 1
        });
        if (code == 200 && data) {
          const arr = data
            .map((item) => ({
              menuName: item.targetName,
              value: item.targetId,
              ...item
            }))
            .filter((item) => 
              // 区县用户只有自测满意度有权限
              this.mapPermission.mapLevel == 3
                ? item.value == '1'
                : true
            );
          // 工信部满意度数据 3、自测满意度 1
          this.targets = arr.filter((item) => item.targetId === filterId);
          const [{childs}] = this.targets;
          if (filterId === '3') {
            this.tabs = childs;
            this.setChildTarget(childs,localName);
          } else {
            this.tabs = childs.filter(
              (item) => item.targetId !== '103' || item.targetName !== '政企客户'
            ); // 筛选掉  政企客户;
          }
          console.log('>>> 1111 >>>\n', this.targets, '\n', this.tabs);
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
    setChildTarget(childs,localName = null) {
      const [statType, statDate] = this.date;
      const current = childs.find((item) => item.targetId == this.targetId) || {};
   
      // 兼容移动端特殊处理(总体感知处理)
      if(current && current.childs.length == 0 && current.targetName == '总体感知') {
        current.childs = newDataEmpty;
      }
      // 局部更新
      if(localName) {
        current.childs = current.childs.filter((item) => item.targetname === localName);
      }
      this.$refs['serve-detail'].dataItems = {...current, time: statDate};
      this.$refs['serve-detail'].activeName = this.dateTypes[statType];
    },
    // 获取下级指标
    async getSubTarget(type, targetId, localName) {
      try {
        const [statType, statDate] = this.date;
        const {data} = await ajaxRequest(this.restSerialNo[targetId], {
          statType: this.dateTypes[statType],
          statDate,
          type
        });
        console.log('>>> 1111 >>>> 获取下级指标 \n', data);
        const current = this.tabs.find((item) => item.targetId == targetId) || {};
        // 局部更新
        let result = data;
        if(localName) {
          result = data.filter((item) => item.targetname === localName);
        }
        // 下面的四个分类在父级的id是不同的， 只能按照名字来分，且后续只能是这四个
        this.$nextTick(() => {
         
          this.$refs['serve-detail'].dataItems = {...current, childs: result,time: statDate};
          this.$refs['serve-detail'].activeName = this.dateTypes[statType];
        });
      } catch (error) {
        console.log(error);
      }
    },
    async toggleTargetData(dateAry, childId) {
      const tabChildId = childId === 'KPI0101' ? '3' : '1';
      this.filterId = tabChildId;
      const [type, date] = dateAry;
      this.date = dateAry;
      this.defaultDate = {[type]: date.replace('-Q', '-0')};
      this.defaultType = type;
      await this.getTargets(tabChildId);
      const [{targetId}] = this.tabs;
      this.targetId = targetId;
      if (this.filterId !== '3') {
        this.parentId = childId;
        // const type = childId === 'KPI0102' ? 3 : 2;
        const type = childId === 'KPI0103' ? 3 : 2;
        this.getSubTarget(type, targetId);
      }
    },
    // 服务分项各类切换
    async changeTab(item,localName = null) {
      const {targetId} = item;
      console.log('>>> 1111 >>> 服务分项各类切换\n', item.targetName, targetId);
      this.targetId = targetId;
      await this.getTargets(this.filterId,localName);
      if (this.filterId !== '3') {
        const type = this.parentId === 'KPI0103' ? 3 : 2;
        this.getSubTarget(type, targetId,localName);
      }
    },
    changeDate(date) {
      this.date = date;
      const type = this.parentId === 'KPI0103' ? 3 : 2;
      this.getSubTarget(type, this.targetId);
    },
    updatelocal(ids) {
      console.log(ids);
      this.changeTab({targetId: ids.tabTargetId},ids.chartsName);
    }
  }
};
</script>

<style lang="scss" scoped>
.service-item {
  .service-header {
    .service-header-title {
      color: #262626;
      font-size: 30px;
      line-height: 38px;
      text-align: center;
    }
    .service-header-tab {
      color: #4D4D4D;
      margin-left: 16px;
      margin-bottom: 12px;
      display: inline-block;
      background: #ffffff;
      border: 1px solid #E6E6E6;
      border-radius: 6px;
      > span {
        line-height: 24px;
        font-size: 14px;
        color: #262626;
      }
      .tab-items {
        border-right: 1px solid #eeeeef;
        display: inline-block;
        width: 80px;
        text-align: center;
        
      }
      .tab-checked {
        background: #ff9900;
        color: #ffffff;
        border-radius: 4px;
        border-right: none;
      }
    }

    .service-date {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .service-box {
    width: 100%;
    display: flex;
    .service-detail {
      width: 100%;

      .service-detail-item {
        width: 100%;
        min-height: 60px;
        padding: 20px;
        box-sizing: border-box;
        background-color: #ffffff;
        margin-bottom: 24px;
      }

      .service-detail-header {
        cursor: pointer;
        width: 100%;
        display: flex;
        align-items: center;
        padding-right: 20px;

        &::before {
          content: ' ';
          display: inline-block;
          width: 23px;
          height: 24px;
          margin-right: 5px;
          background-image: url('../../../assets/icon/fuhao.png');
          background-repeat: no-repeat;
          background-size: 100%;
          // transform: rotate(90deg);
          transition: all ease 0.3s;
        }
        .service-detail-title {
          font-size: 20px;
          font-weight: 500;
          text-align: left;
          color: #262626;
          margin-right: 20px;
        }
        .active-sub-title {
          color: #ff9900;
        }

        .sub-title-disabled {
          cursor: not-allowed !important;
          color: #999;
          pointer-events: none;
        }

        &.toggles {
          &::before {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
}
</style>
