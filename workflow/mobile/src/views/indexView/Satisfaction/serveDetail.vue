<template>
  <div class="serve-detail">
    <div class="content">
      <div class="time-select">
          <DatePicker @change="handleDateChange" :default-date="defaultDate" :date-types="dateTypes"></DatePicker>
      </div> 



      <!-- <el-date-picker style="width: 236px" :clearable="false" 
          v-if="activeName == 1" v-model="selectedDate" :picker-options="pickerOptionsDate" type="date" value-format="yyyy-MM-dd" format="yyyy年MM月dd日" size="small" />
          <el-date-picker style="width: 236px" :clearable="false" 
          v-if="activeName == 2" v-model="selectedWeek" type="week" :picker-options="pickerOptionsDate1" :format="format" value-format="yyyy-MM-dd"  size="small" />
          <el-date-picker style="width: 236px" :clearable="false" 
          v-if="activeName == 3" v-model="selectedMonth" :picker-options="pickerOptionsDate" type="month" value-format="yyyy-MM" format="yyyy年MM月" size="small" />

          <el-select class="secondSelect" v-if="activeName == 4" v-model="selectedSeason" placeholder="请选择" size="small">
            <el-option v-for="item in seasonList" :key="item.name" :label="item.label" :value="item.name" />
           
          </el-select>
      <el-button class="inquire" @click="changeTime" size="small">查询</el-button> -->

      <div class="main-content">
        <template v-if="allData && allData.length > 0">
          <div class="charts-content" v-for="(it) in allData" :key="it.targetId">
            <!-- 柱状图 -->
            <div v-if="it.barData.length != 0" class="cell" :class="
              it.barData.length != 0 && it.exist == '1'
                  ? 'cell-three'
                  : it.barData.length != 0 || it.exist == '1'
                    ? 'cell-two'
                    : 'cell-one'
              ">
              <div class="part-title">
                {{ it.targetname }}满意度分公司对标
              </div>


              <div class="service-tabs-detail">
                <span v-for="(tb) in $parent.tabs" :key="tb.targetId" 
                :class="[it.targetIdForTab == tb.targetId ? 'active' : '' ]"
                @click="changeTabItem(it,tb)">
                  {{ tb.targetName }}
                </span>
              </div>



              <div v-if="it.barData.length != 0" class="rank">
                <div class="rank_left_box">
                  本期排名
                  <div>
                    <span style="
                      font-size: 24px;
                      font-weight: 500;
                      margin-left: 0px;
                      margin-right: 12px;
                      color: #4D4D4D;
                    ">{{ it.rankData.rank || '-' }}</span>
                      
                        <span>环比：</span>
                        <img v-if="it.rankData.momrate != '-'&& it.rankData.momrate!=0" :src="it.rankData.momrateType == '0' ? upImg : downImg" width="8" 
                        style="margin-right: 6px; margin-left: 6px">
                        <span v-if="it.rankData.momrate == '-'">{{ it.rankData.momrate }}</span>
                        <span v-else>{{ Number(it.rankData.momrate).toFixed2(2) }}pp</span>
                  </div>
                </div>

                  <div class="rank_left_box" style="width: 30%">
                    <img v-if="showBack" src="@/assets/icon/fanhui1.png" alt="" style="margin-left: 10px; vertical-align: -4px; cursor: pointer" @click="goBackClick">
                  </div>
                  
              </div>
              <!-- <div style="height:20px" v-if="index===0"></div> -->

              <bar v-if="it.barData.length != 0" :data="it.barData" class="charts" :stat-type="activeName" :stat-date="timeValue" :target-id="it.id" :go-back="goBack" @back="back" />
              <Blank2 v-else />
            </div>
            <!-- 折线图 -->
            <div class="cell" :class="it.barData.length != 0 && it.exist == '1'
                  ? 'cell-three'
                  : it.barData.length != 0 || it.exist == '1'
                    ? 'cell-two'
                    : 'cell-one'
              ">
              <div class="part-title">{{ it.targetname }}{{ it.targetname=='10086热线客户诉求解决率'?'分析':'满意度分析' }}</div>
              <div class="service-tabs-detail" v-if="it.barData.length == 0">
                  <span v-for="(tb) in $parent.tabs" :key="tb.targetId" 
                  :class="[it.targetIdForTab == tb.targetId ? 'active' : '' ]"
                  @click="changeTabItem(it,tb)">
                    {{ tb.targetName }}
                  </span>
                </div>
              <div v-if="it.lineData.length != 0" class="rank">
                <!-- <img class="geo-icon" alt="" src="@/assets/icon/fuhao.png" /> -->
                <div class="rank_left_box">
                满意度
                <div>
                  <span style="
                    font-size: 24px;
                    font-weight: 500;
                    margin-left: 0px;
                    margin-right: 12px;
                    color: #262626;
                  ">{{
                      Number(it.scoreData.score)
                        ? Number(it.scoreData.score).toFixed2(2)
                        : it.scoreData.score
                    }}</span>
                    
                    <span>环比：</span>
                    <img v-if="it.scoreData.momrate != '-'&& it.scoreData.momrate!=0" :src="it.scoreData.momrateType == '0' ? upImg : downImg" width="8" style="margin-right: 6px; margin-left: 6px"
                      alt="">
                    <span v-if="it.scoreData.momrate == '-'">{{ it.scoreData.momrate }}</span>
                    <span v-else style="font-size: 12px;">{{ Number(it.scoreData.momrate).toFixed2(2) }}pp</span>
                </div>
              </div>

              </div>
              <div class="chart-title" v-if="it.lineData.length != 0">
                <i class="split-bar"></i>
                <span>满意度变化趋势图</span>
              </div>
              <line-charts v-if="it.lineData.length != 0" :data="it.lineData" 
              :dataZoomEnd="it.lineData.length > 6 ? Math.floor(5 / it.lineData.length * 100) : 0"
              class="charts" :active-name="activeName" />
              <Blank2 v-else style="margin: 24px 0px;"/>
            </div>
            <!-- 横向柱状图 -->
            <div v-if="it.exist == '1'" class="cell state-cell" :class="
              it.barData.length != 0 && it.exist == '1'
                  ? 'cell-three'
                  : it.barData.length != 0 || it.exist == '1'
                    ? 'cell-two'
                    : 'cell-one'
              ">
              <div class="chart-title">
                <i class="split-bar"></i>
                <span>感知要素情况</span>
              </div>
              <!-- <div class="part-title">感知要素情况</div> -->
              <div class="swiper">
                <swiper v-if="it.stateData&&it.stateData.length" ref="stateSwiper" :options="stateOptions">
                  <swiper-slide v-for="(item, index) in it.stateData" :key="index">
                    <div v-for="i in item" :key="i.targetName" class="score-cell" @click="nextLevel(i)">
                      <div v-if="i.existChild == '1'" class="score-title" style="color: #ff9900">
                        <span class="targetName">{{ i.targetName }}</span>
                      </div>
                      <div v-else class="score-title">
                        <span class="targetName">{{ i.targetName }}</span>
                      </div>
                      <div class="score-rank">
                        <div class="score">{{ i.score }}</div>
                        <div class="ratio">
                          环比：

                          <img v-if="i.momrate != '-'&& i.momrate!=0" :src="i.momrateType == '0' ? upImg : downImg" width="8" style="margin-right: 2px; margin-left: 2px">
                          <span v-if="i.momrate == '-'">{{ i.momrate }}</span>
                          <span v-else>{{ Number(i.momrate).toFixed2(2) }}pp</span>
                        </div>
                      </div>
                      <van-progress  :percentage="Number(i.score)" :show-pivot="false" v-if="i.score" stroke-width="10" :color="barColor"/>
                  
                    </div>
                  </swiper-slide>
                  <div slot="pagination" class="swiper-pagination" />
                </swiper>
                <Blank2 v-else style="height: 320px;" />
              </div>
            </div>
          </div>
        </template>
        <Blank2 v-else />
      </div>
    </div>
  </div>
</template>
<script>
import Blank2 from '@/components/global/ServiceBarLine/Blank2.vue';
import 'swiper/dist/css/swiper.css';
import {swiper, swiperSlide} from 'vue-awesome-swiper';
import Bar from './bar';
import lineCharts from './lineCharts';
import {getServeData} from '@/api/satisfaction/index.js';

export default {
  name: 'ServeDetail',
  components: {
    swiper,
    swiperSlide,
    Bar,
    lineCharts,
    Blank2
  },
  props: {},
  data() {
    return {
      upImg: require('@/assets/icon/up-icon.png'),
      downImg: require('@/assets/icon/down-icon.png'),
      dataItems: [],
      allData: [
        {
          lineData: [],
          barData: [],
          rankData: {},
          stateData: [],
          scoreData: {}
        },
        {
          lineData: [],
          barData: [],
          rankData: {},
          stateData: [],
          scoreData: {}
        },
        {
          lineData: [],
          barData: [],
          rankData: {},
          stateData: [],
          scoreData: {}
        },
        {
          lineData: [],
          barData: [],
          rankData: {},
          stateData: [],
          scoreData: {}
        }
      ],
      dataItem: {}, // JSON.parse(localStorage.getItem('detailItem')),
      timeValue: '',
      // 柱状图的swiper配置
      stateOptionsBar: {
        navigation: {
          nextEl: '.swiper-button-nextbar',
          prevEl: '.swiper-button-prevbar'
        },
        pagination: {
          el: '.swiper-pagination', // 与slot="pagination"处 class 一致
          clickable: true // 轮播按钮支持点击
        }
      },
      // 感知要素的swiper配置
      stateOptions: {
        pagination: {
          el: '.swiper-pagination', // 与slot="pagination"处 class 一致
          clickable: true // 轮播按钮支持点击
        }
      },
      barColor: '#339933',
      // 折线图的数据
      lineData: [],
      // 柱状图公司的数据
      barData: [],
      // 柱状图部门的数据
      barDataDepartment: [],
      // 感知要素数据
      stateData: [],
      preDataItem: [],
      preItem: {},
      ifInit: true,
      scoreData: {},
      rankData: {},
      // 下拉数据
      monthOptions: [
        {name: '1', label: '日'},
        // { name: "2", label: "周" },
        {name: '3', label: '月'},
        {name: '4', label: '季'}
      ],
      defaultDate:{},
      seasonList: [
        {name: 'Q1', label: '第一季度'},
        {name: 'Q2', label: '第二季度'},
        {name: 'Q3', label: '第三季度'},
        {name: 'Q4', label: '第四季度'}
      ],
      activeName: '4',
      selectedDate: '',
      selectedWeek: '',
      selectedMonth: '',
      selectedSeason: 'Q1',
      pickerOptionsDate: {
        disabledDate(time) {
          const bb = new Date();
          bb.setFullYear(bb.getFullYear() - 1);
          return time.getTime() > Date.now(); //  || time.getTime() < bb.getTime();
        }
      },
      pickerOptionsDate1: {
        firstDayOfWeek: 1,
        disabledDate(time) {
          const bb = new Date();
          bb.setFullYear(bb.getFullYear() - 1);
          return time.getTime() > Date.now() || time.getTime() < bb.getTime();
        }
      },
      format: '',
      showBack: false,
      goBack: false
    };
  },
  watch: {
    selectedWeek() {
      this.changeWeekDate(this.selectedWeek);
    },
    dataItems: {
      handler() {
        // this.dataItem = this.dataItems[0];
        // [this.dataItem]  = this.dataItems
        console.log('initTime===>', this.dataItems);
        // debugger;
        // eslint-disable-next-line prefer-const
        let {time, childs = [],targetId} = this.dataItems;
       
       

        const [first] = childs;
        this.dataItem = {...first, time};
        const obj = {
          lineData: [],
          barData: [],
          rankData: {},
          stateData: [],
          scoreData: {}
        };
        let newData = [];
        if(childs.length == 1) {
          newData = childs.map((it) => ({...it, time, targetIdForTab: targetId, ...obj}));
        }else{
          this.allData = childs.map((it) => ({...it, time, targetIdForTab: targetId, ...obj}));
        }

        this.initTime();
        // 需要处理下钻得到的时间
        this.activeName = this.activeName; // this.dataItem.mode;
        this.timeValue = time;

        const defaultType = this.monthOptions.find((item) => item.name == this.activeName);
        this.defaultDate =  {
          [defaultType.label]: this.timeValue
        };

        this.activeName == 1
          ? (this.selectedDate = time)
          : this.activeName == 2
            // eslint-disable-next-line prefer-destructuring
            ? (this.selectedWeek = time.split('|')[0])
            : this.activeName == 3
              ? (this.selectedMonth = time)
              : (this.selectedSeason = time);
        this.getData(newData.length == 1 ? newData : this.allData);
      },
      deep: true
    }
  },
  computed: {
    dateTypes() {
      return [
        {label: '日',value:'daterange'},
        {label: '月', value: 'month'},
        {label: '季度', value: 'quarter'},
      ];
    },
  },
  mounted() {},
  methods: {
    // timevalue时间  active原来逻辑时间类型
    handleDateChange(time) {
      const [type,timer] = time;
      this.timeValue = timer;
      const res = this.monthOptions.find((item) => type.indexOf(item.label) > -1);
      this.activeName = res.name;
      // 原先查询逻辑部分移植
      if (this.allData.length === 0) {
        const dateType = new Map([
          ['1', '日'],
          ['2', '周'],
          ['3', '月'],
          ['4', '季度'],
          ['5','日累计',]
        ]);
        const date = [dateType.get(this.activeName), this.timeValue];
        console.log(date);
        this.$emit('changeDate', date);
      } else {
        this.getData();
      }
    },


    // 返回上一级
    goBackClick() {
      this.goBack = true;
    },
    back(s) {
      this.showBack = s;
      if (s) this.goBack = false;
    },
    // 初始化时间
    initTime() {
      this.selectedDate = this.parseTime(
        new Date(new Date().getTime() - 1000 * 3600 * 24),
        '{y}-{m}-{d}'
      );
      this.selectedWeek = this.parseTime(
        new Date(new Date().getTime() - 1000 * 3600 * 24),
        '{y}-{m}-{d}'
      );
      console.log(this.selectedWeek);
      this.selectedMonth = this.parseTime(
        new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
        '{y}-{m}'
      );
      const year = new Date().getFullYear();
      const month = new Date().getMonth();
      const otherSeason = [
        {name: `${year}-Q1`, label: `${year}第一季度`},
        {name: `${year}-Q2`, label: `${year}第二季度`},
        {name: `${year}-Q3`, label: `${year}第三季度`},
        {name: `${year}-Q4`, label: `${year}第四季度`}
      ];
      if (month >= 0 && month <= 2) {
        otherSeason.length = 1;
      }
      if (month >= 3 && month <= 5) {
        otherSeason.length = 2;
      }
      if (month >= 6 && month <= 8) {
        otherSeason.length = 3;
      }
      this.seasonList = [
        {name: `${year - 1}-Q1`, label: `${year - 1}第一季度`},
        {name: `${year - 1}-Q2`, label: `${year - 1}第二季度`},
        {name: `${year - 1}-Q3`, label: `${year - 1}第三季度`},
        {name: `${year - 1}-Q4`, label: `${year - 1}第四季度`}
      ];
      this.seasonList = this.seasonList.concat(otherSeason);
      this.selectedSeason = this.seasonList[0].name;
    },
    // 时间处理
    changeWeekDate(str) {
      const now = new Date(str);
      const nowTime = now.getTime();
      const day = now.getDay();
      const oneDayTime = 24 * 60 * 60 * 1000;
      // 显示周一
      const MondayTime = nowTime - (day - 1) * oneDayTime;
      // 显示周日
      const SundayTime = nowTime + (7 - day) * oneDayTime;
      // 初始化日期时间
      const monday = new Date(MondayTime);
      const sunday = new Date(SundayTime);
      const endTime = this.parseTime(sunday, '{y}-{m}-{d}');
      const beginTime = this.parseTime(monday, '{y}-{m}-{d}');
      this.format = `${beginTime} 至 ${endTime}`;
      return `${beginTime}|${endTime}`;
    },
    // 查询
    changeTime() {
      const resWeek = this.changeWeekDate(this.selectedWeek);
      this.timeValue =
        this.activeName == 1
          ? this.selectedDate
          : this.activeName == 2
            ? resWeek
            : this.activeName == 3
              ? this.selectedMonth
              : this.selectedSeason;
      if (this.allData.length === 0) {
        const dateType = new Map([
          ['1', '日'],
          ['2', '周'],
          ['3', '月'],
          ['4', '季度'],
          ['5','日累计',]
        ]);
        const date = [dateType.get(this.activeName), this.timeValue];
        console.log(date);
        this.$emit('changeDate', date);
      } else {
        this.getData();
      }
    },
    // 重置时间筛选
    resettTimeValue() {
      this.initTime();
      this.changeTime();
    },
    // 获取图表的数据接口
    async getData(target = this.allData) {
      this.barData = [];
      this.stateData = [];
      this.lineData = [];
      const p = {
        restSerialNo: 'kgGKqA7J',
        statType: this.activeName,
        statDate: this.timeValue
      };
      if (this.activeName != '') {
        const req = target.map((it) => {
          // eslint-disable-next-line camelcase
          const {id, parent_id} = it;
          const params = {
            ...p,
            targetId: id,
            // eslint-disable-next-line camelcase
            parentId: parent_id
          };
          return getServeData(params);
        });


        const res = await Promise.all(req);
        if (res) {
          res.forEach((it, index) => {
            const {data} = it;
            this.disposeData(data, index, target);
          });
          // 局部更新
          if(target.length == 1) {
            // 同一类型
            const index = this.allData.findIndex((item) => item.targetname == target[0].targetname);
            this.$set(this.allData,index,...target);
          }

        }

       
        console.log('所有数据', res);
        // const { data } = await getServeData();
        // this.disposeData(data);
      }
    },
    disposeData(dataArr, index,target = this.allData) {
      if (!dataArr) return;
      const targetId1040204 = this.dataItem.id == 1040204;
      const data = dataArr.filter((it) => it.tableName != '滑动框');
      data.forEach((element) => {
        if (element.tableName == '满意度公司间对标') {
          if (element.dataList && element.dataList.length) {
            element.dataList.forEach((item) => {
              // if (Number(item.score)) {
              item.score = Number(item.score).toFixed2(2);
              // }
            });
          }

          target[index].barData = element.dataList;
          target[index].rankData = element.tableNameChild;
        } else if (element.tableName == '感知要素情况') {
          const arr = [];
          let arrList = [];
          element.dataList.forEach((item, index) => {
            if (targetId1040204 && item.targetName == '维修服务') item.targetName = '安装维修';
            console.log(item.score);
            if (Number(item.score)) {
              item.score = Number(item.score).toFixed2(2);
            }
            arrList.push(item);
            if (index == element.dataList.length - 1) {
              arr.push(arrList);
              return;
            }
            if (arrList.length == 3) {
              arr.push(arrList);
              arrList = [];
            }
          });
          target[index].stateData = arr;
        } else if (element.tableName == '满意度分析') {
          console.log('日期类型=====>', this.activeName);
          let dtArr = [];
          if (element.dataList && element.dataList.length) {
            if(this.activeName == '4') {
              dtArr = element.dataList.filter((it) => it.statDate.includes('Q'));
            } else {
              dtArr = element.dataList;
            }
            dtArr.forEach((item) => {
              item.score = Number(item.score).toFixed2(2);
            });
          }
          target[index].lineData = dtArr;
          target[index].scoreData = element.tableNameChild;
        }
      });
    },
    // 点击下钻
    nextLevel(i) {
      if (1 > 0) return; // 暂时注释此功能
      this.preDataItem.push(this.dataItem);
      i.exist = i.existChild;
      i.firstTitle = this.dataItem.targetname;
      i.level = this.dataItem.targetname;
      i.targetname = i.targetName;
      i.id = i.targetId;
      i.parentId = this.dataItem.id;
      i.currentLevel = this.dataItem.currentLevel + 1;
      this.dataItem = i;
      this.getData();
    },

    changeTabItem(it, tb) {
      it.targetIdForTab = tb.targetId;
      // 重新请求数据更新局部图表
      this.$emit('updatelocal',
        {
          chartsName : it.targetname,
          tabTargetId: tb.targetId
        });
    }
  }
};
</script>
<style>
.el-select-dropdown__wrap {
  margin-bottom: 0 !important;
}
</style>

<style lang="scss">
.van-progress {
  border-radius: 0px;
}
  .service-tabs-detail {
    margin-bottom: 0px;
    padding: 16px 0px;
    padding-bottom: 0px;
    background: #fff;
    min-width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      padding: 0px 12px;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
.serve-detail {
  width: 100%;
  min-height: 100vh;
  margin-bottom: 20px;
  .content {
    .time-select {
      margin-bottom: 12px;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding-right: 16px;
      align-items: center;
      .base_box_time {
  
  font-family: Source Han Sans CN;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: -0.2736363708972931px;
  text-align: left;
  display: flex;
  .date_type_wrap {
    width: auto;
    height: 30px;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    box-shadow: 0px 1px 4px 0px #0000000F;

  }
  .date_select_wrap {
    background: #fff;
    width: auto;
    box-sizing: border-box;
    height: 30px;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 4px 0px #0000000F;
    img {
      margin-top: 2px;
      margin-left: 10px;
    }
  }
}
      .inquire {
        color: #262626;
        background: #ff9900;
        margin-left: 16px;
        padding: 6px 24px;
        // width: 76px;
        font-size: 14px;
        height: 32px !important;
        border: none;
      }
      .reset {
        width: 76px;
        height: 32px;
        background: #fff;
        color: #262626;
        border-color: #d9d9d9;
        margin-left: 16px;
      }
      .reset:hover {
        border-color: #ff9900;
        color: #ff9900;
      }
      .time-select-label{
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #262626;
      }
    }
    .main-content {
      .charts-content {
        width: 100%;
        margin-bottom: 8px;
        // height: 460px;
        .cell {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
          position: relative;
          .swiper,
          .swiperBar {
            width: 100%;
            height: calc(100% - 20px);
            .swiper-container {
              width: 100%;
              height: 100%;
              .swiper-slide {
                width: 100%;
                height: 320px;
                padding-bottom: 20%;
                background-size: cover;
                background-position: center center;
                background-repeat: no-repeat;
              }

              .swiper-pagination-bullet {
                width: 8px;
                height: 8px;
                display: inline-block;
                border-radius: 100%;
                background: #000;
                // background-color: #F29B76;
                // position: absolute;
                // bottom: 70px;
                opacity: 0.2;
              }
              // .swiper-pagination{
              .swiper-pagination-bullet-active {
                background-color: #f29b76;

                opacity: 1;
              }
              // }
            }
          }
          // 柱状图
          .swiperBar {
            width: 100%;
            height: 100%;
            .swiper-container {
              width: 100%;
              height: 100%;
              .swiper-slide {
                height: 100%;
                padding-bottom: 0;
                .charts {
                  height: 80%;
                  width: 93%;
                }
                .rank {
                  padding: 0;
                }
              }
            }
          }
          background-color: white;
          // padding: 20px 16px;
          padding: 16px;
          scrollbar-width: none;
          ::-webkit-scrollbar {
            display: none; /* Chrome Safari */
          }
          .part-title {
            font-size: 18px;
            font-weight: 500;
            color: #262626;
          }
          .rank {
            display: flex;
            align-items: center;
            margin: 16px 0;
            height: 86px;
            font-size: 14px;
            color: rgb(128, 128, 128);
            background-color: #f5f5f5;
            padding: 12px 20px;
            .rank_left_box{
              line-height: 32px;
              width: 70%;
              display: flex;
              justify-content: space-between;
              flex-direction: column;
              span {
                font-size: 12px;
              }
            }
          }
          .charts {
            // min-height: 100px;
            height: 277px;
            // flex-grow: 2;
            width: 100%;
          }
          .score-cell {
            width: 98.5%;
            padding: 8px 16px;
            margin-left: 2px;
            margin-top: 12px;
            box-sizing: border-box;
            .score-title {
              font-size: 14px;
              font-weight: 350;
              margin-bottom: 8px;
              .targetName {
               color: #262626;
              }
            }
            // cursor: pointer;
            .score-rank {
              width: 100%;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              margin-bottom: 6px;
              .score {
                font-size: 24px;
                font-weight: 500;
              }
              .ratio {
                color: #4D4D4D;
                font-size: 12px;
                margin-left: 12px;
              }
            }
            .el-progress-bar__outer {
              border-radius: 0;
            }
            .el-progress-bar__inner {
              border-radius: 0;
            }
            .el-progress-bar {
              padding-right: 0;
            }
            .el-progress__text {
              display: none;
            }
          }
          // .score-cell:hover {
          //   border-radius: 4px;
          //   box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
          //   .targetName {
          //     color: #ff9900;
          //   }
          // }
          .geo-icon {
            margin-right: 10px;
            color:#4D4D4D;
          }
          .chart-title {
            display: flex;
            height: 24px;
            align-items: center;
            .split-bar {
              display: inline-block;
              width: 2px;
              height: 14px;
              background-color: rgb(254, 153, 0);
              margin-right: 8px;
            }
            span {
              color: #262626;
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
        .cell-two {
          width: 100%;
        }
        .cell-three {
          width: 100%;
        }
        .cell-one {
          width: 100%;
          .charts {
            height: 286px;
          }
        }
      }
      // .charts-content:not(:first-child) {
      //   height: 434px;
      // }
      // .charts-content:not(:last-child) {
      //   margin-bottom: 24px;
      // }
    }
  }
  .slider-button {
    position: relative;
  }
  .swiper-button-prev,
  .swiper-container-rtl .swiper-button-next,
  .swiper-button-prevbar,
  .swiper-container-rtl .swiper-button-nextbar {
    background-image: url();
    position: absolute;
    top: -85px;
    left: 0px;
    right: auto;
    z-index: 99;
  }
  .swiper-button-next,
  .swiper-container-rtl .swiper-button-prev,
  .swiper-button-nextbar,
  .swiper-container-rtl .swiper-button-prevbar {
    background-image: url();
    position: absolute;
    top: -85px;
    right: 20px;
    left: auto;
    z-index: 99;
  }
  .el-icon-caret-left,
  .el-icon-caret-right {
    font-size: 45px;
    color: rgb(255, 153, 0);
  }
  .swiper-button-disabled {
    color: rgb(248, 213, 159);
  }
  .week-class {
    width: 300px !important;
  }
}
.swiper {
  position: relative;
  // background: green;
  .swiper-pagination {
    position: absolute;
    bottom: 0;
  }
}
::v-deep .el-input__inner {
  padding: 0 13px;
}
</style>

<style lang="scss" scoped>
</style>
