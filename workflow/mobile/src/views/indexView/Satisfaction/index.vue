<template>
  <div class="home_wrap">
     <header>
      {{ title }}
      <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')"/>
    </header>
    <!-- tab切换栏 -->
    <div class="common_tab_wrap">
       <div class="common_tab_item"
       :style="{background: currentTab.targetId == item.targetId ?  '#FFFFFF' : '#f7f7f7'}"
        v-for="item in baseTabs" :key="item.targetId" @click="checkIndex(item)"> 
           <img :src="item.icon">
           <span class="text_bt">{{item.name}}</span>
           <div class="selected_line" 
           :style="{background: currentTab.targetId == item.targetId ?  '#FE9900' : '#f7f7f7',
           width: currentTab.targetId == item.targetId ?  '100%' : '0px'}"></div>
       </div>
    </div>

    <!-- 总体感知/服务分项切换 -->
    <div class="check_wrap"> 
        <div class="check_wrap_top">
              <div class="check_wrap_item"
              @click="checkModel(item)"
              :class="[currentModel.id == item.id ? 'select_item':'']" 
              v-for="item in modelList" :key="item.id">
                    {{item.text}}
              </div>
        </div>
        <div class="date_wrap" v-show="currentModel.text == '总体感知'">
           <DatePicker @change="handleDateChange" :default-date="defaultDate" :date-types="dateTypes"></DatePicker>
        </div>
    </div>
    
    <!-- 总体感知展示部分 -->
    <div class="charts_wrap" v-if="currentModel.text == '总体感知'">
      <div class="charts_wrap_item" v-for="it in childTabList" :key="it.targetId">
           <ServiceOverviewChart
            :key="`ServiceOverviewChart${it.targetId}`"
            :selectedKpiId="selectedKpiId"
            :currentView="it"
            :date="date"
            :mapCity="mapCity"
          ></ServiceOverviewChart>
      </div>
       
    </div>
    <!-- 服务分项部分 -->
    <div class="charts_wrap"  v-if="currentModel.text == '服务分项'">
      <ServiceItem ref="serviceItem" :key="currentTab.targetId"></ServiceItem>
    </div>
    
  </div>
</template>

<script>
import {Tabs} from './kpiConfig';
import ajaxRequest from '@/api/satisfaction';
import ServiceItem from './ServiceItem.vue';
export default {
  name: 'Satisfaction',

  components: {
    ServiceItem
  },

  data() {
    return {
      title: '综合满意度',
      baseTabs: Tabs,
      currentTab: Tabs[0],

      date: [],
      defaultDate: {},
      KPIDate: {
        // 保存每个指标的最新时间，防止重复获取时间
        KPI0101: ['季度'],
        KPI0102: ['月'],
        KPI0103: ['月'],
        KPI0104: ['月'],
        // KPI05: ['周']
      },

      pickDate: [],
      dateLoading: false,
      
      
      modelList: [
        {
          text: '总体感知',
          id: '1'
        },
        {
          text: '服务分项',
          id: '2'
        }
      ],
      currentModel: {
        text: '总体感知',
        id: '1'
      },

      mapCity: {...this.mapPermission, mapAction: 'query'},
    };
  },
  
  // 迁移
  inject: ['mapPermission'],


  mounted() {
    
  },

  computed: {
    selectedKpiId() {
      return this.currentTab.targetId;
    },
    childTabList() {
      return this.currentTab.children;
    },
    kpiPermission() {
      return this.mapPermission.mapLevel <= 3; // this.selKPI.mapMaxLevel;
    },
    childId() {
      return this.currentTab.targetId;
    },
    dateTypes() {
      const type = ['KPI0103', 'KPI0104'].includes(this.childId)
        ? [
          {label: '季度', value: 'quarter'},
          {label: '月', value: 'month'}
        ]
        : [{label: '季度', value: 'quarter'}];
      return type;
    }
  },
  created() {
    this.getDataDate();
  },
  methods: {
    // tabsClick(item) {
    //   const {targetId, component} = item;
    //   this.childId = targetId;
    //   this.currentView = component;
    //   if (targetId !== 'KPI0102') {
    //     this.getDataDate();
    //   }
    // },
    handleDateChange(date) {
      const [type, value] = date;
      // 处理季度格式为2022-Q1
      this.pickDate =
        type === '季度' ? [type, value.replace('-0', '-Q')] : date;

      this.queryDate();
    },
    onSwitchDateType(date) {
      this.handleDateChange(date);
    },
    queryDate() {
      this.date = [].concat(this.pickDate);
      this.triggerChangeMode();
    },
    // 获取最新有数据的时间
    async getDataDate() {
      console.log('>>> 1111 >>>', '获取最新有数据的时间');
      if (!this.kpiPermission) return;
      if (this.KPIDate[this.childId][1]) {
        const [type, date] = this.KPIDate[this.childId];
        this.defaultDate = {[type]: date.replace('-Q', '-0')};
        this.date =
          type === '季度' ? [type, date.replace('-0', '-Q')] : [type, date];
        this.pickDate = [].concat(this.date);
        this.triggerChangeMode();
        return;
      }
      const index = Object.keys(this.KPIDate).findIndex(
        (key) => key == this.childId
      );
      try {
        this.dateLoading = true;
        const {code, data} = await ajaxRequest('Sp6lGTY6', {
          type: (index == 2 || index == 3) ? '3' : `${index + 1}`
        });
        if (code == 200 && data && data.data && data.data.info) {
          const [{statdate}] = data.data.info;
          const [type] = this.KPIDate[this.childId];
          this.$set(this.KPIDate[this.childId], 1, statdate);
          this.defaultDate = {[type]: statdate.replace('-Q', '-0')};
          this.date = [type, statdate];
          this.pickDate = [type, statdate];
          this.triggerChangeMode();
        }
        this.dateLoading = false;
      } catch (e) {
        this.dateLoading = false;
      }
    },
    // 切换指标
    checkIndex(item) {
      this.currentTab = item;
      const {targetId} = item;
      if (targetId !== 'KPI0102') {
        this.getDataDate();
      }
    },
    // 切换模块
    checkModel(item) {
      this.currentModel = item;
      this.triggerChangeMode();
    },
    // 关联服务分项
    triggerChangeMode() {
      this.$nextTick(() => {
        this.$refs.serviceItem &&
          this.$refs.serviceItem.toggleTargetData(this.date, this.childId);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.check_wrap {
  box-sizing: border-box;
  padding: 0px 16px;
  .check_wrap_top {  
      height: 65px;
      padding: 16px 0px;
     .check_wrap_item {
       background: #fff;
       display: inline-block;
       box-shadow: 1px 1px 10px 0px #23272A0F;
       height: 37px;
       border-radius: 28px;
       margin-right: 8px;
       padding: 8px 16px;
       color: #4D4D4DCC;
       font-size: 14px;
       transition-property: 'background';
       transition-duration: 0.4s;
     }
     .select_item {
       background: #fe9900;
       color: #fff;
     }
  }
  .date_wrap {
    display: flex;
    justify-content: flex-end;
  }
}

.charts_wrap {
  padding-bottom: 80px;
  background: #eeeeef;
  .charts_wrap_item {
    margin-top: 12px;
    height: 441px;
    background: #fff;
    .service-echart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>