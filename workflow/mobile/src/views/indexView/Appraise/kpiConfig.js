export const Tabs = [
  {
    targetId: 'KPI0401',
    name: '个人业务',
    component: 'Tab4Child1',
    icon: require('@/assets/img/remark/r1.png'),
    // 个人业务模块通用
    echartsMaps: [
      {
        chartConfigId: 'KPI04-ART01',
        col: 24,
        name: '满意度趋势',
        restSerialNo: 'trend',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      {
        chartConfigId: 'KPI04-ART03',
        col: 24,
        name: '满意度区域',
        restSerialNo: 'area',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },      
      
      {
        chartConfigId: 'KPI04-ART02',
        col: 24,
        name: '满意度区县排名',
        isSort: true,
        restSerialNo: 'rank',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      
      
    ],
    children: [
      {
        targetId: 'KPI040101',
        id: '20202',
        name: '资费办理规范性',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '周', value: 'monthweek'}, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
        tabs: [
          {
            id: '2020201',
            name: '合约办理',
            child: [
              {
                targetId: 'KPI040101-01',
                id: '202020102',
                name: '关键信息告知到位'
              },
              {
                targetId: 'KPI040101-02',
                id: '202020103',
                name: '客户订购正向确认'
              },
              {
                targetId: 'KPI040101-03',
                id: '202020104',
                name: '收到办理成功提醒短信'                
              },
              {
                targetId: 'KPI040101-04',
                id: '202020105',
                name: '通知短信规范透明'                
              }
            ]
          },
          {
            id: '2020202',
            name: '套餐变更',
            child: [
              {
                targetId: 'KPI040101-01',
                id: '202020202',
                name: '关键信息告知到位'
              },
              {
                targetId: 'KPI040101-02',
                id: '202020203',
                name: '客户订购正向确认'
              },
              {
                targetId: 'KPI040101-03',
                id: '202020204',
                name: '收到办理成功提醒短信'                
              },
              {
                targetId: 'KPI040101-04',
                id: '202020205',
                name: '通知短信规范透明'                
              }
            ]
          }
        ]
      },
      {
        targetId: 'KPI040102',
        id: '10101',
        name: '资费套餐',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '1010101',
            name: '宣传推广'
          },
          {
            targetId: 'KPI040102-02',
            id: '1010102',
            name: '业务规则'
          },
          {
            targetId: 'KPI040102-03',
            id: '1010103',
            name: '明白消费'
          }
        ]
      },
      {
        targetId: 'KPI040103',
        id: '1010201',
        name: '手机上网',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ],
        tabs: [
          {
            id: '101020101',
            name: '5G',
            child: [
              {
                targetId: 'KPI040103-01',
                id: '10102010101',
                name: '网络覆盖与信号强度'
              },
              {
                targetId: 'KPI040103-02',
                id: '10102010102',
                name: '手机上网速度'
              },
              {
                targetId: 'KPI040103-03',
                id: '10102010103',
                name: '手机上网稳定性'
              },
            ]
          },
          {
            id: '101020102',
            name: '非5G',
            child: [
              {
                targetId: 'KPI040103-01',
                id: '10102010201',
                name: '网络覆盖与信号强度'
              },
              {
                targetId: 'KPI040103-02',
                id: '10102010202',
                name: '手机上网速度'
              },
              {
                targetId: 'KPI040103-03',
                id: '10102010203',
                name: '手机上网稳定性'
              },
            ]
          }
        ]
      },
      {
        targetId: 'KPI040104',
        id: '1010202',
        name: '语音通话',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040104-01',
            id: '101020201',
            name: '信号强度'
          },
          {
            targetId: 'KPI040104-02',
            id: '101020202',
            name: '通话清晰'
          },
          {
            targetId: 'KPI040104-03',
            id: '101020203',
            name: '通话稳定'
          }
        ]
      },
      {
        targetId: 'KPI040105',
        id: '101',
        name: '手机客户满意度',
        issueNum: true,
        dateTypes: [
          {label: '月', value: 'month'}, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
      },

    ]
  },
  {
    targetId: 'KPI0402',
    name: '家庭业务',
    icon: require('@/assets/img/remark/r2.png'),
    id: '102',
    component: 'Tab4Child2',
    echartsMaps: [
      
      {
        chartConfigId: 'KPI04-ART01',
        col: 12,
        name: '满意度趋势',
        restSerialNo: 'trend',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      {
        chartConfigId: 'KPI04-ART03',
        col: 12,
        name: '满意度区域',
        restSerialNo: 'area',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      
      
      {
        chartConfigId: 'KPI04-ART02',
        col: 24,
        name: '满意度区县排名',
        isSort: true,
        restSerialNo: 'rank',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },  
    ],
    children: [
      {
        targetId: 'KPI040201',
        id: '10202',
        name: '上网质量',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040201-01',
            id: '1020201',
            name: '上网速度'
          },
          {
            targetId: 'KPI040201-02',
            id: '1020202',
            name: '网络连接稳定性'
          },
          {
            targetId: 'KPI040201-03',
            id: '1020203',
            name: '重点场景上网感知',
            child: [
              {
                id: '102020301',
                name: '浏览图文'
              },
              {
                id: '102020302',
                name: '看视频'
              },
              {
                id: '102020303',
                name: '玩游戏'
              },
              {
                id: '102020304',
                name: '看互联网电视'
              },
            ]
          }
        ]
      },
      {
        targetId: 'KPI040202',
        id: '10204',
        name: '互联网电视',
        dateTypes: [
          // { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040202-01',
            id: '1020401',
            name: '整体感知'
          },
          {
            targetId: 'KPI040202-02',
            id: '1020402',
            name: '终端感知质量'
          },
          {
            targetId: 'KPI040202-03',
            id: '1020403',
            name: '内容体验',
          },
          {
            targetId: 'KPI040202-04',
            id: '1020404',
            name: '使用体验',
          },
          {
            targetId: 'KPI040202-05',
            id: '1020405',
            name: '业务办理推动体验',
          },
          {
            targetId: 'KPI040202-06',
            id: '1020406',
            name: '故障维修',
          }
        ]
      },
      {
        targetId: 'KPI040203',
        id: '10203',
        name: '装维服务',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040202-01',
            id: '1020301',
            name: '装机服务'
          },
          {
            targetId: 'KPI040202-02',
            id: '1020302',
            name: '故障维修',
          }
        ]
      },
      {
        targetId: 'KPI040204',
        id: '10201',
        name: '资费套餐',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '1020101',
            name: '宣传推广'
          },
          {
            targetId: 'KPI040102-02',
            id: '1020102',
            name: '业务规则'
          },
          {
            targetId: 'KPI040102-03',
            id: '1020103',
            name: '明白消费'
          }
        ]
      },
      {
        targetId: 'KPI040205',
        id: '102',
        name: '家宽客户满意度',
        issueNum: true,
        dateTypes: [
          {label: '月', value: 'month'}, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          {label: '季度', value: 'quarter'} // quarter
        ],
      },
   
    ]
  },
  {targetId: 'KPI0403', name: '触点业务', component: 'Tab4Child3',
    icon: require('@/assets/img/remark/r3.png'),
    id: '10103',
    echartsMaps: [
      
      {
        chartConfigId: 'KPI04-ART01',
        col: 12,
        name: '满意度趋势',
        restSerialNo: 'trend',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      {
        chartConfigId: 'KPI04-ART03',
        col: 12,
        name: '满意度区域',
        restSerialNo: 'area',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },      
      
      {
        chartConfigId: 'KPI04-ART02',
        col: 24,
        name: '满意度区县排名',
        isSort: true,
        restSerialNo: 'rank',
        table: [
          {label: '满意度', score: null, rate: null},
          {label: '环比', score: null, rate: null, unit: '%'}
        ]
      },
      
      
    ],
    children: [
      {
        targetId: 'KPI0403-01',
        id: '1010303',
        name: '手厅',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '101030301',
            name: '整体感知'
          },
          {
            targetId: 'KPI040102-02',
            id: '101030305',
            name: '在线客服感知'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030303',
            name: '业务丰富度'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030302',
            name: '界面友好性'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030304',
            name: '使用体验'
          }
        ]
      },
      {
        targetId: 'KPI0403-02',
        id: 't0001',
        name: '营业厅',
        dateTypes: [
          {label: '月', value: 'month'}, // month
          {label: '季度', value: 'quarter'} // quarter
        ]
      },
    ]}
];