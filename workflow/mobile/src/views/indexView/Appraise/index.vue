<template>
   <div class="home_wrap">
     <header>
      {{ titles }}
      <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')"/>
    </header>

    <div class="common_tab_wrap">
       <div class="common_tab_item"
       :style="{background: currentTab.targetId == item.targetId ?  '#FFFFFF' : '#f7f7f7'}"
        v-for="item in baseTabs" :key="item.targetId" @click="checkIndex(item)"> 
           <img :src="item.icon">
           <span class="text_bt">{{item.name}}</span>
           <div class="selected_line" 
           :style="{background: currentTab.targetId == item.targetId ?  '#FE9900' : '#f7f7f7',
           width: currentTab.targetId == item.targetId ?  '100%' : '0px'}"></div>
       </div>
    </div>
    
    <!-- 指标切换 -->
    <div class="check_wrap"> 
        <div class="check_wrap_top scroll-h5-tab">
              <div class="check_wrap_item"
              ref="buttonRefs"
              @click="checkModel(item,index)"
              :class="[currentModel.id == item.id ? 'select_item':'']" 
              v-for="(item,index) in modelList" :key="item.id">
                    {{item.name}}
              </div>
        </div>
        <div class="date_wrap">
           <DatePicker @change="handleDateChange" :default-date="defaultDate" :date-types="dateTypes"></DatePicker>
        </div>
    </div>
     <!-- 图表展示 -->
     <div class="base_echarts_wrap">

      <div class="service-tabs-detail">
        <span v-for="(item) in currentTab.echartsMaps" :key="item.chartConfigId" 
        :class="[item.restSerialNo == chartType ? 'active' : '' ]"
        @click="changeTabItem(item)">
          {{ item.name.slice(-2) }}
        </span>
      </div>

      <van-row class="margin-row">
        
         <van-col v-for="it in modleCheck" :key="it.targetId" :span="24" class="border-col">
            <!-- 主指标 -->
            <van-col style="height: 400px" :span="24" class="border-col">
                <ServiceOverviewChart
                :key="`ServiceOverviewChart${it.targetId}${nums}`"
                :title="title"
                :isMain='true'
                :selectedKpiId="selectedId"
                :currentView="it"
                :date="date"
                :currentModel="currentModel"
                :mapCity="mapPermission"
              ></ServiceOverviewChart>
            </van-col>
            <!-- 次级指标 -->
            <van-col v-for="item in currentModel.tabs" :key="item.id" :span="24" style="height: 400px" class="border-col">
              <ServiceOverviewChart
                :key="`ServiceOverviewChart${item.id}${nums}`"
                :isLast="true"
                :title="item.name"
                :isMain='false'
                :selectedKpiId="item.id"
                :currentView="it"
                :date="date"
                :currentModel="currentModel"
                :mapCity="mapPermission"
              ></ServiceOverviewChart>
            </van-col>
          </van-col>
      </van-row>

    </div>

     <!-- 感知要素分项情况 -->
     <Perceptual :option="defaultPerceptualConfig" :date="date"></Perceptual>

    
  </div>
</template>

<script>
import {Tabs} from './kpiConfig';
import ServiceOverviewChart from './ServiceOverviewChart.vue';
import {getTargetIndexData, getNewTime} from '@/api/afterRemark/index';
import Perceptual from './Perceptual/index.vue';
export default {
  name: 'Appraise',

  inject: ['mapPermission'],
  components: {
    ServiceOverviewChart,
    Perceptual
  },

  data() {
    return {
      titles: '用后即评',
      baseTabs: Tabs,
      currentTab: Tabs[0],

      currentModel: Tabs[0].children[0],

      date: [],
      defaultDate: {},

      onScroll: null,
      scrollWidth: '',
      scrollPosition: '',
      scrollContainer: null,

      nums: '1',
      selectedKpiId: 'KPI040101',
      selectedKpiIdByChild: undefined,
      // 时间参数对应映射 
      baseConfig: {
        '日': '1',
        '周': '2',
        '月': '3',
        '季度':'4'
      },
      // 卡片指标数据
      cardIndexData: [],

      // 选中的图表类型
      chartType: 'trend'

    };
  },

  mounted() {
    this.scrollContainer = document.querySelector('.scroll-h5-tab');
    this.scrollWidth = this.scrollContainer.offsetWidth;
    this.onScroll = () => {
      this.scrollPosition = this.scrollContainer.scrollLeft;
    };
    this.scrollContainer.addEventListener('scroll', this.onScroll);
    this.queryDate();
  },

  beforeDestroy() {
    this.scrollContainer.removeEventListener('scroll', this.onScroll);
  },

  computed: {
    selectedId() {
      return this.currentModel.id;
    },
    modelList() {
      return this.currentTab.children;
    },
    dateTypes() {
      return this.currentModel.dateTypes;
    },
    // 选中的时间类型(默认取第一个类型)
    dateTypeSelect() {
      // 默认时间有的情况
      if(this.date.length > 0) {
        return this.date && this.date[0] && this.baseConfig[this.date[0]];
      }
      return this.dateTypes[0].label && this.baseConfig[this.dateTypes[0].label];
    },
    // 选中的时间类型名
    dateNameSelect() {
      if(this.date.length > 0) {
        return this.date && this.date[0];
      }
      return this.dateTypes[0].label;
      
    },
    title() {
      return this.currentModel.name;
    },
    // 感知要素分析配置
    defaultPerceptualConfig() {
 
      if(this.currentModel.child) {
        return this.currentModel.child;
      }
      return this.currentModel.tabs;
    },

    // 三个模块切换循环判定
    modleCheck() {
      return this.currentTab.echartsMaps.filter((item) => item.restSerialNo == this.chartType);
    }
  },


  methods: {
    // 根据最新属于来查数据
    queryDate() {
      this.getTime().then(() => {
        this.getTargetData();
      });
    },
    query() {
      this.getTargetData();
      this.nums++;
    },
    // 头部卡片切换数据查询
    async getTargetData() {
      if(!this.date || this.date.length == 0)return;
      // 还要获取最新有数据时间的接口
      const params = {
        'cityId': [
          this.mapPermission.cityId
        ],
        'statType': this.dateTypeSelect,
        // 这里查指标, 取的当前时间
        'statDate': [
          this.date && this.date[1]
        ],
        // 卡片有内部指标选择情况使用内部指标id查询
        'targetIds': [
          this.selectedId
        ]
        // this.option.children && this.option.children.map((item) => item.id).concat(this.selectedKpiIdByChild)
      };
      const {data} = await getTargetIndexData(params);
      // console.log('sh');
      // 塞入数据到配置数据里面
      this.cardIndexData = data || [];

    },
    // 获取最新时间
    async getTime() {
      const params = {
        'cityId': this.mapPermission.cityId,
        'statType': this.dateTypeSelect,
        'targetId': this.selectedId
        // this.option.children && this.option.children.find((item) => this.selectedKpiId === item.targetId).id
      };
      try {
        const {data} =  await getNewTime(params);
        // 赋值给默认时间
        this.defaultDate = {[this.dateNameSelect]: data.replace('-Q', '-0')};
        // 赋值给当前时间, 季度特殊处理
        this.date = this.dateNameSelect == '季度' ? [this.dateNameSelect, data.replace('-0', '-Q')] : [this.dateNameSelect, data];
        
        return Promise.resolve();
        // 处理不同的时间类型, 然后入参
      } catch (error) {
        console.log('抛出',error);
      }
      
    },
    
    // 日期时间改变回调
    handleDateChange(date) {
      const [type, value] = date;
      // 处理季度格式为2022-Q1
      this.date =
        type === '季度' ? [type, value.replace('-0', '-Q')] : date;
      this.getTargetData();
    },
    // 切换指标
    checkIndex(item) {
      this.currentTab = item;
      // eslint-disable-next-line prefer-destructuring
      this.currentModel = item.children[0];

    },
    // 切换子指标
    checkModel(item,index) {
      // 赋予当前选中的子指标
      this.currentModel = item;
      this.selectedButtonIndex = index;

      // 获取目标按钮的DOM元素
      const targetButton = this.$refs.buttonRefs[index];

      // 计算目标按钮距离容器左边界的偏移量
      const targetOffset = targetButton.offsetLeft;

      // 计算需要滚动的距离，使其位于容器中间
      const scrollTarget = Math.max(0, targetOffset - (this.scrollWidth / 2 - targetButton.offsetWidth / 2));

      // 更新滚动位置
      this.scrollContainer.scrollTo({
        left: scrollTarget,
        behavior: 'smooth' // 可选，平滑滚动效果
      });

      this.getTargetData();
      
    },

    changeTabItem(item) {
      console.log(item);
      this.chartType = item.restSerialNo;
      this.nums++;
    }
  },
};
</script>

<style lang="scss" scoped>
.home_wrap {
  padding-bottom: 40px;
}
.check_wrap {
  box-sizing: border-box;
  .check_wrap_top {  
      padding: 16px 0px;
      padding-right: 0px;
      overflow-x: scroll;
      white-space: nowrap;
     .check_wrap_item {
       background: #fff;
       display: inline-block;
       box-shadow: 1px 1px 10px 0px #23272A0F;
       height: 37px;
       border-radius: 28px;
       margin-right: 8px;
       padding: 8px 16px;
       color: #4D4D4DCC;
       font-size: 14px;
       transition-property: 'background';
       transition-duration: 0.8s;
       &:first-child {
         margin-left: 16px;
       }
     }
     .select_item {
       background: #fe9900;
       color: #fff;
     }
  }
  .date_wrap {
    display: flex;
    justify-content: flex-end;
    padding-right: 16px;
  }
}
.base_echarts_wrap {
  background: #fff;
  position: relative;
  margin-top: 12px;
  .service-tabs-detail {
    position: absolute;
    right: 10px;
    top: 2px;
    z-index: 1;
    padding: 16px 0px;
    padding-bottom: 0px;
    background: #fff;
    min-width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      padding: 0px 12px;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
}

</style>