<template>
  <div class="service-chart-box" v-loading="barLineLoading">

    <!-- 二级指标的标题 -->
    <div class="service-title">
      <!-- viewObject.name -->
      <span class="main-title" :class="[isLast? 'mini-title':'']">{{title}}</span>
      <!-- 地市/区县切换 -->
      <div class="service-tabs" v-if="currentView.isTab && (mapCity.mapLevel == 1)">
        <span v-for="tb in currentView.switchArr" :key="tb.tabId" :class="{'active': tb.tabId == childTabActive}"
         @click="changeTab(tb)">
          {{tb.name}}
        </span>
      </div>
    </div>
    <!-- 指标具体某些选项展示 -->
    <div class="service-table-wrap">
        <div class="service_item">
          <!-- <img src="@/assets/icon/fuhao.png" alt=""> -->
          <div>满意度</div>
          <div class="service_bottom">
             <span>{{ displayMap.score || '--' }}</span>
              <div class="service_bt_item">
              环比
              <img :src="displayMap.momrateType == '1' ? icon.up : icon.down" alt="" style="margin: 0;margin-left: 12px;width: 8px;" >
              <span :style="{color: displayMap.momrateType == '1' ? 'green' : 'red'}" style="margin: 0; margin-left: 2px; font-size: 12px;line-height: 19px;">{{ displayMap.momrate || '--' }}</span>
            </div>
          </div>
         
        </div>
      
        <!-- <div class="service_item">
          <img src="@/assets/images/fuhao.png" alt="">
          样本量
          <span>{{ displayMap.issueNum }}</span>
        </div> -->
        <div class="service_item">
          <!-- <img src="@/assets/icon/fuhao.png" alt=""> -->
          排名
          <span>{{ (displayMap.rank=='暂无数据'?'--':displayMap.rank) || '--' }}</span>
        </div>
    </div>
    <!-- echarts图表 -->
    <service-bar-line class="service-echart"  :key="echartsKey"
     ref="serviceBarLine" :key-map="echartsMap ? echartsMap.keyMap: {}" :chart-data="barLineData"
      :chart-option="echartsMap ? echartsMap.option : {}" :data-zoom-end="barLineData.length > 6 ? Math.floor(5 / barLineData.length * 100) : 0" />
  </div>
</template>

<script>
import ServiceBarLine from '@/components/global/ServiceBarLine/index.vue';
// import ServiceTable from '../../components/ServiceTable.vue';
import {getSatisfactionTrend, getSatisfactionArea, getTargetCountyRank,getTargetIndexData} from '@/api/afterRemark/index.js';
import {KPI1, KPI_ECHARTS} from '@/utils/echartsOption.js';
// import utils from 'bj_src/utils/utils';
import {sixPeriods} from './tool.js';
const chart_map_table = {
  'trend': getSatisfactionTrend,
  'area': getSatisfactionArea,
  'rank': getTargetCountyRank
};
export default {
  name: 'ServiceOverviewChart',
  props: ['selectedKpiId', 'currentView', 'mapCity', 'date','title','currentModel','isLast','isMain'],
  components: {
    ServiceBarLine,
    // ServiceTable
  },
  data() {
    return {
      icon: {
        up: require('@/assets/icon/up-icon.png'),
        down: require('@/assets/icon/down-icon.png')
      },
      dateType: {
        日: '1',
        周: '2',
        月: '3',
        季度: '4',
        日累计: '5'
      },
      KPI: KPI1, // 一级大类指标
      KPI_ECHARTS,
      KPIChildren: [], // 二级指标数据数组
      childKPI: {}, // 选中的二级指标
      childTabActive: '0', // 二级指标tab选中
      originKPITable: [], // 保存初始化的表格配置
      KPITable: [],

      barLineData: [], // 右侧柱状折线图
      barLineLoading: false, // 右侧柱状折线图加载

      hasMap: false, // 父级是否有地图功能

      // 指标数据
      indexData: {},
    };
  },
  computed: {
    viewObject() {
      const {chartConfigId, dimType, name, tabs, restSerialNo, id, table} = this.currentView;
      return {
        name,
        hasSwitch: !!tabs,
        switchArr: tabs || [],
        restSerialNo,
        childSplitActive: id,
        table,
        dimType,
        chartConfigId
      };
    },

   
    // 整合趋势echarts的[配置]
    echartsMap() {
      if(this.childTabActive == '0' && !this.isMain) {
        return KPI_ECHARTS['KPI04-ART01'];
      }
      return KPI_ECHARTS[this.echartsKey];
      
    },
    displayMap() {
      return this.indexData;
    },
  
    echartsKey() {
      const {hasSwitch, chartConfigId} = this.viewObject;
      return hasSwitch ? `${chartConfigId}_${this.childTabActive}` : chartConfigId;
    },    
    ajaxParams() {
      console.log('城市权限',this.mapCity);
      const {cityId} = this.mapCity;
      const [statType] = this.date;
      // CATI(省内)id 省内对应  ---- (该业务逻辑, 未知 , 选择保留)
      // const cati = {'105060201': '1050602', '105060101': '1050601'};
      // 统一处理时间, 往前推六个周期
      const timeMap = sixPeriods(this.date);
      console.log('------输出的格式--------',timeMap);
      return {
        statType: this.dateType[statType],
        statDate: timeMap,
        targetIds: [this.selectedKpiId],
        // dimType: hasSwitch ? this.childTabActive : dimType,
        cityId: [cityId]
      };
    }
  },
  methods: {
    // 获取图表数据
    queryEchartsData() {
      // 头部表格请求
      this.getTargetData();
      
      
      let restSerialNo;
      // 有地市/区县切换情况
      if(this.currentView.isTab) {
        if(this.childTabActive == '0') {
          restSerialNo = 'trend';
        }
        if(this.childTabActive == '1') {
          restSerialNo = 'area';
        }
        if(this.childTabActive == '2') {
          restSerialNo = 'rank';
        }
      
      }else {
        // 通过配置取接口
        const {restSerialNo: no} = this.viewObject;
        restSerialNo = no;
      }


      // 通用部分
      let params = this.ajaxParams;
      if(restSerialNo == 'rank' || restSerialNo == 'area') {
        const {cityId} = this.mapCity;
        const [statType] = this.date;
        params = {
          statType: this.dateType[statType],
          statDate: [this.date && this.date[1]],
          targetIds: [this.selectedKpiId],
          cityId: [cityId]
        };
      }

      if(!this.date || this.date.length == 0 ) return;

      chart_map_table[restSerialNo](params).then((res) => {
        console.log(res);
          
        if(this.currentView.isSort && this.childTabActive !== '0') {
          this.barLineData = res.data.sort((a,b) => Number(b.score) - Number(a.score) );
        }else {
          this.barLineData =  res.data;
        }
      });



      
    },
    // 获取表格数据
    async getTargetData() {
      if(!this.date || this.date.length == 0 ) return;
      // 还要获取最新有数据时间的接口
      const [statType, statDate] = this.date;
      const {cityId} = this.mapCity;
      const params = {
        'cityId': [
          cityId
        ],
        'statType': this.dateType[statType],
        // 这里查指标, 取的当前时间
        'statDate': [
          statDate
        ],
        // 卡片有内部指标选择情况使用内部指标id查询
        'targetIds': [this.selectedKpiId]
      };
      const {data} = await getTargetIndexData(params);
      console.log(data);
      // 塞入数据到配置数据里面
      if(data && data.length > 0) {
        console.log('出现',data);
        // eslint-disable-next-line prefer-destructuring
        this.indexData = data[0];

      }else{
        this.indexData = {};
      }
      

    },
    // 切换tab
    changeTab(tab) {
      this.childTabActive = tab.tabId;
      // 获取数据
      this.queryEchartsData();
    },
  },
  mounted() {
    this.queryEchartsData();
  },
  watch: {
    date() {
      this.queryEchartsData();
    },
    mapCity() {
      this.queryEchartsData();
    },
    selectedKpiId() {
      this.queryEchartsData();
    }
  }
};
</script>

<style lang="scss" scoped>
.service-chart-box {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 0 0;
  .service-echart {
    width: 100%;
    flex: 1;
    position: relative;
    height: calc(100% - 103px);
  }
  
  .service-title {
    color: #262626;
    font-size: 20px;
    padding: 0 15px;
    height: 29px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    span {
      cursor: pointer;
      // &:not(:first-child) {
      //   margin-left: 10px;
      // }
      &.main-title {
        font-family: Source Han Sans CN;
        font-size: 18px;
        font-weight: 500;
        line-height: 28px;
        text-align: left;

        color: #262626;
        font-weight: 500;
      }
      &.mini-title {
        font-family: Inter;
        position: relative;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.011em;
        text-align: left;
        padding-left: 8px;
        &::after {
          content: '';
          background: #FE9900;
          left: 0px;
          top: 5px;
          position: absolute;
          display: block;
          width: 2.14px;
          height: 16px;
        }
      }
    }

    &.service-title-border {
      // border-bottom: 1px solid rgba(140,140,140,0.35);

      span {
        &.active {
          padding-bottom: 6px;
          border-bottom: 2px solid #ffbd01;
        }
      }
    }
  }
  .service-child {
    display: flex;
    align-items: center;
  }
  .service-tabs {
    // padding: 0 15px;
    height: 24px;
    width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      flex: 1;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
  .service-splits {
    padding: 0 15px;
    display: inline-block;
    font-size: 12px;
    color: #8c8c8c;

    span {
      //  font-size: 12px;
      //  color: #8c8c8c;
      display: inline-block;
      padding: 2px 4px;
      cursor: pointer;

      &.active {
        color: #fec002;
      }

      &.disable {
        opacity: 0.45;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}

.service-table-wrap {
   height: 86px;
   margin: 16px;
   margin-top: -8px;
   background: #F5F5F5;
   display: flex;
   justify-content: space-between;
   align-items: center;
   font-family: Source Han Sans CN;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0px;
    text-align: left;

   .service_item {
      padding: 0px 20px;
      text-align: left;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      .service_bottom {
        display: flex;
        .service_bt_item {
          padding-left: 12px;
          padding-bottom: 2px;
          display: flex;
          align-items: flex-end;
          font-size: 12px;

        }
      } 
      img {
        margin-right: 12px;
        position: relative;
        top: -5px;
        width: 12px;
        margin-top: 3px;
      }
      span {
        font-family: SF Pro Display;
        font-size: 24px;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: -0.46909090876579285px;
        text-align: left;
      }
   }
}
</style>
