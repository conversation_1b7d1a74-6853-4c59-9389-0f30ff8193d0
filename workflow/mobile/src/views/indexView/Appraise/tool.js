/* eslint-disable prefer-destructuring */
// 时间格式倒推处理
export function sixPeriods(data) {
  if(data.length == 0)return;
  const periodType = data[0];
  let dateStr = data[1];
  if(periodType == '季度') {
    dateStr = dateStr.replace('Q', '0');
    const arr = dateStr.split('');
    const len = arr[6] * 3;
    arr[6] = len;
    dateStr = arr.join('');
  }
  const dateObj = new Date(dateStr);
  const periods = [];
  
  // 推算第几周
  function weekOfMonth(date) {
    const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
    const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    const lastDayOfFirstWeek = 7 - firstDayOfMonth;
    // eslint-disable-next-line no-unused-vars
    const weeksInMonth = Math.ceil((lastDayOfFirstWeek + lastDayOfMonth) / 7);
  
    return Math.ceil((date.getDate() + firstDayOfMonth - 1) / 7);
  }

  // 自身
  const formatDate = dateObj.toISOString().split('T')[0];
  if(periodType === '季度') {
    const target = formatDate.slice(0, 7).split('-');
    
    periods.push(quarter(target));
  }else{
    periods.push(periodType === '月' ? formatDate.slice(0, 7) : formatDate);
  }


  for (let i = 0; i < 5; i++) {
    if (periodType === '日') {
      dateObj.setDate(dateObj.getDate() - 1);
    } else if (periodType === '月') {
      dateObj.setMonth(dateObj.getMonth() - 1);
    } else if (periodType === '周') {
      // 周数特殊处理
      if((weekOfMonth(dateObj) > 4) || (weekOfMonth(dateObj) == 4) ) {
        const num = dateObj.getDate() % 7;
        dateObj.setDate(dateObj.getDate() - (7 + num));
      }else{
        dateObj.setDate(dateObj.getDate() - 7);
      }
    } else if (periodType === '季度') {
      dateObj.setMonth(dateObj.getMonth() - 3);
    }

    const formatDateStr = dateObj.toISOString().split('T')[0];

    if(periodType === '季度') {
      const target = formatDateStr.slice(0, 7).split('-');
        
      periods.push(quarter(target));
    }else{
      periods.push(periodType === '月' ? formatDateStr.slice(0, 7) : formatDateStr);
    }
      
  }

  return periods.reverse();
}







function quarter(data) {
  const dateObj = new Date(data);
  const year = dateObj.getFullYear();
  const month = dateObj.getMonth() + 1;
  let quarter;

  if (month >= 1 && month <= 3) {
    quarter = 'Q1';
  } else if (month >= 4 && month <= 6) {
    quarter = 'Q2';
  } else if (month >= 7 && month <= 9) {
    quarter = 'Q3';
  } else {
    quarter = 'Q4';
  }
    
  return `${year}-${quarter}`;
}