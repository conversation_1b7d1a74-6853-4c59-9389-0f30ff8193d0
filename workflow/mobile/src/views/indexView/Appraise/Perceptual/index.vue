<template>
  <div class="service-item" v-if="option&&option.length>0">
    <div class="service-box">
      <div class="service-header-title">感知要素分项情况</div>
      <div class="service-tabs" v-if="'child' in option[0]">
        <span v-for="tb in option" :key="tb.id" :class="{'active': activeTabItem.id == tb.id}"
         @click="changeTab(tb)">
          {{tb.name}}
        </span>
      </div>
       <div class="echarts_row_wrap">
          <van-row :gutter="20">
            <van-col :span="24">
              <div class="grid-content-right">
                <!-- 图表 -->
                <van-row v-for="(item,index) in comConfig" :key="'row' + index" class="margin-row">
                  <van-col v-for="it in chartsMap" :key="it.id" :span="24" style="height: 351px" class="border-col">
                      <ServiceOverviewChart
                        :key="`ServiceOverviewChart${it.id}`"
                        :title="it.name"
                        :isLast="true"
                        :selectedKpiId="it.id"
                        :currentView="item"
                        :date="date"
                        :mapCity="mapCity"
                      ></ServiceOverviewChart>
                   </van-col>
                  </van-row>
              </div>
            </van-col>
          </van-row>

          <!-- <el-row :gutter="20" v-for="item in homeOption" :key="item" >
            <el-col  v-for="it in item" style="height: 351px" :span="it.col" class="border-col">
              <ServiceOverviewChart
                :key="`ServiceOverviewChart${it.targetId}${nums}`"
                :title="''"
                :selectedKpiId="selectedKpiId"
                :currentView="it"
                :date="date"
                :mapCity="mapCity"
              ></ServiceOverviewChart>
            </el-col>
          </el-row> -->

       </div>
    </div>
  </div>
</template>

<script>
// import homeOption from '../Tab4Child2/homeOption';
import ServiceOverviewChart from '../ServiceOverviewChart.vue';
export default {
  name: 'Perceptual',
  props:['option','date'],
  components: {
    ServiceOverviewChart,
  },
  inject: ['mapPermission'],
  data() {
    return {
      nums: '1',
      // homeOption,
      target:'',
      loading: false,
      defaultDate: {},
      selectedKpiId: '',
      mapCity: {...this.mapPermission, mapAction: 'query'},
      // 时间参数对应映射 
      baseConfig: {
        '日': '1',
        '周': '2',
        '月': '3',
        '季度':'4'
      },
      activeTabItem: {},
      comConfig: [
        
        // {
        //   chartConfigId: 'KPI04-ART01',
        //   isTable: true,
        //   col: 24,
        //   name: '满意度趋势',
        //   restSerialNo: 'trend',
        //   table: [
        //     {label: '满意度', score: null, rate: null},
        //     {label: '环比', score: null, rate: null, unit: '%'}
        //   ]
        // },
        {
          chartConfigId: 'KPI04-ART03',
          col: 24,
          isTable: true,
          isTab: true,
          isSort: true,
          switchArr: [
            {
              tabId: '0',
              name:'趋势'
            },
            {
              tabId: '1',
              name:'地市'
            },
            {
              tabId: '2',
              name:'区县'
            }
          ],
          name: '满意度区域',
          restSerialNo: 'area',
          table: [
            {label: '满意度', score: null, rate: null},
            {label: '环比', score: null, rate: null, unit: '%'}
          ]
        }
        
      ]
    };
  },
  created() {
    
  },
  mounted() {
   
  },

  watch: {
    option: {
      handler(val) {
        const [first] = val;
        if(first) {
          this.activeTabItem = first;
        }
      },
      immediate: true
    }
    
  },
  

  computed: {
    chartsMap() {
      if(this.activeTabItem.child) {
        return this.activeTabItem.child;
      }
      return this.option;
    }
  },
  methods: {
    changeTab(item) {
      this.activeTabItem = item;
    }
  }
};
</script>

<style lang="scss" scoped>
.service-item {
   background: #fff;
  .service-header {
    height: 140px;
    padding-top: 32px;
    
    .service-header-tab {
      margin-top: 32px;
      cursor: pointer;
      height: 40px;
      padding: 0 25%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      > span {
        line-height: 40px;
        font-size: 14px;
        color: #262626;
      }
      .tab-items {
        cursor: pointer;
        display: inline-block;
        width: 84px;
        text-align: center;
      }
      .tab-checked {
        border-bottom: 1px solid #000;
      }
    }

    .service-date {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .service-box {
    background: #fff;
    margin-top: 8px;
    width: 100%;
    .service-header-title {
      color: #262626;
      padding: 16px;
      font-family: Source Han Sans CN;
      font-size: 18px;
      font-weight: 500;
      line-height: 28px;
      text-align: left;
    }
    .service-tabs {
    // padding: 0 15px;
    margin: auto;
    height: 24px;
    width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      flex: 1;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
    .service-date {
      display: flex;
      justify-content: flex-end;
      margin: 25px 0 23px;
      .el-button {
        margin-left: 16px;
      }

      .el-button--small{
        height: 32px !important;
        padding: 6px 24px;
        font-size: 14px;
        font-family: Source Han Sans SC;
        font-weight: 400;
      }
   }

   .echarts_row_wrap {
     background-color: #fff;
     min-height: 750px;
     .grid-content-left {
        height: 702px;
        border-right: 1px solid #DCDCDC;
        .service-kpi-map {
          height: 100%;
        }
     }
     .grid-content-right {
        height: 702px;
     }
   }
   
    
  }
}
</style>
