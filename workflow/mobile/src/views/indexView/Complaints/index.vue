<template>
 <div class="home_wrap" style="padding-bottom: 42px;">
     <header>
      {{ title }}
      <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')"/>
    </header>

    <div class="complaint-analyse">
    <div class="service-header">
      <!-- <number-title num="01" text="投诉满意度分析" /> -->
      <div></div>
      <div class="service-date">
        <div class="base_box_time">
          <div  class="date_select_wrap" @click="weekShow = true">
             {{weekDate}}
            <img src="@/assets/img/date.png" width="18" height="23">
          </div>
      </div>
        <van-popup v-model="weekShow" round position="bottom">
            <van-picker
              title="请选择时间类型"
              show-toolbar
              :columns="dateList"
              value-key='label'
              @confirm="confirmWeek"
              @cancel="weekShow = false"
            />
        </van-popup>


        <!-- <el-select v-model="weekDate" placeholder="">
          <el-option
            v-for="item in dateList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select> -->
      </div>
    </div>
    <div class="sub-main-title">
      投诉满意度分析
    </div>
    <div class="complaint-analyse-content-1">
      <van-row :gutter="10" class="header-overview">
        <van-col :span="12">
          <div><img src="@/assets/images/score.png" alt=""></div>
          <div class="name-value">
            <span>整体感知</span>
            <span>{{ sampleTypeData.score0 }}</span>
          </div>
        </van-col>
        <van-col :span="12">
          <div><img src="@/assets/images/momrate.png" alt=""></div>
          <div class="name-value">
            <span>投诉处理及时性</span>
            <span>{{ sampleTypeData.score1 }}</span>
          </div>
        </van-col>
        <van-col :span="12">
          <div><img src="@/assets/images/bad_count.png" alt=""></div>
          <div class="name-value">
            <span>投诉处理专业性</span>
            <span>{{ sampleTypeData.score2 }}</span>
          </div>
        </van-col>
        <van-col :span="12">
          <div><img src="@/assets/images/bad_ratio.png" alt=""></div>
          <div class="name-value">
            <span>投诉处理合理性</span>
            <span>{{ sampleTypeData.score3 }}</span>
          </div>
        </van-col>
      </van-row>
      <van-row class="charts-box">
        <van-col style="margin-right: 10px;" :span="24">
          <div class="charts-title"><span class="split-line"></span>投诉整体感知分析</div>
          <div class="charts-box-item">
            <lineBar id="left-line-bar" :seriesData="seriesData1" :dataZoomEnd="seriesData1.dt1.length > 6 ? Math.floor(6 / seriesData1.dt1.length * 100) : 0"></lineBar>
          </div>
        </van-col>
        <van-col :span="24">
          <div class="charts-title" style="justify-content: space-between;">
            <div>
              <span class="split-line"></span>
              投诉感知分项分析
            </div>
            <div class="tabs-btn">
              <span :class="['span-btn', isActive === 1 &&'span-isActive']" @click="checkEcharts(1)">处理及时性分析</span>
              <!-- <span style="margin: 0 5px;">|</span> -->
              <span :class="['span-btn', isActive === 2 &&'span-isActive']" @click="checkEcharts(2)">处理专业性分析</span>
              <!-- <span style="margin: 0 5px;">|</span> -->
              <span :class="['span-btn', isActive === 3 &&'span-isActive']" @click="checkEcharts(3)">处理合理性分析</span>
            </div>
          </div>
          <div class="charts-box-item">
            <lineBar id="right-line-bar" :seriesData="seriesData2" :dataZoomEnd="seriesData2.dt1.length > 6 ? Math.floor(6 / seriesData2.dt1.length * 100) : 0"></lineBar>
          </div>
        </van-col>
      </van-row>
    </div>
    <!-- <number-title num="02" text="投诉满意度样本分析" /> -->
    <div class="sub-main-title" style="margin-top: 24px;">投诉满意度样本分析</div>
    <div class="complaint-analyse-content-2">
      <van-row class="charts-box-middle">
        <van-col class="middle-col" style="margin-right: 10px;">
          <div class="middle-charts-title"><span class="split-line"></span>投满分客群表现值</div>
          <div class="middle-charts-box-item">
            <lineBar id="middle-left-line-bar"
              :yAxisConfig="{ name: '', splitLine: { show: true, lineStyle: {type: 'dashed'}}, axisLabel: { show: false }}"
              :name="['表现值','环比']"
              :dataZoomEnd="seriesData3.dt1.length > 6 ? Math.floor(6 / seriesData3.dt1.length * 100) : 0"
              :seriesData="seriesData3"
              ></lineBar>
          </div>
        </van-col>
        <van-col class="middle-col">
          <div class="middle-charts-title">
            <span class="split-line"></span>
            <span>样本占比分析</span>
          </div>
          <div class="middle-charts-box-item">
            <pie id="middle-right-pie" :seriesData="seriesData4"></pie>
          </div>
        </van-col>
      </van-row>
      <van-row class="charts-box-bottom" >
        <van-col :span="24">
          <div class="charts-title"><span class="split-line"></span>投诉满意度部门维度分析（综满样本）</div>
          <div class="charts-box-item">
            <lineBar id="bottom-left-line-bar" :seriesData="seriesData5" :dataZoomEnd="seriesData5.dt1.length > 6 ? Math.floor(6 / seriesData5.length * 100) : 0"></lineBar>
          </div>
        </van-col>
        <van-col :span="24">
          <div class="charts-title"><span class="split-line"></span>投诉满意度部门维度分析（专项样本）</div>
          <div class="charts-box-item">
            <lineBar id="bottom-middle-line-bar" :seriesData="seriesData6" :dataZoomEnd="seriesData6.dt1.length > 6 ? Math.floor(6 / seriesData6.length * 100) : 0"></lineBar>
          </div>
        </van-col>
        <van-col :span="24">
          <div class="charts-title"><span class="split-line"></span>投诉满意度部门维度分析（其他样本）</div>
          <div class="charts-box-item">
            <lineBar id="bottom-right-line-bar" :seriesData="seriesData7" :dataZoomEnd="seriesData7.dt1.length > 6 ? Math.floor(6 / seriesData7.length * 100) : 0"></lineBar>
          </div>
        </van-col>
      </van-row>
    </div>
  </div>
    
    
  </div>
</template>

<script>
import lineBar from './lineBar.vue';
import pie from './pie.vue';
import {
  queryStatDateRange,
  queryComplaintSatisfactionStats,
  queryScoreAreaDatas,
  querySampleTypeDatas,
  querySampleRatioDatas,
  querySampleAreaDatas
} from '@/api/satisfaction';
import {getStartAndEndDate} from '@/utils/echartsOption.js';
export default {
  name: 'Complaints',

  components: {
    lineBar,pie
  },

  data() {
    return {
      title: '投诉满意度',
      weekDate: '',
      dateList: [],
      isActive: 1,
      minMaxDate: {
        startDate: new Date(),
        endDate: new Date()
      },
      sampleTypeData: {
        score0: '',
        score1: '',
        score2: '',
        score3: '',
      },
      seriesData1: {
        dt1: [],
        dt2: [],
      },
      seriesData2: {
        dt1: [],
        dt2: [],
      },
      seriesData3: {
        dt1: [],
        dt2: [],
      },
      seriesData4: [],
      seriesData5: {
        dt1: [],
        dt2: [],
      },
      seriesData6: {
        dt1: [],
        dt2: [],
      },
      seriesData7: {
        dt1: [],
        dt2: [],
      },

      weekShow: false,
    };
  },

  created() {
    this.getDateRange();
  },
  mounted() {},
  watch: {
    weekDate(val) {
      if (val) {
        this.getData();
        this.getScoreAreaDatas(0);
        this.getScoreAreaDatas(this.isActive);
        this.getSampleTypeDatas();
        this.getSampleRatioDatas();
        this.getSampleAreaDatas();
      }
    }
  },
  methods: {
    confirmWeek(values) {
      this.weekDate = values.value;
      this.weekShow = false;
    },
    handleDateChange(date) {
      console.log('date ==> ', date);
    },
    checkEcharts(val) {
      if (this.isActive === val) return;
      this.isActive = val;
      this.seriesData2 = {dt1: [], dt2: []};
      this.getScoreAreaDatas(this.isActive);
    },
    async getDateRange() {
      const {code, data, msg} = await queryStatDateRange({
        stat_type: 2
      }).catch((err) => ({msg: err}));
      if (code !== 200) {
        this.$message.error(msg || '获取账期失败');
        return;
      }
      if(data && data.length > 0) {
        const [first] = data;
        this.dateList = data;
        this.weekDate = first.value;
      }
      // const [minWeek, maxWeek] = [data[0].value, data.at(-1).value];
      // const { startDate } = this.dealDate(minWeek);
      // const { endDate } = this.dealDate(maxWeek);
      // this.minMaxDate = {
      //   startDate: new Date(startDate),
      //   endDate: new Date(endDate)
      // };
      // this.weekDate = new Date(endDate);
      // console.log('账期范围==>', this.minMaxDate);
    },
    dealDate(weekDate) {
      const [yearWeek,] = weekDate.split('周');
      const [year, week] = yearWeek.split('年第');
      return getStartAndEndDate(year, week);
    },
    // 1总览数据
    async getData() {
      const params = {
        // score_type: 0, // 业务类型 0：整体感知；1：投诉处理及时性；2：投诉处理专业性；3：投诉处理合理性；	query	true
        stat_date: this.weekDate, // formatDateWeek(this.weekDate, 'yyyy年第ww周'), // 	账期	query	true
        stat_type: 2,
        city_id: 640000
      };
      const reqArr = [0, 1, 2, 3].map((it) => {
        const p = {...params, score_type: it};
        const fd = new FormData();
        Object.keys(p).forEach((key) => fd.append(key, p[key]));
        return queryComplaintSatisfactionStats(fd);
      });
      const [
        {data: dt0},
        {data: dt1},
        {data: dt2},
        {data: dt3}
      ] = await Promise.all(reqArr).catch((err) => ({msg: err}));
      const [it0] = dt0;
      const [it1] = dt1;
      const [it2] = dt2;
      const [it3] = dt3;
      this.sampleTypeData.score0 = it0 ? it0.score : '--';
      this.sampleTypeData.score1 = it1 ? it1.score : '--';
      this.sampleTypeData.score2 = it2 ? it2.score : '--';
      this.sampleTypeData.score3 = it3 ? it3.score : '--';
    },
    // 2投诉整体感知分析
    async getScoreAreaDatas(score_type = 0) {
      const params = {
        score_type,
        stat_date: this.weekDate, // formatDateWeek(this.weekDate, 'yyyy年第ww周'), // 	账期	query	true
        stat_type: 2,
        city_id: 640000
      };
      const fd = new FormData();
      Object.keys(params).forEach((key) => {
        fd.append(key, params[key]);
      });
      const {code, data, msg} = await queryScoreAreaDatas(
        fd
      ).catch((err) => ({msg: err}));
      if (code !== 200) {
        this.$message.error(msg || '查询投诉满意度统计数据失败');
        return;
      }
      const dt1 = data.map((it) => ({name: it.city_name, value: it.score}));
      const dt2 = data.map((it) => ({name: it.city_name, value: it.momrate}));
      const seriesData = {dt1, dt2};
      if (score_type === 0) {
        this.seriesData1 = seriesData;
      } else {
        this.seriesData2 = seriesData;
      }
      console.log('满意度区域柱状图2222', seriesData);
    },
    // 3投满分样本量表现值
    async getSampleTypeDatas() {
      // const params = {
      //   sample_type: '0', // 样本类型 0：综满；1：专项；2：其他；
      //   stat_date: formatDateWeek(this.weekDate, 'yyyy年第ww周'), // 	账期	query	true
      //   stat_type: '2',
      // }
      const fd = new FormData();
      fd.append('city_id', 640000);
      // fd.append('stat_date', formatDateWeek(this.weekDate, 'yyyy年第ww周'));
      fd.append('stat_date', this.weekDate);
      fd.append('stat_type', '2');
      const {code, data, msg} = await querySampleTypeDatas(
        fd
      ).catch((err) => ({msg: err}));
      if (code !== 200) {
        this.$message.error(msg || '投满分样本量表现值失败');
        return;
      }
      const keyMap = new Map([
        ['0', '综满'],
        ['1', '专项'],
        ['2', '其他'],
      ]);
      const dt1 = data.map((it) => {
        // eslint-disable-next-line camelcase
        const {sample_type, score} = it;
        return {name: keyMap.get(sample_type), value: score};
      });
      const dt2 = data.map((it) => {
        // eslint-disable-next-line camelcase
        const {sample_type, score_momrate} = it;
        // eslint-disable-next-line camelcase
        return {name: keyMap.get(sample_type), value: score_momrate};
      });
      const seriesData = {dt1, dt2};
      this.seriesData3 = seriesData;
    },
    // 4 样本占比分布
    async getSampleRatioDatas() {
      const fd = new FormData();
      fd.append('city_id', 640000);
      // fd.append('stat_date', formatDateWeek(this.weekDate, 'yyyy年第ww周'));
      fd.append('stat_date', this.weekDate);
      fd.append('stat_type', '2');
      const {code, data, msg} = await querySampleRatioDatas(
        fd
      ).catch((err) => ({msg: err}));
      if (code !== 200) {
        this.$message.error(msg || '投满分样本量表现值失败');
        return;
      }
      const keyMap = new Map([
        ['0', '综满'],
        ['1', '专项'],
        ['2', '其他'],
      ]);
      const seriesData = data.map((it) => {
        const {sample_type, sample_count, sample_ratio} = it;
        const rate = (sample_ratio === '0' || sample_ratio === 0) || sample_ratio ? sample_ratio : '-';
        return {name: keyMap.get(sample_type), value: sample_count, rate: `${rate}%`};
      });
      this.seriesData4 = seriesData;
    },
    // 5 投诉满意度部门维度
    async getSampleAreaDatas() {
      const params = {
        // sample_type,
        stat_date: this.weekDate,// formatDateWeek(this.weekDate, 'yyyy年第ww周'), // 	账期	query	true
        stat_type: 2,
        city_id: 640000
      };
      const reqArr = [0, 1, 2].map((it) => {
        const p = {...params, sample_type: it};
        const fd = new FormData();
        Object.keys(p).forEach((key) => fd.append(key, p[key]));
        return querySampleAreaDatas(fd);
      });
      const [{data: data1}, {data: data2}, {data: data3}] = await Promise.all(reqArr).catch((err) => ({msg: err}));
      console.log('投诉满意度部门维度', data1, data2, data3);
      this.seriesData5 = {
        dt1: data1.map((it) => ({name: it.city_name, value: it.score})),
        dt2: data1.map((it) => ({name: it.city_name, value: it.score_momrate})),
      };
      this.seriesData6 = {
        dt1: data2.map((it) => ({name: it.city_name, value: it.score})),
        dt2: data2.map((it) => ({name: it.city_name, value: it.score_momrate})),
      };
      this.seriesData7 = {
        dt1: data3.map((it) => ({name: it.city_name, value: it.score})),
        dt2: data3.map((it) => ({name: it.city_name, value: it.score_momrate})),
      };
      // const dt1 = data.map(it => ({name: it.city_name, value: it.score}));
      // const dt2 = data.map(it => ({name: it.city_name, value: it.momrate}));
      // const seriesData = {dt1, dt2};
      // console.log('满意度区域柱状图2222', seriesData);
    }
  }
};
</script>

<style lang="scss" scoped>
$borderColor: #e6e6e6;
$border: none; // 1px solid $borderColor;
$titleColor: #262626;
.complaint-analyse {
  .sub-main-title {
    color: #262626;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
    padding: 16px 0 16px 16px;
    background-color: #fff;
  }
  .service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .service-date {
      .base_box_time {
        // width: 196px;
        margin-top: 8px;
        margin-bottom: 12px;
        margin-right: 14px;
        
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        letter-spacing: -0.2736363708972931px;
        text-align: left;
        display: flex;
  .date_type_wrap {
    width: auto;
    height: 30px;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    box-shadow: 0px 1px 4px 0px #0000000F;
  }
  .date_select_wrap {
    background: #fff;
    width: auto;
    box-sizing: border-box;
    height: 30px;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 4px 0px #0000000F;
    img {
      margin-top: 2px;
      margin-left: 10px;
    }
  }
}

    }
  }
  .complaint-analyse-content-1 {
    background: #fff;
    // height: 510px;
    .header-overview {
      text-align: center;
      margin-bottom: 10px;
      padding: 0px 16px;
      padding-top: 20px;
      img {
        width: 52px;
        height: 52px;
      }
      .van-col {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 20px;
      }
      .name-value {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        span {
          display: block;
          width: 100%;
          text-align: left;
        }
        :nth-child(1) {
          font-size: 14px;
          margin-bottom: 8px;
        }
        :nth-child(2) {
          font-size: 24px;
        }
      }
    }
    .charts-box {
      // height: 400px;
      padding: 16px;
      padding-bottom: 0px;
      .van-col {
        border: $border;
        padding-bottom: 16px;
        .charts-title {
          margin-bottom: 16px;
          // padding-left: 20px;
          line-height: 24px;
          font-weight: 500;
          color: $titleColor;
          
          font-size: 16px;
          border-bottom: $border;
          .tabs-btn {
            width: 100%;
            margin-top: 16px;
            height: 22px;
            line-height: 22px;
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: 400;
            color: #4d4d4d;
            .span-btn {
              cursor: pointer;
              flex: 1;
              text-align: center;
              border-top: 1px solid #e6e6e6;
              border-bottom: 1px solid #e6e6e6;
              border-right: 1px solid #e6e6e6;
              &:nth-child(1) {
                border-left: 1px solid #e6e6e6;
                border-radius: 4px 0 0 4px;
              }
              &:last-child {
                border-right: 1px solid #e6e6e6;
                border-radius: 0 4px 4px 0;
              }
            }
            .span-isActive {
              color: #fff;
              background-color: #FF9900;
              border: none;
            }
          }
        }
        .charts-box-item {
          height: 207px;
        }
      }
    }
  }
  .complaint-analyse-content-2 {
    background-color: #fff;
    // padding: 20px 10px;
     padding: 0px 16px;
    .charts-box-middle {
      margin-bottom: 10px;
      width: 100%;
      .middle-col {
        width: 100%;
        border: $border;
      }
      .van-col {
        padding-bottom: 16px;
        .middle-charts-title {
          height: 24px;
          line-height: 24px;
          margin-bottom: 16px;
          // padding-left: 20px;
          border-bottom: $border;
          color: $titleColor;
          font-weight: 500;
          
          font-size: 16px;
          display: flex;
          align-items: center;
        }
        .middle-charts-box-item {
          height: 207px;
        }
      }
    }
    .charts-box-bottom {
      width: 100%;
      .van-col {
        width: 100%;
        margin-bottom: 20px;
        border: $border;
        .charts-title {
          height: 32px;
          line-height: 32px;
          margin-bottom: 16px;
          // padding-left: 10px;
          font-weight: 500;
          border-bottom: $border;
          color: $titleColor;
          
          font-size: 16px;
          display: flex;
          align-items: center;
        }
        .charts-box-item {
          height: 207px;
        }
      }
    }
  }
  .split-line {
    display: inline-block;
    margin-top: 1px;
    width: 2.14px;
    height: 14px;
    margin-right: 8.5px;
    background-color: #FE9900;
  }
}
</style>