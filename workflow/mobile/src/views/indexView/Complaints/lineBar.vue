<template>
  <div :id="id" class="line-bar-chart"></div>
</template>

<script>
import * as echarts from 'echarts';
export default {
  name: 'lineBar',
  components: {},
  props: {
    id: {
      type: String,
      default: 'line-bar-chart-1'
    },
    name: {
      type: Array,
      default: () => ['满意度','环比']
    },
    seriesData: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    yAxisConfig: {
      type: Object,
      default: () => ({})
    },
    barConfig: {
      type: Object,
      default: () => ({})
    },
    // 设置dataZoomStyle里面的end属性，不为0显示dataZoom
    dataZoomEnd: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      dataZoomStyle: {
        show: true,
        type: 'slider',
        end: 40,
        handleSize: 0, // 滑动条的 左右2个滑动条的大小
        height: 8, // 组件高度
        left: 0, // 左边的距离
        right: 0, // 右边的距离
        bottom: 10, // 右边的距离
        handleColor: '#eee', // h滑动图标的颜色
        handleStyle: {
          borderColor: '#eee',
          borderWidth: '1',
          shadowBlur: 2,
          background: '#eee',
          shadowColor: '#eee',
        },
        backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
        showDataShadow: false, // 是否显示数据阴影 默认auto
        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        handleIcon:
          'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
        // filterMode: "filter",
        moveHandleStyle: {color: '#eee'},
      },
    };
  },
  watch: {
    seriesData: {
      handler() {
        this.$nextTick(() => {
          this.initCharts();
        });
      },
      deep: true
    },
    dataZoomEnd: {
      handler() {
        this.$nextTick(() => {
          this.initCharts();
        });
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    initCharts() {
      const chartDom = document.getElementById(this.id);
      const option = {
        tooltip: {
          trigger: 'axis',
        },
        dataZoom: this.dataZoomEnd ?
          [{...this.dataZoomStyle, end: this.dataZoomEnd}]
          : [],
        grid: {
          top: '20%',
          left: '2%',
          bottom: '18',
          right: '0%',
          containLabel: true
        },
        legend: {
          icon: 'rect', 
          itemWidth:  14 ,
          itemHeight: 2 ,
          textStyle: { // 可选，设置图例文字样式
            color: '#2C3542A6',
            fontSize: 12,
          },
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLine:{
              lineStyle:{
                color:'#e0e0e0'
              }
            },
            axisLabel:{
              color:'#8c8c8c',
              interval: 0
            },
            axisTick: {show: false},
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false,
            },
            name: '单位：-',
            axisLabel: {
              formatter: '{value}'
            },
            splitLine: {lineStyle: {type: 'dashed'}},
      
          },
          {
            type: 'value',
            splitLine: {
              show: false
            },
            show: false,
            axisLabel: {
              formatter: '{value}'
            },
            splitLine: {lineStyle: {type: 'dashed'}},
          },
        ],
        series: [
          {
            type: 'bar',
            barWidth: '11.42px',
            itemStyle: {
						    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						      {offset: 0, color:'#5FAEFF'},
						      {offset: 1, color:'#0682FF'}
						    ])
				    	},
            tooltip: {
              valueFormatter(value) {
                return value;
              }
            },
            // itemStyle: {
            //   color: 'rgb(94, 152,246)'
            // },
            // label: {
            //   show: false, // true,
            //   position: 'top'
            // },
            data: []
          },
          {
            type: 'line',
            symbol: 'none',
            smooth: true,
            itemStyle: {
              color: '#FFB949'
            },
            tooltip: {
              valueFormatter(value) {
                return `${value}%`;
              }
            },
            data: []
          }
        ]
      };
      const myChart = this.$echarts.init(chartDom,null,{renderer:'svg'});
      const {dt1, dt2} = this.seriesData;

      const [name1, name2] = this.name;

      const axisData = dt1.map((item) => item.name);

      option.xAxis[0].data = axisData;

      const [y1, y2] = option.yAxis;
      option.yAxis = [{...y1, ...this.yAxisConfig}, y2];
      const [series1, series2] = option.series;
      option.series = [
        {
          ...series1,
          name: name1,
          ...this.barConfig,
          data: dt1
        },
        {
          ...series2,
          yAxisIndex: 1,
          name: name2,
          data: dt2
        }
      ];
      myChart.setOption({...option});
      window.addEventListener('resize', () => {
        myChart.resize({width: 'auto', height: 'auto'});
      });
    }
  }
};
</script>

<style scoped lang="scss">
.line-bar-chart {
  width: 100%;
  height: 100%;
  > div {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
