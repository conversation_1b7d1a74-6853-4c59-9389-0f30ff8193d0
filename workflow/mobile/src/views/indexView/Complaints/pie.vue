<template>
  <van-row :gutter="10" style="width: 100%; height:100%">
    <van-col :span="12">
      <div :id="id" class="pie-chart-1"></div>
    </van-col>
    <van-col :span="12">
      <div style="height: 100%; display: flex; flex-direction: column; justify-content: center;text-align: center;">
        <van-row :gutter="10" class="type">
          <van-col :span="2"></van-col>
          <van-col :span="8">业务分类</van-col>
          <van-col :span="7">样本量</van-col>
          <van-col :span="7">占比</van-col>
        </van-row>
        <van-row :gutter="10" class="value" v-for="(it,idx) in seriesData" :key="idx" style="color: gray;text-align: center;">
          <van-col :span="2"><i class="it-dot" :style="{background: colorArr[idx]}"></i></van-col>
          <van-col :span="8">{{ it.name }}</van-col>
          <van-col :span="7">{{ it.value }}</van-col>
          <van-col :span="7">{{ it.rate }}</van-col>
        </van-row>
      </div>
    </van-col>
  </van-row>
</template>

<script>
const color = [
  'rgb(15,148,255)',
  'rgb(208, 76,255)',
  'rgb(124,222,56)',
  'rgb(105,89,255)'
];
export default {
  name: 'pie',
  components: {},
  props: {
    id: {
      type: String,
      default: 'pie-chart-1'
    },
    seriesData: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      colorArr: color,
      option: {
        color,
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          show: false
        },
        series: [
          {
            name: '样本占比分析',
            type: 'pie',
            radius: ['50%', '80%'],
            // left: '0',
            // right: '50%',
            avoidLabelOverlap: false,
            itemStyle:{
              normal: {
                borderWidth: 5,
                borderColor: '#fff',
              }
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18
                // fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      }
    };
  },
  watch: {
    seriesData: {
      handler() {
        this.$nextTick(() => {
          this.initCharts();
        });
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    initCharts() {
      const chartDom = document.getElementById(this.id);
      const myChart = this.$echarts.init(chartDom, null, {renderer: 'svg'});
      this.option.series[0].data = this.seriesData;
      myChart.setOption({...this.option});
      window.addEventListener('resize', () => {
        myChart.resize({width: 'auto', height: 'auto'});
      });
    }
  }
};
</script>

<style scoped lang="scss">
.van-row {
  .van-col {
    height: 100%;
    margin: 4px 0px;
    .it-dot {
      display: inline-block;
      margin-top: 2px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
  }
}
.pie-chart-1 {
  width: 100%;
  height: 100%;
  > div {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
