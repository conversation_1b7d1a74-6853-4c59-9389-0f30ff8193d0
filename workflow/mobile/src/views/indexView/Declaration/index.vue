<template>
   <div class="home_wrap">
     <header>
      {{ title }}
      <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')"/>
    </header>

    <div class="date_wrap">
      <DatePicker @change="handleDateChange" :default-date="defaultDate" :date-types="selKPI.dateTypes"></DatePicker>
    </div>

    <div :key="selectedKpiId" class="service-kpi-detail" v-loading="loading">
      <!-- <ServcieOverviewMap
        :date="date"
        :key="`${selectedKpiId}_MAP`"
        :selected-kpi-id="selectedKpiId"
        :target="target"
        @map-change="onMapChange"
        class="service-kpi-map"
        v-if="selKPI.hasMap"
      /> -->
      <div class="overall-situation">
        <div class="overall-title">工信部申告整体情况</div>
        <div
          :class="{'active': childKPI.targetId == item.targetId}"
          :key="item.targetId"
          @click="changeKPIChild(item)"
          class="overall-card"
          v-for="item in overallSituationList"
        >
          <div class="overall-card-title">
            <!-- <img src="../images/point-icon.svg" /> -->
            <div>{{item.name|formatNull}}</div>
          </div>
          <div class="overall-card-content">
            <div :key="el.label" v-for="el in item.tabs.table">
              <div class="label">{{el.label}}</div>
              <div :style="{color:el.score < 0 ? '#d9001b' : ''}" class="score">{{el.score|formatNull}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="service-kpi-echarts">
      <template v-for="(item,index) in KPIChildren">
        <div :key="`title${index}`" class="index-title" v-if="index%2===0">{{item.name}}分项分析</div>
        
        <div :key="index" class="index-item" v-if="index%2===0">

          <div class="service-tabs">
            <span v-for="(tb,ind) in [`${item.tabs.name}分析`,`${KPIChildren[index+1].tabs.name}分析`]" :key="tb" 
            :class="[item.active ==`${index}${ind}` ? 'active' : '' ]"
             @click="changeTab(item,`${index}${ind}`)">
              {{tb}}
            </span>
          </div>

          <div v-if="item.active ==`${index}${0}`">
            <!-- <div class="index-name">{{item.tabs.name}}分析</div> -->
            <service-bar-line
              :chart-data="item.barLineData"
              :chart-option="KPI_ECHARTS[item.echartsKey] ? KPI_ECHARTS[item.echartsKey].option : {}"
              :data-zoom-end="item.barLineData.length > 6 ? Math.floor(6 / item.barLineData.length * 100) : 0"
              :key="item.echartsKey"
              :key-map="KPI_ECHARTS[item.echartsKey] ? KPI_ECHARTS[item.echartsKey].keyMap: {}"
              class="service-echart"
              ref="serviceBarLine"
              v-loading="loading"
            />
          </div>


          <div v-if="index+1 < KPIChildren.length && item.active ==`${index}${1}`">
            <!-- <div class="index-name">{{KPIChildren[index+1].tabs.name}}分析</div> -->
            <service-bar-line
              :chart-data="KPIChildren[index+1].barLineData"
              :chart-option="KPI_ECHARTS[KPIChildren[index+1].echartsKey] ? KPI_ECHARTS[KPIChildren[index+1].echartsKey].option : {}"
              :data-zoom-end="KPIChildren[index+1].barLineData.length > 6 ? Math.floor(6 / KPIChildren[index+1].barLineData.length * 100) : 0"
              :key="KPIChildren[index+1].echartsKey"
              :key-map="KPI_ECHARTS[KPIChildren[index+1].echartsKey] ? KPI_ECHARTS[KPIChildren[index+1].echartsKey].keyMap: {}"
              class="service-echart"
              ref="serviceBarLine"
              v-loading="loading"
            />
          </div>
          <div v-else></div>
        </div>
      </template>
    </div>


    
  </div>
</template>

<script>
import ServiceBarLine from '@/components/global/ServiceBarLine/index.vue';
import {childTabs2} from './kpiConfig';
import {KPI_ECHARTS} from '@/utils/echartsOption.js';
import ajaxRequest from '@/api/satisfaction';
import utils from '@/utils/index';
export default {
  name: 'Declaration',

  components: {
    ServiceBarLine
  },

  inject: ['mapPermission'],

  data() {
    return {
      title: '工信部申告',
      selectedKpiId: 'KPI02',
      loading: false,
      KPI_ECHARTS,
      date: [],
      dateType: {
        '日': '1',
        '周': '2',
        '月': '3',
        '季度': '4',
        '日累计': '5'
      },
      KPIChildren: [], // 二级指标数据数组
      childKPI: {}, // 选中的二级指标
      mapCity: {...this.mapPermission, mapAction: 'query'},
      target: '',
      KPIDate: {
        // 保存每个指标的最新时间，防止重复获取时间
        KPI01: ['季度'],
        KPI02: ['月'],
        KPI03: ['月'],
        KPI04: ['月'],
        KPI05: ['周']
      },
      pickDate: [],
      defaultDate: {},
    };
  },

  computed: {
    restSerialNo() {
      const {restSerialNo} = childTabs2;
      return restSerialNo;
    },
    kpiPermission() {
      return this.mapPermission.mapLevel <= this.selKPI.mapMaxLevel;
    },
    selKPI() {
      const {dateTypes, children, mapMaxLevel} = childTabs2;
      const childKPISection = [0, children.length];
      return {
        dateTypes,
        hasMap: true,
        mapMaxLevel: mapMaxLevel || 3,
        childKPISection
      };
    },
    overallSituationList() {
      return this.KPIChildren.filter((item) => item.tabs.tabId === '1');
    }
  },

  filters: {
    formatNull(val) {
      if (val === 0 || val === '0') {
        return val;
      }
      if (val === 'null') {
        return '--';
      }
      return val || '--';
    }
  },

  async created() {
    await this.getDataDate();
    this.getKPIChildren();
    this.getAppealData();
  },

  mounted() {
    
  },

  watch: {
    date() {
      this.getAppealData();
    },
    mapCity() {
      this.getAppealData();
    }
  },

  methods: {
    handleDateChange(date) {
      const [type, value] = date;
      // 处理季度格式为2022-Q1
      this.pickDate =
        type === '季度' ? [type, value.replace('-0', '-Q')] : date;
      this.queryDate();
    },
    getKPIChildren() {
      const {children} = childTabs2;
      const [start, end] = this.selKPI.childKPISection;
      this.KPIChildren = (children || []).slice(start, end);
      console.log('哈哈',this.KPIChildren);
      [this.childKPI] = this.KPIChildren;
      this.onTargetChange();
    },

    changeKPIChild(kpi) {
      if (this.childKPI.targetId == kpi.targetId) return;
      this.childKPI = kpi;
      this.onTargetChange();
    },
    queryDate() {
      this.date = [].concat(this.pickDate);
    },
    // 获取最新有数据的时间
    async getDataDate() {
      if (!this.kpiPermission) return;
      if (this.KPIDate[this.selectedKpiId][1]) {
        const [type, date] = this.KPIDate[this.selectedKpiId];
        this.defaultDate = {[type]: date.replace('-Q', '-0')};
        this.date =
          type === '季度' ? [type, date.replace('-0', '-Q')] : [type, date];
        this.pickDate = [].concat(this.date);
        return;
      }
      try {
        this.loading = true;
        const {code, data} = await ajaxRequest('Sp6lGTY6', {
          type: '2'
        });
        if (code == 200 && data && data.data && data.data.info) {
          const [{statdate}] = data.data.info;
          const [type] = this.KPIDate[this.selectedKpiId];
          this.$set(this.KPIDate[this.selectedKpiId], 1, statdate);
          this.defaultDate = {[type]: statdate.replace('-Q', '-0')};
          this.date = [type, statdate];
          this.pickDate = [type, statdate];
        }



        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
    /**
     * @description 获取工信部申诉的数据(趋势图的两个月累计、领先值)
     * @param
     * <AUTHOR>
     * @date 2023-12-15
     */
    getAppealData() {
      const [statType, statDate] = this.date;
      const cityId = this.mapCity.cityid || this.mapCity.cityId;

      // CATI(省内)id 省内对应
      const cati = {'105060201': '1050602', '105060101': '1050601'};
      if (!cityId || !statDate) return;
      this.loading = true;

      const promises = this.KPIChildren.map(async (item) => {
        const [appealAmount, appealRate] = item.ajaxIds;
        if (appealAmount && appealRate) {
          const params = {
            statType: this.dateType[statType],
            opTime: statDate,
            targetId: cati[item.targetId] || item.targetId,
            dimType: item.tabs.tabId || '1',
            cityId,
          };
          return Promise.all([
            ajaxRequest(item.restSerialNo || this.restSerialNo, {
              ...params,
              targetId: appealAmount
            }),
            ajaxRequest(item.restSerialNo || this.restSerialNo, {
              ...params,
              targetId: appealRate
            })
          ]).then((res) => {
            const [amount, rate] = res;
            const {table} = item.tabs;
            let anountData = [];
            let rateData = [];
            if (amount.code == 200 && amount.data && amount.data.data) {
              const {chart, data: {first: [item]}} = amount.data.data;
              anountData = this.processEchartsData(Object.values(chart));
              table[0].score = item ? item.score : null;
            } else {
              table[0].score = null;
            }
            if (rate.code == 200 && rate.data && rate.data.data) {
              const {chart, data: tableData} = rate.data.data;
              rateData = this.processEchartsData(Object.values(chart), item.tabs.tabId === '1' ? 3 : 1);
              if (tableData.first) {
                table[1].score = tableData.first[0] ? tableData.first[0].score : null;
              }
              if (table[2]) {
                const ary = Object.values(tableData).reduce((prev, cur) => prev.concat(cur), []);
                const leading = this.calLeadingValue(ary);
                table[2].score = leading;
              }
            } else {
              table[1].score = null;
              if (table[2]) {
                table[2].score = null;
              }
            }
            item.echartsKey = [this.selectedKpiId, item.targetId, item.tabs.tabId].filter((item) => item ? true : false).join('_');
            if (anountData.length) {
              item.barLineData = anountData.map((item, index) => ({
                ...item,
                ...rateData[index],
                // // 查询归属单位时，appartname有值就显示appartname的数据
                ...(item.appartname && item.appartname !== '-' ? {cityname: item.appartname} : {})
              }));
              console.log();
            } else {
              item.barLineData = [].concat(rateData).map((item) => ({
                ...item,
                // 查询归属单位时，appartname有值就显示appartname的数据
                ...(item.appartname && item.appartname !== '-' ? {cityname: item.appartname} : {})
              }));
            }
          }).catch((err) => {
            console.log(err);
          });
        }
      });
      Promise.all(promises).finally(() => {
        this.loading = false;
      });
    },

    // 计算领先值
    calLeadingValue(ary) {
      if (!ary.length) return null;
      const [first, second, third] = ary;
      if (!first && !second && !third) return null;
      let secleading, thileading;
      if (second) {
        secleading = (Number(first.score) - Number(second.score)).toFixed(2);
      }
      if (third) {
        thileading = (Number(first.score) - Number(third.score)).toFixed(2);
      }
      if (secleading == undefined) return thileading;
      if (thileading == undefined) return secleading;
      return Math.min(secleading, thileading);
    },
    // 处理echarts数据
    // first:移动 second:电信 third:联通
    processEchartsData(dataAry, start = 0) {
      let data = [];
      const {name, mapAction} = this.mapCity;
      dataAry.forEach((item, index) => {
        const ary = utils.handlerMomrateAndYoyrate(item).map((it) => ({
          ...it,
          [`value${start + index + 1}`]: it.score || '',
          [`momrate${start + index + 1}`]: it.momrate || '',
          ...(mapAction == 'select' ? (it.cityname == name ? {itemStyle: {color: '#FFDF55'}} : {}) : {}),
          childSplitActiveName: '' // 工信部满意度分业务table文字处理
        }));
        if (index == 0) {
          data = ary;
        } else {
          for (let i = 0; i < data.length; i++) {
            data[i] = {...data[i], ...ary[i]};
          }
        }
      });
      return data;
    },
    onTargetChange(action) {
      const {targetId, dateTypes, name} = this.childKPI;
      this.target = {
        targetId,
        targetName: name,
        dateTypes,
        childTabActive: '1',
        childSplitActive: '',
        __action: action
      };
    },
    // 地图改变
    onMapChange(map) {
      this.mapCity = {...map};
    },

    changeTab(item,index) {
      item.active = index;
    }
  },
};
</script>

<style scoped lang="scss">
  .service-tabs {
    padding: 16px 0px;
    padding-top: 10px;
    background: #fff;
    height: 48px;
    min-width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      padding: 0px 12px;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
.date_wrap {
  display: flex;
  justify-content: flex-end;
  padding: 12px 14px;
}
.service-date {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 23px;
  .el-button {
    margin-left: 10px;
  }
}
.service-kpi-detail {
  background: #ffffff;
  // height: 521px;
  // .service-kpi-map {
  //   flex: 0.7;
  // }
  .overall-situation {
    padding: 12px 18px 4px 16px;
    .overall-title {
      color: #262626;
      margin-bottom:16px;
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
    }
    .overall-card {
      margin-bottom: 16px;
      cursor: pointer;
    }

    .overall-card-title {
      display: flex;
      align-content: center;
      color: #262626;
      
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      &::before {
        content: '';
        width: 2.14px;
        height: 14px;
        background: #FE9900;
        margin-right: 8px;
        margin-top: 6px;
      }
      img {
        margin-right: 4.5px;
      }
    }
    .overall-card-content {
      margin-top: 16px;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding: 12px 21px;
      padding-bottom: 0px;
      >div {
        margin-bottom: 20px;
      }
      .label {
        color: #262626;
        
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        width: 254px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .score {
        color: #262626;
        font-family: SF Pro Display;
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: -0.469px;
        margin-top: 8px;
      }
    }
  }
}
.active {
  // box-shadow: 0px 4px 11px 1px rgba(99, 99, 99, 0.19);
  // box-shadow: 0px 2px 4px 0px #FF9900CC;
  // border: 2px solid #ff9900;
}
.service-kpi-echarts {
  margin-top: 8px;
  width: 100%;
  position: relative;
}
.service-echart {
  height: 220px;
}
.index-title {
  color: #262626;
  background: #ffffff;
  padding: 12px 18px 4px 16px;
  
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;
  text-align: left;
}
.index-name {
  
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;
  text-align: left;
  margin-bottom: 14px;
}
.index-item {
  background: #ffffff;
  // display: flex;
  padding: 0px 16px 10px 16px;
  margin-bottom: 8px;
  // > div {
  //   flex: 1;
  //   flex-shrink: 0;
  // }
  // > div:first-child {
  //   margin-right: 12px;
  // }
  // > div:last-child {
  //   margin-left: 12px;
  // }
}
</style>
