export const childTabs2 = {
  targetId: 'KPI02',
  name: '工信部申告',
  dateTypes: [
    {label: '日', value: 'date'}, // date
    {label: '月', value: 'month'} // monthrange
  ],
  restSerialNo: '7LxwnCvM',
  mapMaxLevel: 2,
  // 地图选项配置
  mapOption: {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const {name, data} = params;
        const str = [name];
        for (let i = 0; i < 2; i++) {
          const targetName = i == 0 ? 'targetname' : `targetname${i}`;
          const value = i == 0 ? 'value' : `value${i}`;
          if (data[targetName]) {
            const [seriesName] = data[targetName].split('-');
            str.push(`${seriesName} ${data[value] || '-'}`);
          }
        }
        return str.join('</br>');
      }
    }
  },
  children: [
    {
      targetId: '302',
      active: '00',
      name: '工信部百万申告率',
      ajaxIds: ['309', '302'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          {label: '移动工信部百万申告量月累计', score: null},
          {label: '移动工信部百万申告率月累计', score: null},
          {label: '移动工信部百万申告率月累计领先值', score: null, isCal: true}
        ]
      },
    },
    {
      targetId: '302',
      active: '10',
      name: '工信部百万申告率',
      ajaxIds: ['309', '302'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          {label: '移动工信部百万申告量月累计', score: null},
          {label: '移动工信部百万申告率月累计', score: null}
        ]
      }
    },
    {
      targetId: '305',
      active: '20',
      name: '工信部携号转网申告率',
      ajaxIds: ['306', '305'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          {label: '移动工信部携号转网申告量月累计', score: null},
          {label: '移动工信部携号转网申告率月累计', score: null},
          {label: '移动工信部携号转网申告率月累计领先值', score: null, isCal: true}
        ]
      },
    },
    {
      targetId: '305',
      active: '30',
      name: '工信部携号转网申告率',
      ajaxIds: ['306', '305'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          {label: '移动工信部携号转网申告量月累计', score: null},
          {label: '移动工信部携号转网申告率月累计', score: null}
        ]
      }
    },
    {
      targetId: '307',
      active: '40',
      name: '工信部营销宣传申告率',
      ajaxIds: ['308', '307'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          {label: '移动工信部营销宣传申告量月累计', score: null},
          {label: '移动工信部营销宣传申告率月累计', score: null},
          {label: '移动工信部营销宣传申告率月累计领先值', score: null, isCal: true}
        ]
      },
    },
    {
      targetId: '307',
      active: '50',
      name: '工信部营销宣传申告率',
      ajaxIds: ['308', '307'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          {label: '移动工信部营销宣传申告量月累计', score: null},
          {label: '移动工信部营销宣传申告率月累计', score: null}
        ]
      },
    },
  ]
};