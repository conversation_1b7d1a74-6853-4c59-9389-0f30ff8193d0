<template>
  <div class="login_grid">
    <van-empty image="network" :description="text" />
  </div>
</template>

<script>
import {gridLogin} from '@/api/login/index';
// import {init} from '@/utils/filter';
import getUserPermission from '@/utils/user'; // 获取用户权限信息
export default {
  name: 'login_grid',

  data() {
    return {
      text: ''
    };
  },

  mounted() {
    if (this.$route.path === '/login_grid') this.login();
  },

  methods: {
    login() {
      const {account, token} = this.$route.query;
      const params = {
        account :(account.replace(/\s/g,'+')), 
        token
      };
      gridLogin(params).then((v) => this.onLogin(v));
    },
    onLogin(v) {
      console.log(v);
      if (v.success) {
        sessionStorage.setItem('token-workflow', v.data.credentials);
        sessionStorage.setItem('userName', v.data.details.userid);

        Promise.all([getUserPermission(this, v.data.details.userid)]).then(() => {
          this.$router.replace('/home');
        }).catch((e) => {
          this.$Toast(e);
        });

        // init()
        //   .then(() => {
        //     getUserPermission(this).then(() => {
        //       this.$router.replace('/home');
        //     });
        //   })
        //   .catch((e) => {
        //     this.$Toast(e);
        //   });
      } else {
        this.text = v.msg;
        this.$Toast(v.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.login_grid {
  padding-top: 200px;
}
</style>