<template>
  <div class="me">登陆</div>
</template>
<script>
import {login, loginByOAToken} from '@/api/login';
import {init} from '@/utils/filter';
export default {
  name: 'Login',
  mounted() {},
  created() {
    console.log(this.$route);
    if (this.$route.path === '/login') {
      this.login();
    } else if (this.$route.path === '/login-oa') {
      console.log('this.$route.query...');
      console.log(this.$route.query);
      console.log(this.$route.query.redirect);
      const {redirect} = this.$route.query;
      console.log('xxx');
      console.log(redirect);
      const index = redirect.lastIndexOf('taskId');
      const string = redirect.substring(index);
      console.log(string);
      // eslint-disable-next-line no-unused-vars
      const array = string.split('=');
      console.log(array);
      // eslint-disable-next-line prefer-destructuring
      const taskId = array[1];
      console.log(taskId);
      sessionStorage.setItem('taskId', taskId);
      this.oaLogin();
    }
  },
  methods: {
    onLogin(v) {
      console.log(v);
      if (v.success) {
        sessionStorage.setItem('token-workflow', v.data.credentials);
        sessionStorage.setItem('userName', v.data.details.userid);
        init()
          .then(() => {
            this.$router.replace(this.$route.query.redirect);
          })
          .catch((e) => {
            console.log(e);
            this.$Toast(e);
          });
      } else {
        this.$Toast(v.msg);
      }
    },
    login() {
      login({
        userName: this.$route.query.userName,
        token: this.$route.query.token,
      }).then((v) => this.onLogin(v));
    },
    oaLogin() {
      loginByOAToken({
        token: this.$route.query.token || this.$route.query.ticket,
        // 工单来源 oa / 大音
        source: this.$route.query.source,
        usertoken: this.$route.query.usertoken,
      }).then((v) => this.onLogin(v));
    },
  },
};
</script>
