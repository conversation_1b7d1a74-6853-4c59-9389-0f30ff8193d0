<template>
    <van-empty image="network" :description="text" />
  </template>
<script>
export default {
  /**
   * 此页面的作用:用作统一跳转页
   * 之前的登陆跳转地址有4个,分别是
   * http://127.0.0.1:82/#/login_moa_group?appToken=jindongxun
   * http://127.0.0.1:82/#/login_moa?apiToken=xxx&data=VhdsPzNg3erbCl4A/ImjZcMrMMgrntzyl1acS9XikRCrW7WVy9RIloneXhtYAdPSaSIuKEGGkWwmJsYmMHslcg==
   * http://127.0.0.1:82/#/login_moa2?apiToken=xxx&uid=jindongxun
   * http://127.0.0.1:82/#/login_grid?token=xxx&account=TVt25ghZQ+kqRozRgoVsHg==
   * 但是moa的生产环境后台配置的跳转地址中不能带#,所以只能新建一个jump页面,并且jump页面是根路由(根路由在地址没有#),
   * 然后通过jump页面再跳转到之前的4个跳转地址,上面4个地址的根路由访问的地址分别是
   * http://127.0.0.1:82/?route=login_moa_group&appToken=jindongxun
   * http://127.0.0.1:82/?route=login_moa&apiToken=xxx&data=VhdsPzNg3erbCl4A/ImjZcMrMMgrntzyl1acS9XikRCrW7WVy9RIloneXhtYAdPSaSIuKEGGkWwmJsYmMHslcg==
   * http://127.0.0.1:82/?route=login_moa2&apiToken=xxx&uid=jindongxun
   * http://127.0.0.1:82/?route=login_grid&token=xxx&account=TVt25ghZQ+kqRozRgoVsHg==
   */
  name: 'jump',
  created() {
    /**
     * 此段程序的主要作用是将根路由地址
     * http://127.0.0.1:82/?route=login_moa_group&appToken=jindongxun
     * 转换为之前的路由地址
     * http://127.0.0.1:82/#/login_moa_group?appToken=jindongxun
     * 并跳转页面
     */
    const url = window.location.href;
    //url为   http://127.0.0.1:82/?route=login_moa_group&appToken=jindongxun#/jump
    console.log(url);
    const queryString = new URL(url).search;
    //queryString为   ?route=login_moa_group&appToken=jindongxun
    console.log(queryString);
    const params = new URLSearchParams(queryString); 
    const vue_url = '#/' + params.get('route') + queryString;
    //vue_url为    #/login_moa_group?route=login_moa_group&appToken=jindongxun 其实这里的route=login_moa_grou可以不要
    console.log(vue_url);
    // window.location.href = vue_url;
    window.location.href = window.location.origin+window.location.pathname + vue_url;
  }
};
</script>