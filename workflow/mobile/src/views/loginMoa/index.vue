<template>
  <div class="login_moa">
    <van-empty image="network" :description="text" />
  </div>
</template>

<script>
import {moaLogin} from '@/api/login/index';
import getUserPermission from '@/utils/user'; // 获取用户权限信息
export default {
  name: 'login_moa',

  data() {
    return {
      text: ''
    };
  },

  mounted() {
    if (this.$route.path === '/login_moa') this.login();
  },

  methods: {
    login() {
      // eslint-disable-next-line no-useless-escape
      const myCookie = document.cookie.replace(/(?:(?:^|.*;\s*)apiToken\s*\=\s*([^;]*).*$)|^.*$/, '$1');
      moaLogin({
        data:  (this.$route.query.data.replace(/\s/g,'+')),
        apiToken: myCookie,
      }).then((v) => this.onLogin(v));
    },
    onLogin(v) {
      console.log(v);
      if (v.success) {
        sessionStorage.setItem('token-workflow', v.data.credentials);
        sessionStorage.setItem('userName', v.data.details.userid);

        Promise.all([getUserPermission(this, v.data.details.userid)]).then(() => {
          this.$router.replace('/home');
        }).catch((e) => {
          this.$Toast(e);
        });;
        // init()
        //   .then(() => {
        //     console.log(123);
        //     this.$router.replace('/home');
        //   })
        //   .catch((e) => {
        //     this.$Toast(e);
        //   });
      } else {
        this.text = v.msg;
        this.$Toast(v.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.login_moa {
  padding-top: 200px;
}
</style>