<template>
  <div class="approval_wrap">
    <van-skeleton title avatar :row="18" :loading="loading">
      <template v-if="(taskHistory && taskHistory.length > 0)">
         <TrackingCard
          :track="item"
          v-for="item of taskHistoryOrder"
          :key="item.id"
          ref="collapse"
        >
        </TrackingCard>
      </template>
     
      <van-empty description="暂无数据" v-if="!(taskHistory && taskHistory.length > 0)"/>
    </van-skeleton>
  </div>
</template>

<script>
import {getHistoricTaskList} from '@/api/workflow/index';
// import collapseContent from './collapse-content.vue';
import TrackingCard from './processComponents/index';

export default {
  name: 'approval-opinions',
  components: {
    // collapseContent,
    TrackingCard,
  },
  props: ['processInfo'],
  data() {
    return {
      activeNames: [],
      loading: true,
      taskHistory: null,
    };
  },

  mounted() {
    this.showData();
  },
  methods: {
    showData() {
      getHistoricTaskList(
        {
          currentPage: 0,
          pageSize: 100,
        },
        {
          processInstanceId: this.processInfo.processInstanceId,
          processDefinitionKey: null,
          processVariables: {},
          /**
           * 如果是管理员这个参数才生效，表示显示所有人的
           */
          showAll: true,
          taskStatus: 'NONE',
          taskVariables: {},
          variableNames: null,
        }
      )
        .then((v) => {
          this.taskHistory = v.data.list;
          this.loading = false;
          const [confirmdata] = v.data.list.filter(
            (item) =>
              item.assignee === sessionStorage.getItem('userName') &&
              item.endTime === null
          );
          // 刷新的时候判断用户, 移动端好像暂时不用处理
          this.$emit('putconfirm', confirmdata);
        })
        .catch(() => {this.loading = false;});
    },
  },
  computed: {
    taskHistoryOrder() {
      // return this.taskHistory ? this.taskHistory?.reverse() : [];
      return this.taskHistory || [];
    },
  },
};
</script>

<style lang="scss" scoped>
.approval_wrap {
  margin: 13px 10px;
  margin-top: 0px;
  padding-bottom: 30px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 0.5px 6px 0px rgba(6, 28, 42, 0.06);
  .van-skeleton {
    padding-top: 25px;
  }
  .van-empty {
    height: 80vh;
  }
}
</style>
