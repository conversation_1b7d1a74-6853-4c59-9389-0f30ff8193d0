<template>
  <div class="tracking_wrap">
    <div class="left">
      <div class="avr">
        {{ $root.$options.filters.assignee(track.assignee).slice(0, 1) }}
      </div>
      <div
        class="line"
        :style="{ background: track.endTime ? '#4e92f2' : '#d4d7da' }"
      ></div>
    </div>
    <div class="right">
      <span class="name">{{ track.assignee | assignee }}</span>
      <ul>
        <li>
          <span class="header">流程节点：</span>
          <span class="footer">{{ track.name }}</span>
        </li>
        <li>
          <span class="header">受理类型：</span>
          <span class="footer">{{
            track.taskLocalVariables.acceptType || "-"
          }}</span>
        </li>

        <li>
          <span class="header">开始时间：</span>
          <span class="footer">{{
            track.createTime
              ? new Date(track.createTime)
              : null | dateFormat("yyyy/MM/dd HH:mm:ss")
          }}</span>
        </li>
        <li>
          <span class="header">结束时间：</span>
          <span class="footer">
            {{
              track.endTime
                ? new Date(track.endTime)
                : null | dateFormat("yyyy/MM/dd HH:mm:ss")
            }}</span
          >
        </li>
        <li>
          <van-collapse
            :border="false"
            v-model="activeNames"
            class="collapse_wrap_123"
          >
            <van-collapse-item title="处理意见：" name="1">
              <template>
                {{ track.taskLocalVariables.comment || "暂无数据" }}
              </template>
              <template #value>
                <div>{{ track.taskLocalVariables.comment || "-" }}</div>
              </template>
              <template #right-icon v-if="!track.taskLocalVariables.comment">
                {{ "" }}
              </template>
            </van-collapse-item>
          </van-collapse>
        </li>
        <li>
          <span class="header">附件：</span>
          <span class="footer">
            <template v-if="track.attachments.length > 0">
              <a
                v-for="item in track.attachments"
                :key="item.id"
                target="_blank"
                :href="
                  downloadUrlWork +
                  `?attachmentId=${item.id}&fileName=${encodeURIComponent(
                    item.name
                  )}`
                "
                >{{ item.name }}</a
              >
            </template>
            <template v-else> - </template>
          </span>
        </li>
        <li>
          <span class="header">状态：</span>
          <span
            class="footer state"
            :style="{ color: !track.endTime ? '#FF432B' : '' }"
            >{{ track.endTime ? "已处理" : "处理中" }}</span
          >
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
// import {dateFormat} from '@/utils/index.js';
export default {
  name: 'tracking-card',
  props: ['track'],
  data() {
    return {
      activeNames: [],
      downloadUrlWork: `${process.env.VUE_APP_REQUEST_URL}/workflow/attachment`,
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList`,
      downloadUrlReply: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downReplyAttachList`,
    };
  },

  mounted() {},

  methods: {
    showDetail(text) {
      this.$Toast(text);
    },
  },
};
</script>

<style lang="scss" scoped>
.tracking_wrap {
  padding-top: 20px;
  position: relative;
  font-family: PingFang SC, PingFang SC-Regular;
  display: flex;
  font-size: 14px;
  .left {
    width: 20%;

    .avr {
      margin: 0px auto;
      width: 44px;
      height: 44px;
      background: #4e92f2;
      text-align: center;
      font-size: 20px;
      line-height: 44px;
      color: #fefeff;
      border-radius: 50%;
    }
    .line {
      position: absolute;
      left: 36.5px;
      bottom: 0px;
      width: 2px;
      height: calc(100% - 80px);
      background: #4e92f2;
      border-radius: 1px;
    }
  }
  .right {
    width: 80%;
    ul {
      margin-top: 8px;
    }
    li {
      line-height: 32px;
    }
    .name {
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      color: #2b3034;
    }
    .header {
      font-weight: 400;
      text-align: left;
      color: #a5a5a5;
    }
    .footer {
      font-weight: 400;
      text-align: left;
      color: #4a5057;
    }
    .state {
      color: #00c813;
    }
  }
}
</style>
<style lang="scss">
.collapse_wrap_123 {
  .van-cell__title {
    flex: none;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    text-align: left;
    color: #a5a5a5;
    line-height: 32px;
  }
  .van-cell {
    padding: 0;
  }
  .van-cell__value {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    text-align: left;
    color: #4a5057;
    line-height: 32px;
    div {
      width: 88%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .van-collapse-item__content {
    color: #4a5057;
    font-size: 14px;
  }
  .van-cell__right-icon {
    margin-top: 2px;
    margin-right: 8px;
  }
}
</style>
