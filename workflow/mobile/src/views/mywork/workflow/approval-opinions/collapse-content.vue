<template>
  <div>
    <div v-if="row.endTime !== null" style="padding: 0">
      <el-card
        v-for="(item, index) of formDetail"
        :key="index"
        style="overflow-y: auto; height: 120px"
      >
        <div class="el-card__body__option">
          <div
            class="el-card__child_"
            v-for="(it, index) of item.childs[0]"
            :key="index"
            :style="{
              display:
                index === 0 || isShowItem(item.childs[0][0], it.id)
                  ? 'block'
                  : 'none',
            }"
          >
            <span v-if="index === 0 || isShowItem(item.childs[0][0], it.id)"
              >{{ it.name }}:
              {{
                geneVaule(it.value, it.options, it.params, it.type, it.id)
              }}</span
            >
          </div>
        </div>
        <div v-if="detail.length !== 0">
          <div v-for="(it, index) of detail[0].childs" :key="index">
            <span v-if="it.type === 'comment'"
              >处理意见：
              <Ecard :res="it"></Ecard>
            </span>
            <span v-if="it.type === 'file'"
              >附件：
              <a
                target="_blank"
                :href="
                  downloadUrlWork +
                  `?attachmentId=${it.id}&fileName=${encodeURIComponent(
                    it.name
                  )}`
                "
                >{{ it.name }}</a
              >
            </span>
            <span v-if="it.type === 'system'">日志:{{ it.fullMessage }} </span>
          </div>
          <p style="text-align: right">
            {{ detail[0].userId | assignee }} 提交于 {{ detail[0].time }}
          </p>
        </div>
      </el-card>
    </div>
    <div v-else>
      <p style="text-align: center">暂无处理数据</p>
    </div>
  </div>
</template>

<script>
import {dateFormat} from '@/utils/index.js';
import {getTaskDetail} from '@/api/workflow/index';
import Ecard from '@/components/common/Ecard/Ecard.vue';
import {allCode} from '@/utils/filter.js';
export default {
  name: 'CollapseContent',
  props: ['row'],

  components: {
    Ecard,
  },

  data() {
    return {
      formDetail: [],
      detail: [],
    };
  },

  mounted() {
    console.log('我开始挂载');
    this.loadTaskDetail();
  },

  methods: {
    // 处理受理后显示
    geneVaule(val, opt, params, type, id) {
      console.warn(params);
      if (type === 'deptSelect') {
        let tmpChild = allCode(id);
        if (tmpChild) {
          val.forEach((ele) => {
            [tmpChild] = tmpChild.filter((ele1) => ele1.value === ele);
            tmpChild.childs !== null && (tmpChild = tmpChild.childs);
          });
        }

        return tmpChild?.label;
      }
      if (opt) {
        if (!val) {
          return val;
        }
        return opt.filter((ele) => ele.id === val.toString())[0]?.name;
      } else if (params.hasOwnProperty('selectId')) {
        if (typeof val !== 'string') {
          return val;
        }
        if (params.selectId === 'problemType') {
          if (val.length === 4) {
            return allCode(params.selectId).filter((v) => v.value === val)[0]
              ?.label;
          } else if (val.length === 6) {
            const res = allCode(params.selectId).filter(
              (v) => v.value === val.substring(0, 4)
            );
            return `${res[0]?.label}/${
              res[0].childs[parseInt(val.substring(4, 6)) - 1]?.label
            }`;
          }
        }
        return allCode(params.selectId).filter((ele) => ele.value === val)[0]
          ?.label;
      } else if (params.hasOwnProperty('userRoleId')) {
        if (typeof val === 'object' && val !== null) {
          let label = '';
          val.forEach((item) => {
            label += ` ${
              allCode('user').filter((ele) => ele.value === item)[0]?.label
            }`;
          });
          return label;
        }
        return allCode('user').filter((ele) => ele.value === val)[0]?.label;
      }
      return val;
    },
    // 判断是否显示条目
    isShowItem(itemOne, id) {
      if (itemOne.params) {
        return itemOne.params.setVisible[itemOne.value].includes(id);
      }
      return false;
    },
    loadTaskDetail() {
      getTaskDetail({taskId: this.$parent.name}).then((v) => {
        console.log('详细', v.data);
        const arr = [
          ...v.data.attachments,
          ...v.data.comments,
          ...v.data.systemComments,
        ];
        const formArrs = [];
        // 按照时间分类数据
        const arrs = [];
        arr.forEach((item) => {
          const parent = arrs.find(
            (cur) =>
              dateFormat(new Date(cur.time), 'yyyy/MM/dd HH:mm') ===
              dateFormat(new Date(item.time), 'yyyy/MM/dd HH:mm')
          );
          if (parent) {
            parent.childs.push(item);
          } else {
            console.log('here');
            const obj = {
              time: dateFormat(new Date(item.time), 'yyyy/MM/dd HH:mm'),
              userId: item.userId,
              childs: [item],
            };
            arrs.push(obj);
          }
        });
        formArrs.push({
          id: v.data.formInfo.id,
          name: v.data.formInfo.name,
          key: v.data.formInfo.key,
          childs: [v.data.formInfo.formModel.fields],
        });
        console.log('分类后', arrs);
        console.log('分类后', formArrs);
        this.detail = arrs;
        this.formDetail = formArrs;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
