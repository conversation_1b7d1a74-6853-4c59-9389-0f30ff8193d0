<template>
  <van-popup v-model="value" position="right">
    <div class="detail_page">
      <div class="top_wrap">
        <van-nav-bar
          :title="processInfo.processVariables.title"
          left-arrow
          @click-left="onClickLeft"
        />
        <div class="header_wrap">
          <router-link
            v-for="item in detailInforLists"
            :to="item.route"
            :key="item.route"
            >{{ item.name }}</router-link
          >
        </div>
      </div>

      <div class="body_contain">
        <keep-alive>
          <router-view :processInfo="processInfo"></router-view>
        </keep-alive>
      </div>
    </div>
  </van-popup>
</template>

<script>
// 工作流详情页面
import Vue from 'vue';
import {NavBar} from 'vant';

Vue.use(NavBar);
export default {
  name: 'work-flow',
  data() {
    return {
      processInfo: {},
      value: true,
      detailInforLists: [
        {
          name: '基本信息',
          route: 'basic-information',
        },
        {
          name: '审批流程',
          route: 'approval-opinions',
        },
        // {
        //   name: '流程跟踪',
        //   route: 'process-tracking',
        // },
        // {
        //   name: '查看附件',
        //   route: 'view-attachments',
        // },
      ],
    };
  },

  created() {
    if (this.$route.params.id) {
      sessionStorage.setItem('processInfo', JSON.stringify(this.$route.params));
      this.processInfo = this.$route.params;
    } else {
      this.processInfo = JSON.parse(sessionStorage.getItem('processInfo'));
    }
  },

  methods: {
    onClickLeft() {
      this.$router.push('workflow-panel');
    },
  },
};
</script>

<style lang="scss" scoped>
.detail_page {
  height: 100vh;
  width: 100vw;
  background: #f8f9fb;
  .top_wrap {
    background: #fff;
    box-shadow: 0px 0.5px 6px 0px rgba(6, 28, 42, 0.06);
    .header_wrap {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 44px;
      font-weight: 400;
      text-align: center;
      color: #4a5057;
      line-height: 44px;
      font-size: 14px;
      a {
        color: #4a5057;
      }
      .router-link-active {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          bottom: 6px;
          left: calc(50% - 10px);
          width: 20px;
          height: 3px;
          background: #4e92f2;
          border-radius: 1.5px;
        }
        color: #4e92f2;
      }
    }
    .header_tab {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 44px;
      font-weight: 400;
      text-align: center;
      color: #4a5057;
      line-height: 44px;
      .tab_item {
        font-size: 14px;
        color: #4a5057;
        &.active {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: 6px;
            left: calc(50% - 10px);
            width: 20px;
            height: 3px;
            background: #4e92f2;
            border-radius: 1.5px;
          }
          color: #4e92f2;
        }
      }
    }
  }
}
.body_contain {
  background: #f8f9fb;
  box-sizing: border-box;
  // padding: 15px 0px 87px 0px;
  padding: 15px 0px 15px 0px;
}
</style>
