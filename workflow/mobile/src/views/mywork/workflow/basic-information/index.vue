<template>
  <div class="basic_information_wrap">
    <van-skeleton title :row="18" :loading="loading">
      <div v-if="detailfields.length > 0">
        <!-- 通用项 -->
        <van-cell title="工单标题：" :is-link="false">
          <template>
            {{ processInfo.processVariables.identyType | identyType }}
          </template>
        </van-cell>
        <van-cell title="工单子类型：" :is-link="false">
          <template>
            {{ processInfo.processVariables.identySubtype | identySubtype }}
          </template>
        </van-cell>
        <!-- 动态详情项 -->
        <van-cell
          v-for="item of detailForm"
          :key="item.id"
          :title="`${item.name}：`"
          :is-link="false"
        >
          <template>
            <component
              :is="`detailItem_${item.type}`"
              :info="processInfo.processVariables[item.id]"
              :form="item"
            ></component>
          </template>
        </van-cell>
      </div>
      <van-empty description="暂无数据" v-if="!(detailfields.length > 0)" />
    </van-skeleton>
    
  </div>
</template>

<script>
import {getDetailForm} from '@/api/workflow/index';
import modules from '@/components/dynamic/detailItem.js';
export default {
  name: 'basic-information',
  props: ['processInfo'],
  components: {
    ...modules,
  },
  data() {
    return {
      detailfields: [],
      loading: true,
    };
  },

  created() {
    getDetailForm({
      processDefinitionProcessId: this.processInfo.processDefinitionId,
    }).then((res) => {
      if (res.data.formModel) {
        this.detailfields = res.data.formModel.fields;
        // this.$emit('detailInfo',res.data);
        // 2023 gkh 修改 工单类型为 通用版任务单 且 是否需要回复 为不回复 在受理的时候隐藏 回复集团 下拉选项
        const info = res.data;
        console.log('this.processInfo:', this.processInfo);
        if (info.name === '通用版任务单') {
          if (info && info.formModel && Array.isArray(info.formModel.fields)) {
            info.formModel.fields.forEach((i) => {
              if (
                i.id === 'isReplyCss' &&
                (i.params.showType === '0' || i.params.showType === '1')
              ) {
                console.log(
                  '=======>',
                  this.processInfo.processVariables['isReplyCss']
                );
                if (this.processInfo.processVariables['isReplyCss'] === '1') {
                  // 在点击受理时 隐藏回复集团 选项
                  this.$emit('hiddenOptionReplyGroup', true);
                }
              }
            });
          }
        }
        this.loading = false;
      }
    }).catch(() => this.loading = false);
  },

  computed: {
    detailForm() {
      const orderData =
        this.detailfields === null
          ? []
          : this.detailfields.filter(
            (item) =>
              item.params.showType === '2' || item.params.showType === '0'
          );
      orderData.sort((a, b) => a.params.order - b.params.order);
      console.log(orderData);
      return orderData;
    },
  },

  methods: {},
};
</script>

<style lang="scss" scoped>
.van-cell__value {
  flex: 2;
  text-align: left;
}
.van-cell__title {
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  color: #a5a5a5;
}
.van-cell__value {
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  color: #4a5057;
}
.basic_information_wrap {
  background: #fff;
}
.van-skeleton {
  padding-top: 25px;
}
.van-empty {
    height: 80vh;
  }
</style>
