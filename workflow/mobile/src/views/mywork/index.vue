<template>
  <div class="work_panel">
    <div class="header">
      <!-- 工单状态 -->
      <div class="header_tab">
        <span
          class="tab_item"
          v-for="item in tabLists"
          :key="item.key"
          @click="checkTab(item)"
          :class="{ active: item.value === taskvalue }"
        >
          {{ item.name }}
          
        </span>
        <van-icon name="arrow-left" class="left_icon" @click="$router.push('/home')"/>
      </div>
      <!-- 搜索工单 -->
      <div class="work_search">
        <van-search
          v-model="searchVariables.identifier"
          @search="inquiryFlag"
          @clear="inquiryFlag"
          shape="round"
          placeholder="请输入工单编号"
        />
      </div>
      <!-- 工单类型 -->
      <div class="work_type_wrap">
        <van-dropdown-menu active-color="#4e92f2">
          <van-dropdown-item
            v-model="searchVariables.identyType"
            @change="changeIdenty"
            :options="identyTypeData"
          />
          <van-dropdown-item
            @change="inquiryFlag"
            v-model="searchVariables.identySubtype"
            :options="identySubtypeData"
          />
        </van-dropdown-menu>
      </div>
    </div>

    <!-- 展示查询的工单 -->
    <div class="panel_body" ref="list">
      <div ref="list-wrap">
        <div class="panel_item_wrap" v-for="item of tableData" :key="item.id">
          <Card :msg="item.processVariables" @jump="jump(item)">
            <template>
              <div class="footer_wrap" v-if="taskvalue === 'backlog'">
                <span
                  @click="
                    activedCardItem = item;
                    assignShow = true;
                  "
                  ><van-icon size="14" name="revoke" />转派</span
                >
                <span @click="handle(item)"
                  ><van-icon size="14" name="records" />受理</span
                >
              </div>
              <div class="footer_wrap" v-else>
                <span @click="jump(item)"
                  ><van-icon size="14" name="comment-o" />详情</span
                >
              </div>
            </template>
          </Card>
        </div>
        <van-empty description="暂无工单" v-if="!(tableData?.length > 0)" />
        <div class="loading_text" v-if="tableData?.length > 0">
          {{ loading && !finished ? "加载中..." : "没有更多了" }}
        </div>
      </div>
    </div>
    <!-- 转派 -->
    <AssignTemplate
      v-model="assignShow"
      :taskId="activedCardItem?.id"
    ></AssignTemplate>

    <!-- 受理 -->
    <AcceptTemplate v-model="acceptShow" :acceptData="activedCardItem">
    </AcceptTemplate>
    <!-- 详情 -->
    <router-view></router-view>
  </div>
</template>

<script>
// 面板主页面
import {getTaskFormInfo, TaskList} from '@/api/workflow/index';
import Vue from 'vue';
import {
  Search,
  DropdownMenu,
  DropdownItem,
  Popup,
  Form,
  Field,
  Picker,
} from 'vant';
Vue.use(Search).use(DropdownMenu).use(DropdownItem);
Vue.use(Popup).use(Form).use(Field).use(Picker);
import Card from '@/components/common/Card/index.vue';
import AssignTemplate from '@/components/common/Assign/assign-template.vue';
import AcceptTemplate from '@/components/common/Accept/accept-template.vue';
export default {
  name: 'my-work-panel',
  components: {
    Card,
    AssignTemplate,
    AcceptTemplate,
  },
  data() {
    return {
      // 当前编辑的工单
      activedCardItem: null,
      loading: false,
      finished: false,
      // 默认待办
      taskvalue: 'backlog',
      searchVariables: {
        identifier: null,
        title: null,
        identyType: null,
        identySubtype: null,
      },
      total: 0,
      pagesize: 10,
      currentPage: 1,
      tabLists: [
        {
          key: 0,
          name: '待办',
          value: 'backlog',
          num: '',
          // src: require('../../assets/icon/icon-1.png'),
          visible: true,
        },
        // {
        //   key:1,
        //   name: '未完成任务',
        //   value:'unfinished',
        //   num:'',
        //   visible: false
        // },
        // {
        //   key:2,
        //   name: '逾期任务',
        //   value:'overdue',
        //   num:'',
        //   visible: false
        // },
        // {
        //   key:3,
        //   name: '完成任务',
        //   value:'finished',
        //   num:'',
        //   visible:false
        // },
        // {
        //   key:4,
        //   name: '创建任务',
        //   value:'create',
        //   num:'',
        //   visible:false
        // },
        {
          key: 6,
          name: '待阅',
          value: 'sendCopy',
          num: '',
          visible: true,
        },
        // {
        //   key:5,
        //   name: '全部任务',
        //   value:'count',
        //   num:'',
        //   visible:false
        // }
      ],

      // 数据
      tableData: [],
      // 转派
      assignShow: false,
      // 受理
      acceptShow: false,
    };
  },

  mounted() {
    this.handleLoading();
    const {list} = this.$refs;
    list.addEventListener('scroll', this.handleLoading);
  },

  beforeDestroy() {
    const {list} = this.$refs;
    list.removeEventListener('scroll', this.handleLoading);
  },

  computed: {
    // 获取工单类型数据
    identyTypeData() {
      const data = JSON.parse(sessionStorage.getItem('identyType')).map(
        (item) => ({
          value: item.value,
          text: item.label,
        })
      );
      data.unshift({value: null, text: '全部类型'});
      return data;
    },
    // 获取工单子类型数据
    identySubtypeData() {
      let data = [];
      JSON.parse(sessionStorage.getItem('identyType')).forEach((item) => {
        if (this.searchVariables.identyType === item.value) {
          data = item.children;
        }
      });
      data.unshift({value: null, label: '全部子类型'});
      return data.map((item) => ({
        value: item.value,
        text: item.label,
      }));
    },
  },

  methods: {
    // 切换工单状态
    checkTab(item) {
      this.taskvalue = item.value;
      this.inquiryFlag();
    },

    // 处理加载
    handleLoading() {
      const {list} = this.$refs;
      const scrollHeight = list.scrollHeight - list.clientHeight;
      if (scrollHeight - list.scrollTop < 30) {
        if (!(this.loading || this.finished)) this.inquiry(true);
        return;
      }
      
      // 未铺满情况
      this.$nextTick(function () {
        if (list.scrollHeight - list.clientHeight === 0 && !this.finished) {
          this.inquiry(true);
        }
      });
    },
    // 工单详情跳转
    jump(item) {
      this.$router.push({name: 'basic-information', params: item});
      this.activedCardItem = item;
    },

    // 改变工单类型
    changeIdenty() {
      this.searchVariables.identySubtype = null;
      this.inquiryFlag();
    },
    inquiryFlag() {
      this.currentPage = 1;
      this.pagesize = 10;
      this.inquiry();
      const {list} = this.$refs;
      list.scrollTo(0, 0);
    },
    /**
     * @Author: fishDre
     * @description: 工单列表回调
     * @param {*} isList 判断是列表触底回调, 还是条件判断回调, 在触底回调中, 数据未铺满视图, 会继续回调
     * @return {*}
     */
    inquiry(isList) {
      this.loading = true;
      this.finished = false;
      // 由于组件引起的问题
      if (this.taskvalue !== '0' && !!this.taskvalue) {
        // this.loading = true;
        this.searchVariables.identifier =
          this.$route.query.id || this.searchVariables.identifier;
        console.log(this.timeValue);
        // 获取任务列表
        TaskList(
          {currentPage: this.currentPage - 1, pageSize: this.pagesize},
          {
            name: this.taskvalue,
            showAll: false,
            processVariables: {
              // 完成任务 扩建任务的时间参数
              // startTime: this.timeValue ? this.timeValue[0] : '',
              // endTime: this.timeValue ? this.changeDate(this.timeValue[1]) : '',
              identifier:
                this.searchVariables.identifier &&
                `%${this.searchVariables.identifier}%`,
              title:
                this.searchVariables.title &&
                // ['%', this.searchVariables.title, '%'].join(''),
                `%${this.searchVariables.title}%`,
              identySubtype: this.searchVariables.identySubtype,
              identyType: this.searchVariables.identyType,
            },
          }
        )
          .then((data) => {
            this.total = data.data.total;
            console.log(this.taskvalue);
            console.log(data);
            // 针对扩建任务的数据来源特殊处理
            if (this.taskvalue === 'create') {
              data.data.list.forEach((element) => {
                element.processVariables = element;
              });
            }
            // 滚动处理
            if (isList) {
              this.tableData = [...this.tableData, ...data.data.list];
              this.$nextTick(() => this.handleLoading());
            } else {
              this.tableData = data.data.list;
            }
            // 通用处理
            this.currentPage++;
            // 加载状态结束
            this.loading = false;
            // 数据全部加载完成
            if (this.tableData.length >= this.total) {
              this.finished = true;
            }
            console.log('列表数据', this.tableData);

            if (this.tableData.length !== 0) {
              const row = this.tableData.filter((item) => {
                if (
                  item.processVariables.identifier !== undefined &&
                  this.$route.query.id !== undefined
                ) {
                  return (
                    item.processVariables.identifier === this.$route.query.id
                  );
                }
                return false;
              });
              if (row.length > 0) {
                console.log(row[0]);
                // 根据url的参数打开详情, 移动端暂时不支持
                // this.showDetail(row[0]);
                this.$router.push({query: {}});
              }
            } else {
              this.$Toast('没有找到该工单或工单已被处理');
              this.$router.push({query: {}});
            }
          })
          .catch((error) => {
            console.warn('错误', error);
            this.loading = false;
          });
      }
      return;
    },
    // 受理回调, 获取表单数据
    handle(row) {
      getTaskFormInfo({taskId: row.id}).then((v) => {
        const {data} = v;
        this.$set(row, 'formInfo', data);
        this.activedCardItem = row;
        this.acceptShow = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.work_panel {
  width: 100%;
  height: 100vh;
  background: #fff;
  .header {
    height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .header_tab {
      background: #2f94f9;
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 44px;
      font-weight: 400;
      text-align: center;
      color: #4a5057;
      line-height: 44px;
      .left_icon {
        position: absolute;
        color: #fff;
        left: 15px;
      }
      .tab_item {
        font-size: 14px;
        color: #fff;
        &.active {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: 6px;
            left: calc(50% - 10px);
            width: 20px;
            height: 3px;
            background: #fff;
            border-radius: 1.5px;
          }
          color: #fff;
        }
      }
    }
    // .work_search {

    // }
    .work_type_wrap {
      ::v-deep .van-dropdown-menu__bar {
        box-shadow: 0 2px 2px rgba(100, 101, 102, 0.12) !important;
      }
    }
  }
  .panel_body {
    overflow-y: scroll;
    background: #f8f9fb;
    height: calc(100vh - 150px);
    .panel_item_wrap {
      text-align: center;
      background: #fff;
      margin: 15px 10px;
    }
    .loading_text {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      text-align: center;
    }
  }
}
.footer_wrap {
  height: 40px;
  border-top: 1.5px solid #d1d4d8;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: left;
  color: #4e92f2;
  letter-spacing: 3px;
}
</style>
