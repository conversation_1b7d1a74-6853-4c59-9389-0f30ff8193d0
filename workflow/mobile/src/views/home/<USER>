<template>
  <div class="home_wrap">
    <header>
      {{ title }}
    </header>
    <div class="panel_box">
       <div class="main_panel">
          <div class="header_wrap">
              <BaseTitle>
                <span class="index_style">指标看板</span>
              </BaseTitle>
          </div>

          <div class="content_wrap">
              <div class="content_item" v-for="item in indexList" 
              @click="jumpIndex(item.path)"
              :key="item.targetKpiId">
                <img :src="item.icon" alt="">
                <span class="text_bt">{{item.name}}</span>
              </div> 
              
          </div>
       </div>
    </div>

    <div class="workflow_panel">
          <div class="header_wrap">
              <BaseTitle isJump @onJump='jump'>
                <span class="index_style">工作流</span>
              </BaseTitle>
          </div>
       </div>
  </div>
</template>

<script>
import {init} from '@/utils/filter';
export default {
  name: 'Home',
  data() {
    return {
      title: '大音平台',
      indexList: [
        {
          name: '综合满意度',
          targetKpiId: 'KPI01',
          path: '/satisfaction',
          icon: require('@/assets/img/top1.png')
        },
        {
          name: '工信部申告',
          targetKpiId: 'KPI02' ,
          path: '/declaration',
          icon: require('@/assets/img/top2.png')
        },
        {
          name: '用后即评',
          targetKpiId: 'KPI04' ,
          path: '/appraise',
          icon: require('@/assets/img/top3.png')
        },
        {
          name: '投诉满意度',
          targetKpiId: 'KPI03' ,
          path: '/complaints',
          icon: require('@/assets/img/top4.png')
        },
        {
          name: '我的指标',
          targetKpiId: 'KPI05' ,
          path: '/myindex',
          icon: require('@/assets/img/top1.png')
        }
      ]
    };
  },

  mounted() {
    // 获取码表数据
    init();
  },

  methods: {
    jump() {
      this.$router.push('/workflow_panel');
      // this.$toast('暂未开放');
    },
    jumpIndex(path) {
      this.$router.push(path);
    }
  },
};
</script>

<style lang="scss" scoped>
 .panel_box {
    margin-top: 12px;
    background: #fff;
    padding-top: 16px;
    .index_style {
      font-size: 16px;
      font-weight: 500;
    } 
    .content_wrap {
      margin-top: 22px;
      padding-bottom: 56px;
      display: flex;
      justify-content: center;
      .content_item {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0px 13.5px;
        width: 59px;
        height: 59px;
        border-radius: 8px;
        background: #FE9900;
        .text_bt {
          display: block;
          width: 120%;
          text-align: center;
          white-space: nowrap;
          position: absolute;
          bottom: -25px;
          left: -10%;
          font-family: Source Han Sans CN;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: 0px;
          color: #4D4D4D;
        }
      }
    }
  }

  .workflow_panel {
     margin-top: 12px;
     background: #fff;
     padding: 10px 0px;
     .index_style {
      font-size: 16px;
      font-weight: 500;
    } 
  }
</style>
