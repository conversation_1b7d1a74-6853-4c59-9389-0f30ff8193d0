<template>
  <div class="login_moa">
    <van-empty image="network" :description="text" />

  </div>
</template>

<script>
import {moaLogin2} from '@/api/login/index';
import getUserPermission from '@/utils/user'; // 获取用户权限信息
// import {init} from '@/utils/filter';
export default {
  name: 'login_moa',

  data() {
    return {
      text: ''
    };
  },

  mounted() {
    if (this.$route.path === '/login_moa2') this.login();
  },

  methods: {
    login() {
      // eslint-disable-next-line no-useless-escape
      const myCookie = this.$route.query.apiToken;
      moaLogin2({
        uid:  (this.$route.query.uid.replace(/\s/g,'+')),
        apiToken: myCookie,
      }).then((v) => this.onLogin(v));
    },
    onLogin(v) {
      console.log(v);
      if (v.success) {
        sessionStorage.setItem('token-workflow', v.data.credentials);
        sessionStorage.setItem('userName', v.data.details.userid);

        Promise.all([getUserPermission(this, v.data.details.userid)]).then(() => {
          this.$router.replace('/home');
        }).catch((e) => {
          this.$Toast(e);
        });;

        // init()
        //   .then(() => {
        //     getUserPermission(this).then(() => {
             
        //     });
        //   })
        //   .catch((e) => {
        //     this.$Toast(e);
        //   });
      } else {
        this.text = v.msg;
        this.$Toast(v.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.login_moa {
  padding-top: 200px;
}
</style>