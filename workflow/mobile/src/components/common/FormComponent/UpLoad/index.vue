<template>
  <van-field name="uploader" label="文件上传："> 
   
    <template #input> 
      <van-button icon="plus" native-type="button"  type="primary" size="small" @click="noUpload"
      >上传文件</van-button>
      <!-- <van-uploader
        accept=""
        :max-count="1"
        result-type="file"
        v-model="fileList"
        disabled
        :max-size="20 * 1024 * 1000"
        @oversize="onOversize"
        @click-upload="noUpload"
        :after-read="afterRead"
        :before-delete="deleteFile"
      >
      </van-uploader> -->
    </template>
  </van-field>
</template>

<script>
import Vue from 'vue';
import axios from 'axios';
import {Field, Uploader, Button} from 'vant';
Vue.use(Field).use(Uploader).use(Button);
export default {
  name: 'upload-form',
  data() {
    return {
      fileList: [],
    };
  },

  mounted() {},

  methods: {
    noUpload() {
      this.$Toast('注：手机端目前不支持上传附件，如有需要请在PC端上操作');
    },
    onOversize(file) {
      console.log(file);
      this.$Toast('文件大小不能超过 20M');
    },
    afterRead(fileInfo) {
      // 此时可以自行将文件上传至服务器
      console.log('上传的文件', fileInfo);
      console.log('文件数组', this.fileList);
      fileInfo.status = 'uploading';
      fileInfo.message = '上传中...';

      let formData = new FormData();
      formData.append('file', fileInfo.file, fileInfo.file.name);
      axios({
        method: 'post',
        url: '/staticresource/FileUploadController/upload',
        data: formData,
      })
        .then((res) => {
          if (res.code === '0') {
            // 表单校验阶段对上传失败情况进行处理
            fileInfo.filePath = res.data;
            fileInfo.status = 'done';
            fileInfo.message = '上传成功';
          } else {
            fileInfo.status = 'failed';
            fileInfo.message = '上传失败';
          }
        })
        .catch((error) => {
          console.log(error);
          fileInfo.status = 'failed';
          fileInfo.message = '上传失败';
        });
      formData = null;
    },
    deleteFile() {
      return true;
    },
  },
};
</script>

<style lang="scss" scoped></style>
