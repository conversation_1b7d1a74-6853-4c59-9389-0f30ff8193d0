<template>
  <div class="card_wrap">
    <div class="header_wrap" @click="jump()">{{ msg.title }}</div>
    <div class="body_wrap">
      <ul class="body_list">
        <li><span>工单子类型：</span> {{ msg.identySubtype | identySubtype }}</li>
        <li><span>工单编号：</span> {{ msg.identifier }}</li>
        <!-- <li><span>工单:</span> 预警单</li> -->
      </ul>
      <div class="hr"></div>
    </div>

    <slot name="default" :msg="msg"></slot>
  </div>
</template>

<script>
export default {
  name: 'Card',
  props: ['msg'],

  data() {
    return {};
  },

  mounted() {},

  methods: {
    jump() {
      this.$emit('jump', '');
    },
  },
};
</script>

<style lang="scss" scoped>
.card_wrap {
  // height: 156px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 0.5px 6px 0px rgba(6, 28, 42, 0.06);
  .header_wrap {
    padding: 6px 12.5px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: #2b3034;
    line-height: 24px;
    
    letter-spacing: -0.31px;
  }
  .body_wrap {
    padding-bottom: 8px;
    .body_list {
      padding-left: 12.5px;
      li {
        font-size: 14px;
        line-height: 32px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: left;
        color: #4a5057;
        span {
          color: #a5a5a5;
        }
      }
    }
  }
}
</style>
