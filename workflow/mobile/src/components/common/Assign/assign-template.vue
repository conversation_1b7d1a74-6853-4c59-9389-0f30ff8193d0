<template>
  <div>
    <!-- 转派弹框 -->
    <van-popup :value="value" @input="onChange" position="left">
      <div class="assign_content">
        <van-nav-bar title="转派" left-arrow @click-left="onChange(false)" />
        <van-form ref="form">
          <van-field
            v-model="positionValue.text"
            is-link
            readonly
            label="部门："
            placeholder="请选择所在部门"
            @click="positionOpen = true"
            :rules="[{ required: true }]"
          />
          <van-field
            v-model="assignUserInfo.assignUserName"
            is-link
            readonly
            label="人员："
            placeholder="请选择转派人"
            @click="userOptionsOpen = true"
            :rules="[{ required: true }]"
          />
        </van-form>
        <FooterButton :config="buttonOption"></FooterButton>
      </div>
    </van-popup>

    <!-- 转派人员选择 -->
    <van-popup v-model="positionOpen" round position="bottom">
      <van-picker
        ref="picker"
        title="请选择"
        value-key="label"
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </van-popup>
    <van-popup v-model="userOptionsOpen" round position="bottom">
      <van-picker
        v-if="userOptions.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="userOptions"
        @confirm="onConfirmOther"
        @cancel="onCancelOther"
      />
      <van-empty description="暂无数据" v-if="userOptions.length === 0" />
    </van-popup>
  </div>
</template>

<script>
import {assigneeTask} from '@/api/workflow/index';
import {
  // getUserList,getDepartmentChilds,
  getDepartmentUsers,
} from '@/api/common';
import FooterButton from '@/components/common/FooterButton/index.vue';
export default {
  name: 'assign-template',
  props: ['value', 'taskId'],
  components: {
    FooterButton,
  },
  data() {
    return {
      positionValue: {
        value: null,
        text: null,
      },
      positionOpen: false,
      userOptionsOpen: false,
      assignUserInfo: {
        assignUserId: null,
        assignUserName: null,
      },
      // 级联选择
      columns: [],
      userOptions: [],
      // 按钮配置
      buttonOption: [
        {
          label: '取消',
          style: {
            color: '#2b3034',
            background: '#fff',
          },
          emit: () => this.handleAssign('', this.onChange),
        },
        {
          label: '确定',
          style: {
            color: '#fff',
            background: '#2f94f9',
          },
          emit: () => this.handleAssign('confirm', this.onChange),
        },
      ],
    };
  },

  mounted() {
    // 初始化公司/部门信息
    const {dept} = JSON.parse(localStorage.getItem('dic'));
    const maxDeep = this.getDepths(dept[0], 0, []);
    // 补全深度
    this.handleDepth(dept[0], 0, maxDeep);

    console.log(dept);
    this.columns = dept;
  },

  methods: {
    clearInfo() {
      this.positionValue = {
        value: null,
        text: null,
      };
      this.assignUserInfo = {
        assignUserId: null,
        assignUserName: null,
      };
      this.userOptions = [];
    },
    /**
     * @Author: fishDre
     * @description: 递归处理, 根据数据深度补全不足的数据
     * @param {*} dept
     * @return {*}
     */
    handleDepth(root, deep, maxDeep) {
      // 边界处理
      if (root !== null) deep++;
      else return 0;

      // 匹配组件属性
      root.children = root.childs;

      if (
        root.children === null ||
        (Array.isArray(root.children) && root.children.length === 0)
      ) {
        root.children = null;
        // 深度对比
        if (deep < maxDeep) {
          const count = maxDeep - deep;
          let target = root;
          for (let index = 0; index < count; index++) {
            target.children = [];
            target.children.push({
              label: '默认',
              children: null,
            });
            // eslint-disable-next-line prefer-destructuring
            target = target.children[0];
          }
        }
      } else {
        // 给每一项增加默认值
        root.childs.unshift({
          label: '默认',
          childs: [],
        });
        for (const item of root.childs) {
          this.handleDepth(item, deep, maxDeep);
        }
      }
    },

    /**
     * @Author: fishDre
     * @description: 获取树形的深度
     * @param {*} root
     * @param {*} deep
     * @param {*} deepArr
     * @return {*}
     */
    getDepths(root, deep, deepArr) {
      if (root !== null) deep++;
      else return 0;
      // 到底了
      if (
        root.childs === null ||
        (Array.isArray(root.childs) && root.childs.length === 0)
      ) {
        // 记录深度
        deepArr.push(deep);
      } else {
        for (const item of root.childs) {
          this.getDepths(item, deep, deepArr);
        }
      }
      return Math.max(...deepArr);
    },
    /**
     * @Author: fishDre
     * @description: 表单验证 ==> 发起转派请求 ==> 根据返回值处理弹框
     * @param {*} action 按钮类型
     * @param {*} done 调用关闭弹框
     * @return {*}
     */
    handleAssign(action, done) {
      if (action === 'confirm') {
        // 表单验证
        this.$refs.form
          .validate()
          .then(() => {
            // 发起请求
            assigneeTask({
              taskId: this.taskId,
              newUserName: this.assignUserInfo.assignUserId,
              // remark: this.assignComment,
              // filePath: fileUrl,
              // fileName
            })
              .then((value) => {
                if (value.code === '0') {
                  // 更新列表
                  this.$parent.inquiryFlag();
                  // this.onProcessComplete(true);
                  this.$Toast('转派成功');
                  this.clearInfo();
                  done();
                } else {
                  this.$Toast(`转派失败${value.msg}`);
                  done(true);
                }
              })
              .catch(() => {
                done(true);
                this.$Toast('转派失败');
              });
          })
          .catch(() => {
            done(true);
          });
      } else {
        this.clearInfo();
        done(false);
      }
    },
    onChange(val) {
      this.$emit('input', val);
    },
    // 级联获取
    onConfirm() {
      const target = this.$refs.picker.getValues();
      console.log(target);
      const {length} = target;
      this.positionValue = {
        value: null,
        text: null,
      };
      for (let index = length - 1; index >= 0; index--) {
        const element = target[index];
        if (element.label !== '默认' && this.positionValue.value === null) {
          this.positionValue = {
            text: element.label,
            value: element.value,
          };
        }
      }
      // 清空
      this.userOptions = [];
      this.assignUserInfo = {
        assignUserId: null,
        assignUserName: null,
      };
      // 请求人员数据
      getDepartmentUsers({
        deptId: this.positionValue.value,
        deptLevel: '',
        userName: '',
      })
        .then((res) => {
          this.userOptions = res.data || [];
        })
        .finally(() => {
          this.positionOpen = false;
        });
    },
    onConfirmOther(item) {
      this.assignUserInfo = {
        assignUserId: item.value,
        assignUserName: item.label,
      };
      this.userOptionsOpen = false;
    },
    onCancel() {
      this.positionOpen = false;
    },
    onCancelOther() {
      this.userOptionsOpen = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.assign_content {
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  padding-bottom: 75px;
  overflow: scroll;
}
</style>
