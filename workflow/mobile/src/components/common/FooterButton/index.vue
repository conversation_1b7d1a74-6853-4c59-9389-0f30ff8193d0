<template>
  <div class="button_wrap_all">
    <div
      v-for="item in config"
      :key="item.label"
      :style="item.style"
      @click="item.emit()"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'footer-button',
  props: ['config'],
  data() {
    return {};
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.button_wrap_all {
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 72px;
  font-size: 16px;
  background: #ffffff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  div {
    width: 166px;
    height: 44px;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 25px;
  }
  :first-child {
    border: 0.5px solid rgba(0, 0, 0, 0.15);
  }
}
</style>
