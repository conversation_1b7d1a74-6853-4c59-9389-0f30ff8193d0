<template>
  <div class="card_wrap">
    <p v-html="res.message.replace(/\n/g, '<br>')" v-if="showMessage"></p>
    <p v-html="res.fullMessage.replace(/\n/g, '<br>')" v-else></p>
    <div v-show="res.fullMessage !== res.message" class="meg_wrap">
      <span @click="showMessage = !showMessage" class="text_fold">{{
        showMessage ? "展开全部" : "收起"
      }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Ecard',
  props: {
    res: Object,
  },

  data() {
    return {
      showMessage: true,
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped>
.card_wrap {
  padding: 0px !important;
  .meg_wrap {
    text-align: left;
    padding: 0px;
  }
  .text_fold {
    color: rgba(247, 138, 28, 1);
    cursor: pointer;
  }
}
</style>
