<template>
  <div>
    <!-- 受理弹框 -->
    <van-popup :value="value" @input="onChange" position="right">
      <div class="accept_content">
        <van-nav-bar title="受理" left-arrow @click-left="onChange(false)" />
        <van-form ref="acceptForm" class="form_wrap" v-if="value">
          <!-- 动态表单项 -->
          <template
            v-for="item of acceptData &&
            acceptData.formInfo &&
            acceptData.formInfo.formModel.fields"
          >
            <component
              v-if="!item.params || !item.params.hidden"
              :is="'formItem-' + item.type"
              :data="variabledata[item.id]"
              :info="variabledata"
              :key="item.id"
              :validate="item.id"
              :fieldInfo="item"
              :formdata="acceptData"
              :handlerList="deptHandlerList"
              :createdTime="acceptData.createTime?.split('T')[0]"
              @change="formItemChange($event, item.id, item.type, item)"
              @setVisible="setVisible"
            ></component>
          </template>
          <!-- 处理意见 -->
          <van-field
            v-model="variabledata.comment"
            name="comment"
            validate="comment"
            rows="1"
            autosize
            required
            label="处理意见："
            type="textarea"
            placeholder="请输入处理意见"
            :rules="[{ required: true, message: `处理意见不能为空` }]"
          />
          <!-- 附件上传 -->
          <UpLoader
            ref="uploader"
            v-if="acceptData?.formInfo?.formModel?.description !== 'no_upload'"
          ></UpLoader>
        </van-form>
        <FooterButton :config="buttonOption"></FooterButton>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue';
import {Popup, Form, Field, Empty, Picker, Uploader, Button} from 'vant';
import UpLoader from '@/components/common/FormComponent/UpLoad/index.vue';
import modules from '@/components/dynamic/form.js';
import {completeTask} from '@/api/workflow/index';
import {getDepartmentUsers, getByUrl, postByUrl} from '@/api/common/index.js';
import FooterButton from '@/components/common/FooterButton/index.vue';
Vue.use(Popup)
  .use(Form)
  .use(Field)
  .use(Picker)
  .use(Empty)
  .use(Uploader)
  .use(Button);
export default {
  name: 'accept-template',
  props: ['value', 'acceptData'],
  components: {
    UpLoader,
    ...modules,
    FooterButton,
  },
  data() {
    return {
      // 表单数据项
      variabledata: {
        comment: '',
      },
      deptHandlerList: [],
      // 按钮配置
      buttonOption: [
        {
          label: '取消',
          style: {
            color: '#2b3034',
            background: '#fff',
          },
          emit: () => this.handleAccept('', this.onChange),
        },
        {
          label: '确定',
          style: {
            color: '#fff',
            background: '#2f94f9',
          },
          emit: () => this.handleAccept('confirm', this.onChange),
        },
      ],
    };
  },

  mounted() {},

  methods: {
    onChange(val) {
      this.clear();
      this.$emit('input', val);
    },
    clear() {
      console.log('调用了');
      this.variabledata = {
        comment: '',
      };
    },
    setVisible(visible, hidden) {
      // console.log('显示',visible,'隐藏',hidden);
      hidden.forEach((v) => {
        this.acceptData.formInfo.formModel.fields
          .filter((field) => field.id === v)
          .forEach((field) => {
            field.params.hidden = true;
            // 隐藏时需要将variabledata中对应值删掉，todo 此代码需要业务测试！！！ by @zhanghm10 20230516
            if (this.variabledata[field.id]) {
              delete this.variabledata[field.id];
            }
          });
      });
      visible.forEach((v) => {
        this.acceptData.formInfo.formModel.fields
          .filter((field) => field.id === v)
          .forEach((field) => {
            field.params.hidden = false;
          });
      });
    },
    formItemChange(v, field, type, item) {
      if (type === 'handler') {
        this.$set(this.variabledata, field, v.toString());
        return;
      }
      type === 'deptSelect' &&
        getDepartmentUsers({
          deptId: v[v.length - 1],
          deptLevel: '',
          userName: '',
        })
          .then((res) => {
            this.$set(this, 'deptHandlerList', res.data);
            this.$set(this.variabledata, 'taskUser', '');
          })
          .catch((err) => {
            console.log(err);
          });
      console.log(v, field, type);
      this.$set(this.variabledata, field, v);
      // 校验
      // if (this.$refs.acceptForm) {
      //   this.$refs.acceptForm.validate(field);
      // }

      if (item && item.params) {
        // 支持在表单中配置调用接口，并把接口的返回值设置到指定表单项的params中指定key上 by @zhanghm10 20230516
        if (item.params.queryApiAndSetResultToComponent) {
          // 下面注释掉的数据是demo数据，和form的params约定以下格式
          /* const item_param_queryApiAndSetResultToComponent = {
            "val1": {
              "api": "接口地址",
              "method": "请求API用的method",
              "param": "传给api的参数",
              "itemId": "",
              "itemParamKey": ""
            },
            "val2": {
              "api": "接口地址",
              "method": "请求API用的method",
              "param": "传给api的参数",
              "itemId": "",
              "itemParamKey": ""
            },
          } */
          const config = item.params.queryApiAndSetResultToComponent[v];
          if (config) {
            (config.method.toLowerCase() === 'get' ? getByUrl : postByUrl)(
              config.api,
              config.param,
              this.acceptData.id
            )
              .then((r) => {
                this.acceptData.formInfo.formModel.fields
                  .filter((field) => field.id === config.itemId)
                  .forEach((field) => {
                    this.$set(field.params, config.itemParamKey, r.data);
                  });
              })
              .catch((e) => {
                console.error(e);
              });
          }
        }
      }
    },
    // 弹框关闭之前回调
    handleAccept(action, done) {
      if (action === 'confirm') {
        this.submitReply(done);
      } else {
        this.$parent.activedCardItem = null;
        done(false);
      }
    },
    // 工单确定受理
    submitReply(done) {
      const {fileList} = this.$refs.uploader;
      if (!fileList.every((item) => item.status === 'done')) {
        this.$Toast.loading('请等待文件上传');
        done(true);
      }
      const param = {
        // 不能上传多个文件
        fileName: fileList.length > 0 ? fileList[0].filePath.fileName : null,
        filePath: fileList.length > 0 ? fileList[0].filePath.path : null,
        remark: this.variabledata.comment,
        taskId: this.acceptData.id,
      };
      let _;
      if (this.variabledata.problemType?.length === 2) {
        this.variabledata.problemType &&
          ([_, this.variabledata.problemType] = this.variabledata.problemType);
      } else {
        console.log(
          'this.variabledata.problemType:',
          this.variabledata.problemType
        );
        // this.variabledata.problemType && ([this.variabledata.problemType] = this.variabledata.problemType);
        if (
          Array.isArray(this.variabledata.problemType) &&
          this.variabledata.problemType.length
        ) {
          this.variabledata.problemType =
            this.variabledata.problemType[
              this.variabledata.problemType.length - 1
            ];
        }
      }
      console.log(_);
      const data = {
        ...this.variabledata,
      };
      console.log('data=>', data);
      console.log(
        '当前的表单组件',
        this.$refs['acceptForm'].$children.forEach((item) => {
          console.log(item.$attrs.validate);
        })
      );
      const validateKeys = this.$refs['acceptForm'].$children.map(
        (item) => item.$attrs.validate
      );

      this.$refs['acceptForm']
        .validate(validateKeys)
        .then(() => {
          console.log('校验成功');
          completeTask(param, data)
            .then((res) => {
              if (res) {
                if (res.code === 500) {
                  this.$Toast.fail(res.msg);
                } else {
                  this.$Toast.success(res.msg);
                }
                this.$parent.activedCardItem = null;
                this.$parent.inquiryFlag();
                done(false);
              }
            })
            .catch((e) => {
              this.$Toast.fail(e.response.data);
              done(true);
            });
        })
        .catch((error) => {
          console.log('校验失败', error);
          this.$Toast.fail('请填写必填项');
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.accept_content {
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  padding-bottom: 75px;
  overflow: scroll;
}
</style>
<style>
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
}
</style>
