<template>
  <div class="base_title_wrap" @click="jump">
    <div class="left_wrap">
      <div :class="isCharts ? 'rect_area_sm' : 'rect_area_big' " ></div>
      <slot name="default">
      </slot>
    </div>
    
    <!-- 跳转按钮 -->
    <svg width="24" height="20" v-if="isJump"  class="jump_button" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.5 12.0001L8.5 17.1963L8.5 6.80395L15.5 12.0001Z" fill="#D9D9D9"/>
    </svg>
  
  </div>
</template>

<script>
export default {
  name: 'BaseTitle',

  props: {
    isCharts: {
      type: Boolean,
      default: false
    },
    isJump: {
      type: Boolean,
      default: false
    },
    
  },

  data() {
    return {
      
    };
  },

  mounted() {
    
  },

  methods: {
    jump() {
      if(!this.isJump) return;
      this.$emit('onJump');
    }
  },
};
</script>

<style lang="scss" scoped>
.base_title_wrap {
  padding-right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left_wrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .rect_area_big {
     width: 3px;
     height: 18px;
     background: #FE9900;
     margin-right: 12px;
  }
  .rect_area_sm {
     width: 2.14px;
     height: 14px;
     background: #FE9900;
     margin-right: 8.75px;
  }
}
</style>