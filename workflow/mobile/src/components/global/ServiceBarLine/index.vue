<template>
 <div class="service-bar-line">
  <div class="service-echarts"></div>
  <!-- 没有数据时显示空 -->
  <Blank2 v-show="!chartData.length" style="position: absolute; top: 0; left: 0px;" />
 </div>
</template>

<script>
import Blank2 from './Blank2.vue';
export default {
  name: 'ServiceBarLine',
  components: {
    Blank2
  },
  props:{
    // 图表横坐标，系列图表取值字段配置
    keyMap:{
      type:Object,
      default:() => ({
        xAxis: 'name', // 横坐标字段
        // yAxis:'满意度' || yAxis:['满意度','环比'] || yAxis: [{name:'',formatter:.....}]
        // yAxisIndex: [0,1],
        legend: ['满意度'],// 图例
        series: ['value'], // 图表系列对应的取值字段
        control: ['line'] // ['bar','line','line'] 控制图表的系列类型
      })
    },
    lineColorList: {
      type: Array,
      default: () => ['#ff9900','#ff9900']
    },
    // 图表数据
    chartData:{
      type: Array,
      default: () => [
        {name:'Q1',value:Math.random()},
        {name:'Q2',value:Math.random()},
        {name:'Q3',value:Math.random()},
        {name:'Q4',value:Math.random()}
      ]
    },
    // 自定义图表相关的option配置
    chartOption: {
      type: Object
    },
    // 设置dataZoomStyle里面的end属性，不为0显示dataZoom
    dataZoomEnd: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      chart: null,
      dataZoomStyle: {
        show: true,
        type: 'slider',
        end: 40,
        handleSize: 0, // 滑动条的 左右2个滑动条的大小
        height: 8, // 组件高度
        left: 0, // 左边的距离
        right: 0, // 右边的距离
        bottom: 10, // 右边的距离
        handleColor: '#eee', // h滑动图标的颜色
        handleStyle: {
          borderColor: '#eee',
          borderWidth: '1',
          shadowBlur: 2,
          background: '#eee',
          shadowColor: '#eee',
        },
        backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
        showDataShadow: false, // 是否显示数据阴影 默认auto
        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        handleIcon:
          'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
        // filterMode: "filter",
        moveHandleStyle: {color: '#eee'},
      },
    };
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el.querySelector('.service-echarts'), null, {renderer: 'svg'});
      this.chart && this.setOption();
    },
    setOption() {
      const {xAxis = 'name', yAxis, yAxisIndex, series = [], control = [], legend = []} = this.keyMap;
      let yAxisConfig = [];
      const propertyValues = this.chartData.map((item) =>
        series.map((propertyName) => (item[propertyName] ? parseFloat(item[propertyName]) : null))
      );
      const minValues = propertyValues.map((seriesValues) => {
        const filteredValues = seriesValues.filter((value) => value !== null);
        return filteredValues.length > 0 ? Math.floor(Math.min(...filteredValues)) : null;
      });
      const maxValues = propertyValues.map((seriesValues) => {
        const filteredValues = seriesValues.filter((value) => value !== null);
        return filteredValues.length > 0 ? Math.ceil(Math.max(...filteredValues)) : null;
      });
      // 配置y轴
      if(yAxis) {
        if (typeof yAxis === 'string') {
          if (control.length > 1) {
            yAxisConfig = [
              {
                min: Math.min(...minValues) || 0,
                max: Math.max(...maxValues) || 100,
                name: yAxis,
                type: 'value',
                splitLine: {lineStyle: {type: 'dashed'}},
                nameTextStyle: {align: 'right'}
              }
            ];
          }else{
            yAxisConfig = [
              {
                name: yAxis,
                type: 'value',
                splitLine: {lineStyle: {type: 'dashed'}},
                nameTextStyle: {align: 'right'}
              }
            ];
          }

        }
        else{
          yAxis.forEach((item,index) => {
            yAxisConfig.push({
              type: 'value',
              splitLine:{show: index === 0 ? true : false, lineStyle:{type:'dashed'}},
              nameTextStyle:{align: index === 0 ? 'right' : 'left'},
              ...(typeof item === 'string' ? {name: item} : item)
            });
          });
        }
      }else{
        yAxisConfig = [
          {
            type: 'value',
            splitLine:{lineStyle:{type:'dashed'}},
          }
        ];
      }
      // 如果数据设置了itemStyle，表示该条数据被选中
      const dataSelectedIndex = this.chartData.findIndex((item) => item.hasOwnProperty('itemStyle'));
      const option = {
        tooltip:{
          trigger: 'axis',
          confine: true
        },
        grid:{
          top: '20%',
          left: '5%',
          bottom: '5%',
          right: '3%',
          containLabel: true
        },
        legend:{
          data: legend,
          icon: 'rect', 
          itemWidth:  14 ,
          itemHeight: 2 ,
          textStyle: { // 可选，设置图例文字样式
            color: '#2C3542A6',
            fontSize: 12,
          },
        },
        xAxis:[
          {
            type: 'category',
            data: this.chartData.map((item) => item[xAxis]),
            axisTick:{show: false},
            axisLine:{
              lineStyle:{
                color:'#e0e0e0'
              }
            },
            axisLabel:{
              color:'#8c8c8c',
              interval: 0
            }
          }
        ],
        yAxis: yAxisConfig,

        dataZoom: this.dataZoomEnd ?
          [{...this.dataZoomStyle, end: this.dataZoomEnd, startValue: dataSelectedIndex > -1 ? dataSelectedIndex : 0}]
          : [],
        series: control.map((type, index) => ({
          name: legend[index],
          type,
          barWidth: 11.42,
          // barGap: '80%',
          data: this.chartData.map((item) => ({...item, name: item[xAxis], value:item[series[index]]})),
          label: {
            // show: type === 'bar' ? true : false,
            show: false,
            position: 'top'
          },
          symbol: 'none',
          yAxisIndex: yAxisIndex ? yAxisIndex[index] : 0,
          smooth: true,
          itemStyle: {
            // borderRadius: [10, 10, 0, 0],
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {offset: 0, color:index % 3 === 0 ? '#5FAEFF' : index % 3 === 1 ? this.lineColorList[0] : '#FFC266'},    // 渐变起始颜色
                {offset: 1, color:index % 3 === 0 ? '#0682FF' : index % 3 === 1 ? this.lineColorList[1] : '#FF9900'}     // 渐变结束颜色
              ],
            },
          }
        })),
        ...(this.chartOption || {})
      };
      this.chart && this.chart.setOption(option, true);
      window.addEventListener('resize', () => {
        this.chart && this.chart.resize({width: 'auto', height: 'auto'});
      });
    }
  },
  mounted() {
    this.initChart();
  },
  watch:{
    chartData() {
      this.setOption();
    }
  }
};
</script>

<style lang="scss" scoped>
.service-bar-line{
  width: 100%;
  height: 100%;
  position: relative;
}
.service-echarts{
  width: 100%;
  height: 100%;
}
</style>
