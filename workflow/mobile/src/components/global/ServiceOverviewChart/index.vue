<template>
  <div class="service-chart-box" v-loading="barLineLoading">

    <!-- 二级指标的标题 -->
    <div class="service-title">
      <span class="main-title">{{viewObject.name}}</span>
      <!-- 是否有趋势图，地市tab的切换 -->
      <div class="service-tabs" v-if="viewObject.hasSwitch">
        <span v-for="tb in viewObject.switchArr" :key="tb.tabId" :class="{'active': tb.tabId == childTabActive}" @click="changeTab(tb)">
          {{tb.name}}
        </span>
      </div>
    </div>
    <!-- 指标具体某些选项展示 -->
    <service-table :table-data="filterKPITable" :key="viewObject.childSplitActive || childKPI.targetId" />
    <!-- echarts图表 -->
    <service-bar-line class="service-echart" :key="echartsKey" ref="serviceBarLine" :key-map="KPI_ECHARTS[echartsKey] ? KPI_ECHARTS[echartsKey].keyMap: {}" :chart-data="barLineData"
      :chart-option="KPI_ECHARTS[echartsKey] ? KPI_ECHARTS[echartsKey].option : {}" :data-zoom-end="barLineData.length > 6 ? Math.floor(6 / barLineData.length * 100) : 0" />
  </div>
</template>

<script>
import ServiceBarLine from '../ServiceBarLine/index.vue';
import ServiceTable from './ServiceTable.vue';
import ajaxRequest, {
  getTopAppeal,
  getKpiIssueTakeNum
} from '@/api/satisfaction';
import {KPI_ECHARTS} from '@/utils/echartsOption.js';
import utils from '@/utils/index';

export default {
  name: 'ServiceOverviewChart',
  props: ['selectedKpiId', 'currentView', 'mapCity', 'date'],
  components: {
    ServiceBarLine,
    ServiceTable
  },
  data() {
    return {
      dateType: {
        日: '1',
        周: '2',
        月: '3',
        季度: '4',
        日累计: '5'
      },
      KPI_ECHARTS,
      KPIChildren: [], // 二级指标数据数组
      childKPI: {}, // 选中的二级指标
      childTabActive: '1', // 二级指标tab选中
      originKPITable: [], // 保存初始化的表格配置
      KPITable: [],

      barLineData: [], // 右侧柱状折线图
      barLineLoading: false, // 右侧柱状折线图加载

      hasMap: false, // 父级是否有地图功能
    };
  },
  computed: {
    viewObject() {
      const {chartConfigId, dimType, name, tabs, restSerialNo, id, table} = this.currentView;
      return {
        name,
        hasSwitch: !!tabs,
        switchArr: tabs || [],
        restSerialNo,
        childSplitActive: id,
        table,
        dimType,
        chartConfigId
      };
    },
    filterKPITable() {
      // 部分指标只有在特定的split下才显示，添加筛选逻辑
      return this.KPITable.map((item) => {
        const [dateType] = this.date;
        let flag = item.hasOwnProperty('includeTargetId')
          ? item.includeTargetId.includes(this.viewObject.childSplitActive)
            ? item
            : false
          : item;
        flag = item.hasOwnProperty('excludeDateType')
          ? item.excludeDateType.includes(dateType)
            ? false
            : flag
          : flag;
        return flag;
      }).filter((item) => !(item === false));
    },
    echartsKey() {
      const {hasSwitch, chartConfigId} = this.viewObject;
      return hasSwitch ? `${chartConfigId}_${this.childTabActive}` : chartConfigId;
    },
    ajaxParams() {
      const {cityid, cityId} = this.mapCity;
      const [statType, statDate] = this.date;
      const {dimType, hasSwitch, childSplitActive: targetId} = this.viewObject;
      // CATI(省内)id 省内对应
      const cati = {'105060201': '1050602', '105060101': '1050601'};
      return {
        statType: this.dateType[statType],
        opTime: statDate,
        targetId: cati[targetId] || targetId,
        dimType: hasSwitch ? this.childTabActive : dimType,
        cityId: cityid || cityId
      };
    }
  },
  methods: {
    // 获取下级相关
    getKPIChildren() {
      // const {children} = childTabs.find(({ targetId }) => targetId === this.selectedKpiId);
      // this.viewArr = children;
      // console.log('获取下级相关', children)
      // 父级是否有地图功能
      // this.hasMap = !!hasMap;
      // const [start, end] = this.childKpiSection;
      // this.KPIChildren = (children || []).slice(start, end);
      // [this.childKPI] = this.KPIChildren;
      // if (this.childKPI.table) {
      //   this.originKPITable = JSON.parse(JSON.stringify(this.childKPI.table));
      // }
      this.setKPIChild(this.viewObject);
    },
    changeKPIChild(kpi) {
      this.childKPI = kpi;
      this.setKPIChild(this.childKPI);
    },
    // 获取下一级指标的相关数据和信息
    setKPIChild(kpi) {
      const {table, hasSwitch, switchArr} = kpi;
      // 没有tabs时，判断是否有table挂载
      if (table) {
        this.originKPITable = JSON.parse(JSON.stringify(table));
        this.KPITable = JSON.parse(JSON.stringify(table));
      }
      // 是否有tab判断，如趋势图，地市等，默认选中第一个
      if (hasSwitch) {
        const [{tabId, table: tableChild}] = switchArr;
        this.childTabActive = tabId;
        if (tableChild) {
          this.originKPITable = JSON.parse(JSON.stringify(tableChild));
          this.KPITable = JSON.parse(JSON.stringify(tableChild));
        }
      } else {
        this.childTabActive = '';
      }
      // 触发target-change,获取数据
      this.onTargetChange();
    },
    // 切换tab
    changeTab(tab) {
      this.childTabActive = tab.tabId;
      if (tab.table) {
        this.originKPITable = JSON.parse(JSON.stringify(tab.table));
        this.KPITable = JSON.parse(JSON.stringify(tab.table));
      }
      // 触发target-change,获取数据
      this.onTargetChange('tab');
    },
    // 指标，tab切换
    onTargetChange() {
      // const { targetId, dateTypes, name } = this.childKPI;
      // this.$emit('target-change', {
      //   targetId,
      //   targetName: name,
      //   dateTypes,
      //   childTabActive: this.childTabActive,
      //   childSplitActive: this.childSplitActive,
      //   __action: action
      // });
      // 如果没有地图，就触发请求，有地图功能，通过地图事件改变触发
      if (!this.hasMap) this.queryEchartsData();
    },
    // echarts地图数据判断获取
    queryEchartsData() {
      // const {mapAction, parentCityId} = this.mapCity;
      // 工信部申诉 -- TOP10申诉问题改善
      if (this.childKPI.targetId == '303') {
        this.$nextTick(() => {
          this.childTabActive == '1'
            ? this.getTOP10EchartHorData()
            : this.getAppealTOPUnit();
        });
        // 工信部申诉：工信部百万申诉率，工信部携号转网申诉率，工信部营销宣传申诉率 需要查询申诉量拼接数据
      } else if (this.childKPI.hasOwnProperty('ajaxIds')) {
        this.getAppealData(this.childKPI.ajaxIds);
      } else {
        this.getEchartsData();
      }
    },
    /**
     * 获取工信部申诉的数据
     * @param {申诉量，申诉率对应的id} arg
     * @param {请求额外的参数} par
     * @param {是否处理图表数据，默认处理} echarts
     * @param {是否处理表格数据，默认处理} table
     */
    getAppealData(
      [appealAmount, appealRate],
      par = {},
      echartsFlg = true,
      tableFlg = true
    ) {
      if (!this.ajaxParams.cityId || !this.ajaxParams.opTime) return;
      this.barLineLoading = true;
      this.barLineData = [];
      this.KPITable = JSON.parse(JSON.stringify(this.originKPITable));
      const params = {
        ...this.ajaxParams,
        ...par
      };

      Promise.all([
        ajaxRequest(this.viewObject.restSerialNo, {
          ...params,
          targetId: appealAmount
        }),
        ajaxRequest(this.viewObject.restSerialNo, {
          ...params,
          targetId: appealRate
        })
      ])
        .then((all) => {
          const [amount, rate] = all;
          let anountData = [];
          let rateData = [];
          const table = JSON.parse(JSON.stringify(this.originKPITable));

          // 处理申诉量
          if (amount.code == 200 && amount.data && amount.data.data) {
            const {
              chart,
              data: {
                first: [item]
              }
            } = amount.data.data;
            anountData = this.processEchartsData(Object.values(chart));
            table[0].score = item ? item.score : null;
          }
          // 处理申诉率
          if (rate.code == 200 && rate.data && rate.data.data) {
            const {chart, data: tableData} = rate.data.data;
            rateData = this.processEchartsData(
              Object.values(chart),
              this.childTabActive === '1' ? 3 : 1
            );
            if (tableData.first) {
              table[1].score = tableData.first[0]
                ? tableData.first[0].score
                : null;
            }
            // 归责单位无领先者计算
            if (table[2]) {
              const ary = Object.values(tableData).reduce((prev, cur) => prev.concat(cur), []);
              const leading = this.calLeadingValue(ary);
              table[2].score = leading;
            }
          }
          if (tableFlg) this.KPITable = table;

          if (echartsFlg) {
            if (anountData.length) {
              this.barLineData = anountData.map((item, index) => ({
                ...item,
                ...rateData[index],
                // 查询归属单位时，appartname有值就显示appartname的数据
                ...(item.appartname && item.appartname !== '-'
                  ? {cityname: item.appartname}
                  : {})
              }));
            } else {
              this.barLineData = [].concat(rateData).map((item) => ({
                ...item,
                // 查询归属单位时，appartname有值就显示appartname的数据
                ...(item.appartname && item.appartname !== '-'
                  ? {cityname: item.appartname}
                  : {})
              }));
            }
          }
        })
        .catch((e) => {
          console.log(e);
        })
        .finally(() => {
          this.barLineLoading = false;
        });
    },
    // 工信部申诉-- TOP10申诉问题改善--归责单位
    async getAppealTOPUnit() {
      this.barLineData = [];
      this.KPITable = JSON.parse(JSON.stringify(this.originKPITable));
      if (!this.ajaxParams.cityId || !this.ajaxParams.opTime) return;
      this.barLineLoading = true;
      try {
        const {code, data} = await getTopAppeal({
          ...this.ajaxParams
        });
        if (code == 200 && data) {
          const chart = data.chart.map((item) => ({
            ...item,
            cityname:
                item.appartName && item.appartName != '-'
                  ? item.appartName
                  : item.cityName
          }));
          this.barLineData = this.processEchartsData([chart]);
          // 处理表格显示内容
          const table = JSON.parse(JSON.stringify(this.originKPITable));
          table[0].score = data.scoreSum;
          table[0].rate = data.totalScoreYearBasisSum;
          table[1].score = data.appealRateSum;
          table[1].rate = data.totalAppealYearBasisSum;
          this.KPITable = table;
        }
        this.barLineLoading = false;
      } catch (e) {
        this.barLineLoading = false;
      }
    },
    /**
     * 获取echarts图表数据
     * @param {请求额外的参数} params
     * @param {是否处理图表数据，默认处理} echarts
     * @param {是否处理表格数据，默认处理} table
     */
    async getEchartsData(params = {}, echarts = true, table = true) {
      this.barLineData = [];
      this.KPITable = JSON.parse(JSON.stringify(this.originKPITable));
      if (!this.ajaxParams.cityId || !this.ajaxParams.opTime) return;
      this.barLineLoading = true;
      try {
        // 二级指标配置了接口请求，就不使用顶层指标的接口请求
        const {code, data} = await ajaxRequest(
          this.viewObject.restSerialNo,
          {
            ...this.ajaxParams,
            ...params
          }
        );
        if (code == 200 && data && data.data) {
          const {chart, data: tableData} = data.data;

          // first:移动 second:电信 third:联通
          if (echarts) this.barLineData = this.processEchartsData(Object.values(chart));
          if (table) this.KPITable = this.processTableData(Object.values(tableData));
          // 用后即评增加参评量和下发量
          const targetId101Or102Flag =
            this.ajaxParams.targetId == '101' ||
            this.ajaxParams.targetId == '102';
          if (table && targetId101Or102Flag) {
            console.log(this.ajaxParams);
            const p = {
              statDate: this.ajaxParams.opTime || this.ajaxParams.statDate,
              statType: this.ajaxParams.statType,
              targetId: this.ajaxParams.targetId
            };
            getKpiIssueTakeNum(p).then((res) => {
              const {issueNum, takeNum} = res.data || {};
              const canpi = {label: '参评量', score: takeNum, unit: '%'};
              const xiafa = {label: '下发量', score: issueNum, unit: '%'};
              this.KPITable.push(canpi);
              this.KPITable.push(xiafa);
            });
          }
        }
        this.barLineLoading = false;
      } catch (e) {
        console.log(e);
        this.barLineLoading = false;
      }
    },
    // 处理echarts数据
    // first:移动 second:电信 third:联通
    processEchartsData(dataAry, start = 0) {
      let data = [];
      const {name, mapAction} = this.mapCity;
      dataAry.forEach((item, index) => {
        const ary = utils.handlerMomrateAndYoyrate(item).map((it) => ({
          ...it,
          [`value${start + index + 1}`]: it.score,
          [`momrate${start + index + 1}`]: it.momrate,
          ...(mapAction == 'select'
            ? it.cityname == name
              ? {itemStyle: {color: '#FFDF55'}}
              : {}
            : {}),
          childSplitActiveName: this.viewObject.name // 工信部满意度分业务table文字处理
        }));
        if (index == 0) {
          data = ary;
        } else {
          for (let i = 0; i < data.length; i++) {
            data[i] = {...data[i], ...ary[i]};
          }
        }
      });
      return data;
    },
    // 处理table数据
    processTableData(tableAry) {
      // console.log('tableAry=>', tableAry);
      let ary = tableAry.reduce((pre, cur) => pre.concat(cur), []);
      ary = utils.handlerMomrateAndYoyrate(ary);
      const table = JSON.parse(JSON.stringify(this.originKPITable));
      if (ary.length) {
        table.forEach((item, index) => {
          if (!item.isCal) {
            item.score =
              ary[index] && !isNaN(Number(ary[index].score))
                ? ary[index].score || null
                : null;
          } else {
            item.score = this.calLeadingValue(ary);
          }
          // 处理2位小数
          item.score =
            item.score && !isNaN(Number(item.score))
              ? Number(item.score).toFixed(2)
              : null;
          // 比率
          if (item.hasOwnProperty('rate')) {
            item.rate = ary[index]
              ? ary[index].momrate
                ? isNaN(Number(ary[index].momrate))
                  ? null
                  : ary[index].momrate
                : null
              : null;
          }
        });
        console.log('table=>', table);
      }
      // 工信部满意度分业务table文字处理
      this.processKPI0102Table(table);
      return table;
    },
    // 工信部满意度分业务table文字处理
    processKPI0102Table(table) {
      if (this.childKPI.targetId !== 'KPI0102') return;
      table.forEach((item) => {
        item.label = item.label.replace('####', this.childSplitActiveName);
      });
    },
    // 计算领先值
    calLeadingValue(ary) {
      if (!ary.length) return null;
      const [first, second, third] = ary;
      if (!first && !second && !third) return null;
      let secleading, thileading;
      if (second) {
        secleading = (Number(first.score) - Number(second.score)).toFixed(2);
      }
      if (third) {
        thileading = (Number(first.score) - Number(third.score)).toFixed(2);
      }
      if (secleading == undefined) return thileading;
      if (thileading == undefined) return secleading;
      return Math.min(secleading, thileading);
    },
    // 工信部 - TOP10申诉问题改善-趋势图
    async getTOP10EchartHorData() {
      this.barLineLoading = true;
      try {
        this.$refs.topEcharts &&
          (await this.$refs.topEcharts.getData({
            ...this.ajaxParams,
            cityId: this.mapCity.cityid
          }));
        this.barLineLoading = false;
      } catch (e) {
        this.barLineLoading = false;
      }
    },
    // 工信部 - TOP10申诉问题改善 - 趋势图
    // 点击原因下转，展示的table表格内容不一致
    switchTOP10Table(type = 'HorizontalBar', data) {
      const table =
        type == 'BarLine'
          ? [
            {label: '总申诉量', score: null, rate: null, unit: '%'},
            {label: '总申诉率', score: null, rate: null, unit: '%'}
          ]
          : JSON.parse(JSON.stringify(this.originKPITable));
      if (data) {
        table.forEach((item, index) => {
          item.score = data[index].score;
          item.rate = data[index].rate;
        });
      }
      this.KPITable = table;
    }
  },
  mounted() {
    this.getKPIChildren();
  },
  watch: {
    date() {
      this.queryEchartsData();
    },
    mapCity() {
      this.queryEchartsData();
    }
  }
};
</script>

<style lang="scss" scoped>
.service-chart-box {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 0px 16px 0px;
  .service-echart {
    width: 100%;
    flex: 1;
    position: relative;
    height: calc(100% - 103px);
  }

  .service-title {
    color: #262626;
    font-size: 18px;
    padding: 0 15px;
    height: 29px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      cursor: pointer;
      // &:not(:first-child) {
      //   margin-left: 10px;
      // }
      &.main-title {
        color: #262626;
        font-weight: 500;
      }
    }

    &.service-title-border {
      // border-bottom: 1px solid rgba(140,140,140,0.35);

      span {
        &.active {
          padding-bottom: 6px;
          border-bottom: 2px solid #ffbd01;
        }
      }
    }
  }
  .service-child {
    display: flex;
    align-items: center;
  }
  .service-tabs {
    // padding: 0 15px;
    height: 24px;
    width: 142px;
    // display: inline-block;
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      flex: 1;
      text-align: center;
      background: #fff;
      display: inline-block;
      height: 100%;
      line-height: 22px;
      color: #4D4D4D;
      font-weight: 400;
      border: 1px solid #e6e6e6;
      cursor: pointer;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        background: #FF9900;
        color: #fff;
        border: 1px solid transparent;
      }
    }
  }
  .service-splits {
    padding: 0 15px;
    display: inline-block;
    font-size: 12px;
    color: #8c8c8c;

    span {
      //  font-size: 12px;
      //  color: #8c8c8c;
      display: inline-block;
      padding: 2px 4px;
      cursor: pointer;

      &.active {
        color: #fec002;
      }

      &.disable {
        opacity: 0.45;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}
</style>
../../../utils/echartsOption.js/index.js