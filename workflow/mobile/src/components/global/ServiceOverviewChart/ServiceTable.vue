<template>
  <div class="service-table-box">
    <table class="servie-table">
      <tr>
        <template v-for="td in tableData">
          <td :key="td.label">
            <div class="name-icon">
              <!-- <img class="title-icon" alt="" src="@/assets/icon/fuhao.png" /> -->
              <span class="title-name">{{td.label}}</span>
            </div>
            <div class="table-value-main">
              <!-- 领先值负数标红显示 -->
              <span :class="{'score':true,'red':td.isCal && td.score < 0}" style="color: #262626;">
                {{td.score ? td.score : (td.score === 0 ? td.score :  '--')}}
              </span>
              <span class="rate" v-if="td.hasOwnProperty('rate')">
                {{td.rateLabel || '环比'}}：
                <span :class="[td.rate > 0 ? 'top' : td.rate <  0 ? 'bottom' : '']" v-if="td.rate ===0 || td.rate && !isNaN(Number(td.rate))">
                  <img :src="td.rate> 0 ?  upImg  : td.rate <  0  ? downImg : ''" width="8"/>
                  {{td.rate}}{{td.unit}}
                </span>
                <span v-else>--</span>
              </span>
            </div>
          </td>
        </template>
      </tr>
    </table>
  </div>
</template>

<script>

export default {
  name: 'ServiceTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      upImg: require('@/assets/icon/up-icon.png'),
      downImg: require('@/assets/icon/down-icon.png'),
    };
  }
};
</script>

<style lang="scss" scoped>
.service-table-box {
  padding: 0 16px;
  width: 100%;
  margin: 16px 0 10px;
  height: 90px;
}
.servie-table {
  height: 100%;
  width: 100%;
  padding: 0 0 0 9px;
  font-size: 12px;
  color: #595959;
  background-color: rgb(245,245,245);
  .name-icon {
    display: flex;
    align-items: center;
    .title-icon {
      width: 11px;
      height: 11px;
      margin-right: 6px;
    }
    .title-name {
      font-size: 14px;
      padding-right: 2px;
      color: #4d4d4d
    }
  }
  .table-value-main {
    margin-top: 8px;
    padding: 0 0 0 0px;
  }
  .score {
    font-weight: 500;
    font-size: 24px;

    &.red {
      color: #d9001b;
    }
  }
  .rate {
    font-size: 12px;
    .top {
      color: #339933;
    }

    .bottom {
      color: #d9001b;
    }

    i {
      font-size: 18px;
      vertical-align: middle;
    }
  }
}
</style>
