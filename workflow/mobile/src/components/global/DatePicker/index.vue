<template>
  <div class="date_picker_wrap">
    <div class="base_box_time">
       <div class="date_type_wrap" @click="typeShow = true">
          {{timeType.label}}
        </div>
        <div  class="date_select_wrap" @click="dateShow = true">
          {{dispalyTimeText}}
          <img src="@/assets/img/date.png" width="18" height="23">
        </div>
   
    </div>
   
   <!-- 时间类型选择 -->
   <van-popup v-model="typeShow" round position="bottom">
      <van-picker
        title="请选择时间类型"
        show-toolbar
        :columns="dateTypes"
        value-key='label'
        @confirm="confirmType"
        @cancel="typeShow = false"
      />
   </van-popup>

   <!-- 时间选择组件 -->
   <van-popup v-model="dateShow" round position="bottom">
      <!-- 月/日选择器 -->
      <template v-if="['date','year-month'].indexOf(selectedTimeType) > -1">
         <van-datetime-picker
          v-model="currentDate"
          :type="selectedTimeType"
          title="选择年月"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="confirmDate"
          @cancel="dateShow = false"
        />
      </template>
      <!-- 年份选择器 -->
      <template v-else-if="selectedTimeType == 'year'">
          <van-picker
            title="请选择年份"
            show-toolbar
            :columns="yearColumns"
            :default-index="defaultIndexYear"
            @cancel="dateShow = false"
            @confirm="confirmYear"
          />
      </template>
       <!-- 季度选择器 -->
      <template v-else-if="selectedTimeType == 'quarter'">
          <van-picker
            ref="quarter"
            title="请选择季度"
            show-toolbar
            :columns="quarterColumns"
            @cancel="dateShow = false"
            @confirm="confirmQuarter"
            @change="onChangeQuarter"
          />
      </template>

       <!-- 周选择器 -->
      <template v-else-if="selectedTimeType == 'monthweek'">
          <van-picker
            ref="monthweek"
            title="请选择周"
            show-toolbar
            :columns="quarterMonthWeek"
            @cancel="dateShow = false"
            @confirm="confirmMonthWeek"
            @change="onChangeMonthWeek"
          />
      </template>
     

      
   </van-popup>

   
     
  </div>
</template>

<script>
// 年月日-周-季度 选择器封装
// 特殊周 一月四周, 多余算进第四周
// 特殊季度 Q1 Q2 Q3 Q4
export default {
  name: 'DatePicker',

  props: {
    dateTypes: {
      type: Array,
      default: () => [
        {label: '日', value: 'daterange'}, 
        {label: '月', value: 'month'}, 
        {label: '周', value: 'monthweek'},
        {label: '季度', value: 'quarter'},
        {label: '年', value: 'year'}
      ]
    },
    
    defaultDate:{
      type: Object,
      default: () => ({'日': new Date()})
    },
    // 格式化出参
    valueFormat: {
      type: Object,
      default: () => ({
        'date': 'yyyy-MM-DD',
        'year-month': 'yyyy-MM',
        'year': 'yyyy',
        'quarter':'yyyy-0Q',
        'monthweek': 'yyyy-MM-DD'
      })
    },
    // 可选的最小日期
    minDate: {
      type: Date,
      default: () => new Date(2020, 0, 1),
    },
    // 可选的最大日期(固定)
    maxDate: {
      type: Date,
      default: () => new Date(),
    }
  },

  data() {
    return {
      currentDate: new Date(),
      // 移植pc端后的映射
      timeMap: {
        'daterange': 'date',
        'month': 'year-month'
      },
      // 基本格式映射(周特殊处理, 可能有多个不同逻辑)
      timeTextMap: {
        'date': 'yyyy-MM-DD',
        'year-month': 'yyyy-MM',
        'year': 'yyyy',
        'quarter':'yyyy年第Q季度'
      },

      // 存储不同类型时间的最近切换值
      checkMap: {
        'date': null,
        'year-month': null,
        'year': null,
        'quarter':null,
        'monthweek':null
      },

      typeShow: false,
      dateShow: false,
      
      // 初始化
      timeType: this.dateTypes[0]
    };
    
  },

  created() {
  },

  watch: {
    // 监听默认值的改变, 赋予新值
    defaultDate() {
     
      this.dateTypes.forEach((item) => {
        this.handleTimeType(item);
      });
    }
  },

  computed: {
    // 时间格式化文本展示
    dispalyTimeText() {
      if(this.selectedTimeType in this.timeTextMap) {
        return this.$moment(this.currentDate).format(this.timeTextMap[this.selectedTimeType]); 
      }
      const weekNum = Math.ceil(this.$moment(this.currentDate).date() / 7); 
      return `${this.$moment(this.currentDate).format('yyyy年MM月')}第${weekNum === 5 ? 4 : weekNum}周`;        
    },

    // 当前选择的时间类型, 用于条件展示组件,用于映射类型值
    selectedTimeType() {
      const val =  this.timeType.value;
      if( val in this.timeMap) {
        return this.timeMap[val];
      }
      return val;
    },

    // 生成年份数据 (根据最小时间和最大时间)
    yearColumns() {
      // 处理年份
      const yearsList = [];
      const maxVal = this.$moment(this.maxDate).endOf('year');
      let minVal = this.$moment(this.minDate).startOf('year'); 
      
      // 时间差对比-年份
      while(minVal.isBefore(maxVal)) {
        yearsList.push(String(minVal.year()));
        minVal = minVal.add(1,'year');
      }
      return yearsList;
    },

    // 年份的默认选择
    defaultIndexYear() {
      return  this.yearColumns.indexOf(this.dispalyTimeText);      
    },
    // 生成季度数据
    quarterColumns() {
      // 根据当前时间赋予默认值
      const Y = this.currentDate.getFullYear();
      const YIndex =  this.yearColumns.indexOf(`${Y}`);
      const QIndex = this.$moment(this.currentDate).quarter() - 1; 
      
      // 处理边界情况
      let QNum = 4;
      if(Y === this.$moment().year()) {
        QNum = QIndex + 1;
      }

      const years = [
        {
          values: this.yearColumns,
          defaultIndex: YIndex,
        },
        { 
          defaultIndex: QIndex,
          values: new Array(QNum).fill('').map((item,index) => `Q${index + 1}`)
        }
      ];
      return years;
    },
    // 生成周数据
    quarterMonthWeek() {
      // 根据当前时间赋予默认值
      const Y = this.currentDate.getFullYear();
      const M = this.currentDate.getMonth();
      const YIndex =  this.yearColumns.indexOf(`${Y}`);
      const MIndex = this.$moment(this.currentDate).month(); 
      const WIndex = Math.ceil(this.$moment(this.currentDate).date() / 7) - 1; 

      // 处理边界情况(月份/周的最大限制)
      let MNum = 12;
      let WNum = 4;
      if(Y === this.$moment().year()) {
        MNum = this.$moment().month() + 1;
        if(M === this.$moment().month()) {
          WNum = WIndex + 1; 
        }
      }
      const years = [
        // 年
        {
          values: this.yearColumns,
          defaultIndex: YIndex,
        },
        // 月
        { 
          defaultIndex: MIndex,
          values: new Array(MNum).fill('').map((item,index) => `${index + 1}月`)
        },
        // 周
        { 
          defaultIndex: WIndex === 4 ? 3 : WIndex,
          values: new Array(WNum).fill('').map((item,index) => `第${index + 1}周`)
        }
      ];
      return years;
    }
  },

  methods: {
    // 处理不同时间类型的默认时间
    handleTimeType(dateType) {
      const {label} = dateType;
      // 两种时间都支持
      if(label == '季度') {
        if('季' in this.defaultDate || '季度' in this.defaultDate) {
          this.currentDate =  this.$moment((this.defaultDate[label] || this.defaultDate['季']).replace('Q','0'), 'YYYY-0Q').toDate();
          this.timeType =  {label: '季度', value: 'quarter'};
        }
      }
      if(label == '周') {
        if('周' in this.defaultDate) {
          const weekMoment = this.$moment(this.defaultDate[label]);
          const lastMonthDate = weekMoment.date();
          const week = Math.ceil(lastMonthDate / 7);
          const date = week > 4 ? lastMonthDate : week * 7; 
          this.currentDate = new Date(weekMoment.year(), weekMoment.month(), date);
          this.timeType =  {label: '周', value: 'monthweek'};
        }
      }
      if(label == '年' || label == '月' || label == '日') {
        console.log('重新运行了');
        if('年' in this.defaultDate || '月' in this.defaultDate || '日' in this.defaultDate) {
          this.currentDate =  this.$moment(this.defaultDate[label]).toDate();
          const Key = this.dateTypes.find((item) => item.label == label );
          this.timeType =  {label, value: Key.value};
        }
      }
    },
    // 统一确定后回调
    confirmCallback() {
      const time = this.$moment(this.currentDate).format(this.valueFormat[this.selectedTimeType]);
      // 兼容pc映射值
      const map = [
        {label: '日', value: 'date'}, 
        {label: '月', value: 'year-month'}, 
        {label: '周', value: 'monthweek'},
        {label: '季度', value: 'quarter'},
        {label: '年', value: 'year'}
      ];
      const {label} = map.find((item) => item.value == this.selectedTimeType);
      console.log('选择的时间: ', [label,time]);
      this.$emit('change', [label,time]);
    },
    // 时间类型选择确认
    confirmType(values) {
      // console.log(values);
      // if(this.checkMap[this.selectedTimeType]) {
      //   this.currentDate = this.checkMap[this.selectedTimeType];
      // }
      // // 存储切换前的时间
      // this.checkMap[this.selectedTimeType] = this.currentDate;
      this.timeType = values;
      this.typeShow = false;


      // 切换成周的时候, 取周的最后一天
      if(this.selectedTimeType === 'monthweek') {
        const lastMonthDate = this.currentDate.getDate();
        const week = Math.ceil(this.$moment(this.currentDate).date() / 7);
        const date = week > 4 ? lastMonthDate : week * 7; 
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), date);
      }

      this.confirmCallback();

      
    },
    // 日/月选择确认
    confirmDate(values) {
      this.currentDate = values;
      this.dateShow = false;
      this.confirmCallback();
    },
    // 年份选择确认
    confirmYear(values) {
      console.log(values);
      // 这里要处理 不同时间类型切换后,对原始时间的影响
      this.currentDate = new Date(`${values}`);
      this.dateShow = false;
      this.confirmCallback();
    },
    // 季度确认
    confirmQuarter(values,picker) {
      // 设置季度
      this.currentDate = new Date(values[0], `${picker[1] * 3}`);
      this.dateShow = false;
      this.confirmCallback();
    },
    // 周确认
    confirmMonthWeek(values,picker) {
      // 设置周(统一pc端按照最后一天算)
      const lastMonthDate = new Date(values[0], `${picker[1] + 1}`,0).getDate();
      const date = picker[2] === 3 ? lastMonthDate : (picker[2] + 1) * 7; 
      this.currentDate = new Date(values[0], `${picker[1]}`, date);
      this.dateShow = false;
      this.confirmCallback();
    },
    // 年 - 月 - 周 集合切换
    onChangeMonthWeek(picker, values) {
      console.log(picker, values);
      const year = (new Date()).getFullYear();
      const month = (new Date()).getMonth() + 1;
      const MNum = this.$moment().month() + 1; 
      const WIndex = Math.ceil(this.$moment().date() / 7) - 1; 
      // eslint-disable-next-line prefer-destructuring
      const checkMonth = values[1].split('')[0];
      if(values[0] === `${year}`) {
        picker.setColumnValues(1, new Array(MNum).fill('').map((item,index) => `${index + 1}月`));
        console.log(checkMonth,month);
        if(`${checkMonth}` === `${month}`) {
          picker.setColumnValues(2, new Array(WIndex + 1).fill('').map((item,index) => `第${index + 1}周`));
        }else{
          picker.setColumnValues(2, new Array(4).fill('').map((item,index) => `第${index + 1}周`));
        }
      }else{
        picker.setColumnValues(1, new Array(12).fill('').map((item,index) => `${index + 1}月`));
        picker.setColumnValues(2, new Array(4).fill('').map((item,index) => `第${index + 1}周`));
      }

    },
    // 年份改变, 切换季度集合
    onChangeQuarter(picker, values) {
      // 年份
      const year = (new Date()).getFullYear();
      const QNum = this.$moment().quarter(); 
      if(values[0] === `${year}`) {
        picker.setColumnValues(1, new Array(QNum).fill('').map((item,index) => `Q${index + 1}`));
      }else{
        picker.setColumnValues(1, new Array(4).fill('').map((item,index) => `Q${index + 1}`));
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.base_box_time {
  
  font-family: Source Han Sans CN;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: -0.2736363708972931px;
  text-align: left;
  display: flex;
  .date_type_wrap {
    width: auto;
    height: 30px;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    box-shadow: 0px 1px 4px 0px #0000000F;

  }
  .date_select_wrap {
    background: #fff;
    width: auto;
    box-sizing: border-box;
    height: 30px;
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    padding: 0px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 4px 0px #0000000F;
    img {
      margin-top: 2px;
      margin-left: 10px;
    }
  }
}

</style>