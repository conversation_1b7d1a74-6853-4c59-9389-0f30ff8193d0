<template>
  <span>{{ info && data ? info && data : "暂无数据" }}</span>
</template>

<script>
import {allCode} from '@/utils/filter.js';
export default {
  name: 'detailItem_codeSelect',
  props: {
    info: String,
    form: Object,
  },
  mounted() {
    this.data = allCode(this.form.params && this.form.params.selectId).filter(
      (ele) => ele.value === this.info
    )[0]?.label;
  },
  data() {
    return {
      data: null,
    };
  },
};
</script>
