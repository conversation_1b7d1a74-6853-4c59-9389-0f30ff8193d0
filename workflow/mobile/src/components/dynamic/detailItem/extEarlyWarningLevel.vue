<template>
  <span v-if="value"> {{ value | extEarlyWarningLevel }}</span>
  <span v-else>暂无数据</span>
</template>

<script>
import filter from '@/utils/filter.js';
export default {
  filters: filter,
  name: 'detailItem_extEarlyWarningLevel_select',
  props: {
    info: String,
  },
  created() {
    this.value = this.info;
  },
  data() {
    return {
      value: '',
    };
  },
};
</script>

<style></style>
