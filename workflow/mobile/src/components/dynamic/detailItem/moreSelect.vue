<template>
  <!-- <Cascader
    v-model="value"
    :options="options"
    :props="{ expandTrigger: 'hover' }"
    @change="change"
    :disabled='form.readOnly'
    size="small"></Cascader> -->
  <span>{{ value ? value : "暂无数据" }}</span>
</template>

<script>
import {allCode} from '@/utils/filter.js';
// import {Cascader} from 'element-ui';
export default {
  // components: {Cascader},
  name: 'detailItem_moreSelect',
  props: {
    info: [String, Array],
    form: Object,
  },
  mounted() {
    try {
      const data = allCode(this.form.params.selectId);
      let info;
      console.log(data);
      console.log(this.form);
      console.log(this.info);
      typeof this.info === 'object'
        ? (info = this.info[this.info.length - 1])
        : ({info} = this);
      // if (info.length === 4) {
      //   const res = data.filter((v) => v.value === info);
      //   this.value = res[0].label;
      // } else if (info.length === 6) {

      //   // const res = data.filter((v) => v.value === info.substring(0, 4));
      //   // console.warn(info,data);
      //   // this.value = `${res[0].label}/${res[0].childs[parseInt(info.substring(4, 6)) - 1].label}`;
      // }

      const dics = new Map();
      data.forEach((d) => {
        this.getDics(d, dics);
      });
      if (info.length === 6) {
        this.value = `${dics.get(info.substring(0, 2))}/${dics.get(
          info.substring(0, 4)
        )}/${dics.get(info.substring(0, 6))}`;
      } else if (info.length === 4) {
        this.value = `${dics.get(info.substring(0, 2))}/${dics.get(
          info.substring(0, 4)
        )}`;
      } else {
        this.value = dics.get(info);
      }
      this.change();
    } catch (error) {
      console.log(error);
    }
  },
  data() {
    return {
      value: null,
    };
  },
  methods: {
    getDics(d, dics) {
      dics.set(d.value, d.label);
      if (d.childs) {
        d.childs.forEach((s) => {
          this.getDics(s, dics);
        });
      }
    },
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
