<template>
  <span>{{ value ? value : "暂无数据" }}</span>
</template>

<script>
// import {getFlowCode} from '@/api/common/index.js';
export default {
  name: 'detailItem_systemText',
  props: {
    info: String,
    form: Object,
  },
  mounted() {
    console.log('qqqqqq');
    this.value = this.info;
    // this.getCode();
  },
  data() {
    return {
      value: '',
    };
  },
  // watch: {
  //   data(val) {
  //     this.value = val;
  //   },
  // },
  methods: {
    // // 获取后台生成的代码
    // getCode() {
    //   getFlowCode({
    //     typeId:this.form.id,
    //   }).then((res) => {
    //     this.value = res.data['0'].label;
    //     console.log('qqqqqq', res);
    //   });
    // }
  },
};
</script>
