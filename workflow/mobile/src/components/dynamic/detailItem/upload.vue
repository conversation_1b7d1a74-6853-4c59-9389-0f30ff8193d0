<template>
  <div>
    <div v-if="value">
      <div v-if="isApply">
        <a
          style="margin-right: 16px"
          v-for="(item, index) in value.split('|')"
          :key="index"
          target="_blank"
          :href="downloadUrlApply + `?attachList=${item}`"
          >{{ item }}</a
        >
      </div>
      <div v-else>
        <a
          style="margin-right: 16px"
          v-for="(item, index) in value ? value.split('|') : []"
          :key="index"
          target="_blank"
          :href="downloadUrlCommon + `?attachList=${item}`"
          >{{ item }}</a
        >
      </div>
    </div>
    <div v-else>暂无附件</div>
  </div>
</template>

<script>
export default {
  name: 'detailItem_upload',
  props: {
    info: String,
    isApply: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.value = this.info;
  },
  data() {
    return {
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList`,
      downloadUrlApply: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downReplyAttachList`,
      value: '',
    };
  },
};
</script>

<style></style>
