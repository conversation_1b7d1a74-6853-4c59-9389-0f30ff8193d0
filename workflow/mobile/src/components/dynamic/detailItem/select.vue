<template>
  <span>{{ value ? value : "暂无数据" }}</span>
</template>

<script>
import filter from '@/utils/filter.js';
export default {
  filters: filter,
  name: 'detailItem_select',
  props: {
    info: String,
    form: {
      type: Object,
    },
  },
  created() {
    console.log('qqqqqq');
    this.value =
      this.info &&
      this.form &&
      this.form.options.reduce((acc, item) => {
        if (item.id === this.info) {
          acc = item.name;
        }
        return acc;
      }, null);
  },
  data() {
    return {
      value: '',
    };
  },
};
</script>

<style></style>
