<template>
  <span>{{ data ? data : "暂无数据" }}</span>
</template>

<script>
import {getDepartmentChilds} from '@/api/common/index.js';
export default {
  name: 'detailItem_deptSelect',
  props: {
    form: Object,
  },
  async created() {
    const {data} = await getDepartmentChilds();
    let tmpChild = data;
    this.form.value.forEach((ele) => {
      [tmpChild] = tmpChild.filter((ele1) => ele1.value === ele);
      tmpChild.childs !== null && (tmpChild = tmpChild.childs);
    });
    this.data = tmpChild.label;
  },
  data() {
    return {
      data: null,
    };
  },
};
</script>
