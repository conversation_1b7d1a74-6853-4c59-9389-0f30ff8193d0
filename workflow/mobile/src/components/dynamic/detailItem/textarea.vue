<template>
  <div class="detail-item-text-area-wrap">
    <template v-if="value && form.id !== 'identifier'">
      {{ value }}
    </template>
    <template
      v-else-if="form.id === 'receiverUnit' || form.id === 'originUnit'"
    >
      {{ value | unitCode }}
    </template>
    <template v-else>
      {{ value }}
    </template>
  </div>
</template>

<script>
import filter from '@/utils/filter.js';
export default {
  filters: filter,
  name: 'detailItem_textarea',
  props: {
    info: String,
    form: {
      type: Object,
    },
  },
  created() {
    this.value = this.info;
  },
  data() {
    return {
      value: '',
    };
  },
};
</script>

<style>
.textarea-popper {
  width: 300px;
}
</style>
