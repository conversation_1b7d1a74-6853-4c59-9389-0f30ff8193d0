<template>
  <div>
    <div v-if="value">
      <a
        style="margin-right: 16px"
        v-for="(item, index) in value.split('|')"
        :key="index"
        target="_blank"
        :href="downloadUrlCommon + `?attachList=${item}`"
        >{{ item }}</a
      >
    </div>
    <div v-else>暂无数据</div>
  </div>
</template>

<script>
export default {
  name: 'detailItem_upload_list',
  props: {
    info: String,
  },
  created() {
    this.value = this.info;
  },
  data() {
    return {
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList`,
      value: '',
    };
  },
};
</script>

<style></style>
