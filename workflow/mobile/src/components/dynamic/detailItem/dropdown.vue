<template>
  <span>{{ value ? value : "暂无数据" }}</span>
</template>

<script>
// 工单细类的详情展示组件
import filter from '@/utils/filter.js';
export default {
  filters: filter,
  name: 'detailItem_dropdown',
  props: {
    info: String,
    form: Object,
  },
  created() {
    this.value =
      this.info &&
      this.form &&
      this.form.options.reduce((acc, item) => {
        if (item.id === this.info) {
          acc = item.name;
        }
        return acc;
      }, null);
  },
  data() {
    return {
      value: '',
    };
  },
};
</script>

<style></style>
