<template>
  <div>
    <!-- <template
      v-if="value && value.split('').length > 12 && form.id !== 'identifier'"
    >
      <el-tooltip
        class="item"
        effect="light"
        :content="value"
        placement="top-start"
      >
        <span>{{ value.split("").slice(0, 14).join("") }}...</span>
      </el-tooltip>
    </template> -->
    <div v-if="value">
      <template v-if="form.id === 'receiverUnit' || form.id === 'originUnit'">
        {{ value | unitCode }}
      </template>
      <template v-else>
        {{ value }}
      </template>
    </div>
    <div v-else>暂无数据</div>
  </div>
</template>

<script>
import filter from '@/utils/filter.js';
export default {
  filters: filter,
  name: 'detailItem_text',
  props: {
    info: String,
    form: {
      type: Object,
      default: () => ({id: 'content'}),
    },
  },
  created() {
    this.value = this.info;
  },
  data() {
    return {
      value: '',
    };
  },
};
</script>

<style></style>
