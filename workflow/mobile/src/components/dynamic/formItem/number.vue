<template>
  <van-field
    v-model="value"
    @input="change"
    type="number"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    readonly
    is-link
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="content"
    :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
  />
</template>

<script>
export default {
  name: 'formItem-number',
  props: {
    data: Number,
  },
  mounted() {
    this.value = this.data;
  },
  data() {
    return {
      value: null,
    };
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
