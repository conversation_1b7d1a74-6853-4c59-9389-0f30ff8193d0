<template>
  <van-field
    v-model="commentdata"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    :disabled="fieldInfo.readOnly"
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="`请填写${fieldInfo.name}`"
    :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    @input="change"
    type="text"
  />
</template>

<script>
export default {
  name: 'formItem-comments',
  props: {
    data: String,
    formInfo: Object,
  },
  created() {
    this.change();
  },
  data() {
    return {
      commentdata: '',
    };
  },
  methods: {
    change() {
      this.$emit('change', this.commentdata);
    },
  },
};
</script>

<style lang="scss" scoped></style>
