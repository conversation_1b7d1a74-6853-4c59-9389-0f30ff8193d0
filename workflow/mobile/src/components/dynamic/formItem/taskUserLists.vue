<template>
  <div>
    <UserList
      v-for="item in foruser"
      :key="item"
      @push-info="handleUserInfo($event, item)"
    />
    <el-button plain size="small" class="btn1" @click="increase"
      >增加</el-button
    >
  </div>
</template>

<script>
// 未找到
import UserList from '@/components/dynamic/formItem/userlist/userlist';
export default {
  name: 'formItem-taskUserLists',
  components: {
    UserList,
  },
  props: {
    data: Array,
    fieldInfo: Object,
  },
  data() {
    return {
      foruser: [1],
      UserListData: [],
    };
  },

  mounted() {},

  methods: {
    handleUserInfo(e, i) {
      this.UserListData[i - 1] = e;
      console.log('得到的', this.UserListData);
      this.$emit('change', this.UserListData);
    },
    increase() {
      this.foruser.push(this.foruser.length + 1);
    },
  },
};
</script>

<style lang="scss" scoped></style>
