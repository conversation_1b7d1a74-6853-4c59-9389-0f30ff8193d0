<template>
  <div>
    <van-field
      v-model="value"
      is-link
      :required="fieldInfo.required && !fieldInfo.readOnly"
      readonly
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请填写${fieldInfo.name}`"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
      @click="positionOpen = true"
    />
    <van-popup
      v-model="positionOpen"
      round
      position="bottom"
      get-container="body"
    >
      <van-picker
        ref="picker"
        title="请选择"
        value-key="name"
        show-toolbar
        :columns="options"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </van-popup>
  </div>
  <!-- <el-select v-model="value" @change="change" size="small">
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
      :disabled="item.disabled"
    >
    </el-option>
  </el-select> -->
</template>

<script>
export default {
  name: 'formItem-dropdown_notice',
  props: {
    fieldInfo: Object,
    data: {
      type: String,
      default: '11',
    },
  },
  mounted() {
    if (this.fieldInfo) {
      this.options = this.fieldInfo.options;
    }
    this.change();
  },
  data() {
    return {
      value: this.data,
      positionOpen: false,
      options: [
        {
          id: '11',
          name: '短信',
        },
        {
          id: '00',
          name: '不通知',
        },
      ],
    };
  },
  methods: {
    change() {
      this.$emit(
        'change',
        this.options.find((item) => item.name === this.value)?.id
      );
    },
    onConfirm(value) {
      this.value = value.name;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
  },
  watch: {
    data(val) {
      this.value = val;
    },
  },
};
</script>
