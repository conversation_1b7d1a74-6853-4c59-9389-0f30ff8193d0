<template>
  <van-field
    v-model="value"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    :disabled="fieldInfo.readOnly"
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="`请填写${fieldInfo.name}`"
    :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    rows="1"
    autosize
    @input="change"
    type="textarea"
  />
</template>

<script>
export default {
  name: 'formItem-textarea',
  props: {
    data: String,
    fieldInfo: Object,
  },
  mounted() {
    this.change();
    this.value = this.data;
  },
  data() {
    return {
      value: '',
    };
  },
  watch: {
    data(val) {
      this.value = val;
    },
  },
  methods: {
    change() {
      console.log('chufa1');
      this.$emit('change', this.value);
    },
  },
};
</script>
<style scoped></style>
