<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="content"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="opt?.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="opt"
        @confirm="onConfirm"
        @cancel="onCancel"
      >
        <template #option="target" v-if="fieldInfo.params.userNum === 'more'">
          <div
            style="width: 60%; display: flex; justify-content: space-between"
          >
            <span>{{ target.label }}</span
            ><van-checkbox
              v-model="target.checked"
              ref="vanchecks"
            ></van-checkbox>
          </div>
        </template>
      </van-picker>
      <van-empty description="暂无数据" v-if="opt?.length === 0" />
    </van-popup>
  </div>
  <!-- <el-select
    style="width: 250px"
    v-model="value"
    :multiple="fieldInfo.params.userNum === 'more'"
    :disabled="isDisabled"
    size="small"
    @change="change"
    @remove-tag="removeTag"
    :placeholder="content"
  >
    <el-option
      v-for="item of fieldInfo.params.options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select> -->
</template>

<script>
// import {getDepartmentChilds, getDepartmentUsers} from '@/api/common/index.js';
export default {
  name: 'formItem-apiDropdown',
  props: {
    fieldInfo: Object,
    data: [String, Array],
    formdata: Object,
    handlerList: Array,
    isReset: Boolean,
  },
  mounted() {
    this.$EventBus.$on('clearVal', (res) => {
      console.log(res);
      this.$emit('change', '');
    });
  },
  updated() {
    console.log('updated');
  },
  data() {
    return {
      options: null,
      value: null,
      optionsOpen: false,
      isDisabled: false,
      content: '请选择处理人',
      isRemoving: false,
    };
  },
  watch: {
    handlerList(val) {
      this.$nextTick(() => {
        val &&
          (this.options = val.map((item) => ({...item, checked: false}))) &&
          (this.content = '请选择处理人') &&
          (this.isDisabled = false);
      });
    },
    'fieldInfo.params': {
      handler(v) {
        if (v.options && v.options.length > 0) {
          this.isDisabled = false;
        }
      },
      deep: true,
    },
    data(val) {
      // console.log(this.isReset);
      // if (this.isReset) {
      //   this.options = null;
      //   this.content = '请先选择受理部门';
      //   this.isDisabled = true;
      //   return;
      // }
      if (val?.length === 0 || !val) {
        if (this.isRemoving) {
          this.isRemoving = !this.isRemoving;
          return;
        }
        this.value = null;
        this.options = null;
        this.content = '请先选择受理部门';
        this.isDisabled = true;
      }
    },
  },
  computed: {
    opt() {
      return this.fieldInfo.params?.options?.map((item) => ({
        ...item,
        checked: false,
      }));
    },
  },
  methods: {
    onConfirm(value) {
      if (this.fieldInfo.params.userNum === 'more') {
        this.value = null;
        const option = this.$refs.vanchecks.$parent.options;
        const targetItem = option.filter((item) => item.checked);
        if (targetItem.length === 0) {
          this.$Toast('请选择');
          return;
        }
        this.value = targetItem.map((item) => item.label).join();
        this.$emit(
          'change',
          targetItem.map((item) => item.value)
        );
      } else {
        this.value = value.label;
        this.$emit(
          'change',
          this.opt?.find((item) => item.label === this.value)?.value
        );
      }
      if (this.value) this.optionsOpen = false;
    },
    onCancel() {
      this.optionsOpen = false;
    },
    removeTag() {
      this.isRemoving = true;
      console.log(this.value);
    },
  },
};
</script>
