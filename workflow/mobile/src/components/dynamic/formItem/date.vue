<template>
  <div>
    <van-field
      readonly
      clickable
      name="datetimePicker"
      :value="value"
      :label="`${fieldInfo.name}：`"
      placeholder="点击选择时间"
      @click="showPicker = true"
    />
    <van-popup
      v-model="showPicker"
      round
      get-container="body"
      position="bottom"
    >
      <van-datetime-picker
        v-model="currentDate"
        type="datehour"
        title="选择年月日小时"
        :min-date="minDate"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'formItem-date',
  props: {
    data: String,
    fieldInfo: Object,
    createdTime: String,
    formData: Object,
  },
  created() {
    this.$emit('change', '');
  },
  mounted() {
    if (this.createdTime) {
      this.minDate = new Date();
    } else {
      // 待验证
      this.minDate = Date.now() - 8.64e7;
    }
  },
  data() {
    return {
      value: '',
      showPicker: false,
      minDate: new Date(2020, 0, 1),
      currentDate: new Date(),
    };
  },
  watch: {
    data(val) {
      if (val === '') {
        this.value = '';
      }
    },
  },
  methods: {
    onConfirm(time) {
      // 格式化
      if (this.fieldInfo.params && this.fieldInfo.params.type === 'date') {
        this.value = this.$moment(time).format('YYYY-MM-DD');
        this.$emit('change', `${this.$moment(time).format('YYYYMMDD')}000000`);
      } else {
        this.value = this.$moment(time).format('YYYY-MM-DD HH时');
        this.$emit('change', `${this.$moment(time).format('YYYYMMDDHH')}0000`);
      }
      this.showPicker = false;
    },
  },
};
</script>
