<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="请选择人员"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="options.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="options"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <van-empty description="暂无数据" v-if="options.length === 0" />
    </van-popup>
  </div>
</template>

<script>
import {allCode} from '@/utils/filter.js';
export default {
  name: 'formItem-codeSelect',
  props: {
    fieldInfo: Object,
    data: String,
    formdata: Object,
  },
  mounted() {
    try {
      this.value = this.data;
      const data = allCode(this.fieldInfo.params.selectId);
      this.options = data;
      this.value = this.options[0].label;
      this.change();
      // 从流程中获取默认值
      if (!!this.fieldInfo.params.default) {
        this.value = this.options?.find(
          (item) => item.value === this.fieldInfo.params.default
        )?.label;
        this.change();
      }
      // 从工单信息中获取
      this.$nextTick(() => {
        if (!!this.fieldInfo.params.defaultvari) {
          const _t =
            this.formdata.processVariables[this.fieldInfo.params.defaultvari];
          this.value = this.options?.find((item) => item.value === _t)?.label;
          this.change();
        }
      });
    } catch (error) {
      console.log(error);
    }
  },
  data() {
    return {
      options: [],
      value: null,
      optionsOpen: false,
    };
  },
  methods: {
    change() {
      this.$emit(
        'change',
        this.options?.find(
          (item) => item.label === this.value || item.value === this.value
        )?.value
      );
    },
    onConfirm(value) {
      this.value = value.label;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
  },
};
</script>
