<template>
  <div>
    <div v-if="info.reply_improvementPoints === '01'">
      <van-field
        v-model="value"
        :required="fieldInfo.required && !fieldInfo.readOnly"
        :disabled="info.reply_improvementPoints === undefined"
        readonly
        is-link
        type="text"
        :label="fieldInfo.name + '：'"
        :name="fieldInfo.id"
        :placeholder="`请填写${fieldInfo.name}`"
        @click="optionsOpen = true"
        :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
      />
      <van-popup
        v-model="optionsOpen"
        round
        get-container="body"
        position="bottom"
      >
        <van-picker
          v-if="options.length > 0"
          value-key="name"
          title="请选择"
          show-toolbar
          :columns="options"
          @confirm="onConfirm"
          @cancel="onCancel"
        />
        <van-empty description="暂无数据" v-if="options.length === 0" />
      </van-popup>
    </div>
    <van-field
      v-else
      v-model="value"
      @input="$emit('change', value)"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="info.reply_improvementPoints === undefined"
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请填写${fieldInfo.name}`"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
  </div>
</template>

<script>
export default {
  name: 'formItem-improveMode_dropdown',
  props: {
    fieldInfo: null,
    data: {
      type: String,
    },
    info: {
      type: Object,
    },
  },
  mounted() {
    if (this.fieldInfo) {
      this.options = this.fieldInfo.options;
    }
    this.value = this.data;
  },
  data() {
    return {
      value: '',
      options: [],
      optionsOpen: false,
    };
  },
  methods: {
    change() {
      this.$emit(
        'change',
        this.fieldInfo.options?.find((item) => item.name === this.value)?.id
      );
    },
    onConfirm(value) {
      this.value = value.name;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = '';
      }
    },
    'info.reply_improvementPoints'() {
      this.value = '';
      this.$emit('change', this.value);
    },
  },
};
</script>
