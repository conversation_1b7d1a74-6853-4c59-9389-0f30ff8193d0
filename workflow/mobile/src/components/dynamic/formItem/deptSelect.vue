<template>
  <div>
    <van-field
      v-model="positionValue.text"
      is-link
      :required="fieldInfo.required && !fieldInfo.readOnly"
      readonly
      label="部门："
      placeholder="请选择所在部门"
      @click="positionOpen = true"
      :rules="[{ required: true }]"
    />
    <!-- 转派人员选择 -->
    <van-popup
      v-model="positionOpen"
      round
      position="bottom"
      get-container="body"
    >
      <van-picker
        ref="picker"
        title="请选择"
        value-key="label"
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </van-popup>
  </div>
  <!-- <el-cascader
    style="width: 250px"
    :show-all-levels="false"
    clearable
    v-model="value"
    :options="options"
    :props="{ expandTrigger: 'hover', checkStrictly: true }"
    @change="change"
    :disabled="fieldInfo.readOnly"
    size="small"
  >
  </el-cascader> -->
</template>

<script>
export default {
  name: 'formItem-deptSelect',
  props: {
    fieldInfo: Object,
    data: [String, Array],
    formdata: Object,
  },
  mounted() {
    // 初始化公司/部门信息
    const {dept} = JSON.parse(localStorage.getItem('dic'));

    const maxDeep = this.getDepths(dept[0], 0, []);
    // 补全深度
    this.handleDepth(dept[0], 0, maxDeep);
    this.columns = dept;
  },
  data() {
    return {
      options: null,
      value: [],
      positionValue: {
        value: null,
        text: null,
      },
      positionOpen: false,
      columns: [],
    };
  },
  watch: {
    data(val) {
      !val && (this.value = []);
    },
  },
  methods: {
    // 级联获取
    onConfirm() {
      const target = this.$refs.picker.getValues();
      const {length} = target;
      this.positionValue = {
        value: null,
        text: null,
      };
      for (let index = length - 1; index >= 0; index--) {
        const element = target[index];
        if (element.label !== '默认' && this.positionValue.value === null) {
          this.positionValue = {
            text: element.label,
            value: element.value,
          };
        }
      }
      this.value = target
        .filter((item) => item.value)
        .map((item) => item.value);
      this.$emit('change', this.value);
      this.$EventBus.$emit('clearVal', this.value);
      this.positionOpen = false;
    },
    onCancel() {
      this.positionOpen = false;
    },
    /**
     * @Author: fishDre
     * @description: 递归处理, 根据数据深度补全不足的数据
     * @param {*} dept
     * @return {*}
     */
    handleDepth(root, deep, maxDeep) {
      // 边界处理
      if (root !== null) deep++;
      else return 0;

      // 匹配组件属性
      root.children = root.childs;

      if (
        root.children === null ||
        (Array.isArray(root.children) && root.children.length === 0)
      ) {
        root.children = null;
        // 深度对比
        if (deep < maxDeep) {
          const count = maxDeep - deep;
          let target = root;
          for (let index = 0; index < count; index++) {
            target.children = [];
            target.children.push({
              label: '默认',
              children: null,
            });
            // eslint-disable-next-line prefer-destructuring
            target = target.children[0];
          }
        }
      } else {
        // 给每一项增加默认值
        root.childs.unshift({
          label: '默认',
          childs: [],
        });
        for (const item of root.childs) {
          this.handleDepth(item, deep, maxDeep);
        }
      }
    },

    /**
     * @Author: fishDre
     * @description: 获取树形的深度
     * @param {*} root
     * @param {*} deep
     * @param {*} deepArr
     * @return {*}
     */
    getDepths(root, deep, deepArr) {
      if (root !== null) deep++;
      else return 0;
      // 到底了
      if (
        root.childs === null ||
        (Array.isArray(root.childs) && root.childs.length === 0)
      ) {
        // 记录深度
        deepArr.push(deep);
      } else {
        for (const item of root.childs) {
          this.getDepths(item, deep, deepArr);
        }
      }
      return Math.max(...deepArr);
    },
    // change() {
    //   // 切换部门时，前一个部门选择的人不保留那就放开 this.$bus.$emit('clearVal', this.value); 如果需要保留，那就注释该语句
    //   // this.$bus.$emit('clearVal', this.value);
    //   // this.$emit('change', this.value[this.value.length - 1]);
    //   this.$emit('change', this.value);
    // },
  },
};
</script>
