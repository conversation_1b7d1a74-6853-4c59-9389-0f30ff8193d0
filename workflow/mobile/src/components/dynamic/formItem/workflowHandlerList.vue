<template>
  <div>
    <van-field
      v-model="label"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="请选择人员"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="UserList.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="UserList"
        @confirm="onConfirm"
        @cancel="onCancel"
      >
        <template #option="target">
          <div
            style="width: 60%; display: flex; justify-content: space-between"
          >
            <span>{{ target.label }}</span
            ><van-checkbox
              v-model="target.checked"
              ref="vanchecks"
            ></van-checkbox>
          </div>
        </template>
      </van-picker>
      <van-empty description="暂无数据" v-if="UserList.length === 0" />
    </van-popup>
  </div>
</template>

<script>
import {getUserByRole} from '@/api/common/index.js';
export default {
  components: {},
  name: 'formItem-workflowHandlerList',
  props: {
    data: Array,
    fieldInfo: Object,
  },
  mounted() {
    // 字段名 userName
    this.getUserInfo();
  },
  data() {
    return {
      label: '',
      UserList: [],
      optionsOpen: false,
    };
  },
  watch: {
    data(val) {
      if (!val) {
        this.label = null;
        this.getUserInfo();
      }
    },
  },
  created() {},
  methods: {
    onConfirm() {
      this.label = null;
      const option = this.$refs.vanchecks.$parent.options;
      const targetItem = option.filter((item) => item.checked);
      if (targetItem.length === 0) {
        this.$Toast('请选择');
        return;
      }
      this.label = targetItem.map((item) => item.label).join();
      this.$emit(
        'change',
        targetItem.map((item) => item.value)
      );
      this.optionsOpen = false;
    },
    onCancel() {
      this.optionsOpen = false;
    },
    // 获取数据
    getUserInfo() {
      getUserByRole({roleId: this.fieldInfo.params.userRoleId}).then(
        (res) => {
          if (res) {
            this.UserList = res.data.map((item) => ({
              ...item,
              checked: false,
            }));
          }
        }
      );
    },
  },
};
</script>
