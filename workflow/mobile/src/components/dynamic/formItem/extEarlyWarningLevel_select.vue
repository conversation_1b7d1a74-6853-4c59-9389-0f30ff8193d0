<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请填写${fieldInfo.name}`"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="options.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="options"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <van-empty description="暂无数据" v-if="options.length === 0" />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'formItem-extEarlyWarningLevel_select',
  props: {
    data: String,
  },
  mounted() {
    const WarningLevel = JSON.parse(
      sessionStorage.getItem('extEarlyWarningLevel')
    );
    this.options = WarningLevel;
  },
  created() {
    this.change();
  },
  destroyed() {
    this.$emit('change', undefined);
  },
  data() {
    return {
      value: '',
      optionsOpen: false,
      options: [],
    };
  },
  methods: {
    change() {
      this.$emit(
        'change',
        this.options.find((item) => item.label === this.value)?.value
      );
    },
    onConfirm(value) {
      this.value = value.label;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
  },
  watch: {
    data(val) {
      if (!val) {
        val = '';
      }
    },
  },
};
</script>
