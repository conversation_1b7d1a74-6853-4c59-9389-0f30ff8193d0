<template>
  <div>
    <van-field
      v-model="value"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="上传附件模板下载"
      @click="optionsOpen = true"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
      style="min-height: 40%"
    >
      <van-cell-group v-if="fileName">
        <van-cell
          v-for="(item, index) in fileName ? fileName.split('|') : []"
          :key="index"
          value="下载"
          arrow-direction="down"
          is-link
          @click="downAttachList(item)"
        >
          <!-- 使用 title 插槽来自定义标题 -->
          <template #title>
            <span class="custom-title">{{ item }}</span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-empty description="暂无数据" v-if="!fileName" />
    </van-popup>
  </div>
</template>

<script>
// 下载模板附件的组件
export default {
  name: 'formItem-modelDown',
  props: {
    data: String,
    fieldInfo: Object,
  },
  created() {
    this.$emit('change', '');
  },
  mounted() {
    this.fileName = this.fieldInfo.value;
  },
  data() {
    return {
      fileName: null,
      value: null,
      optionsOpen: false,
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downModelAttachList`,
    };
  },
  watch: {},
  methods: {
    change() {
      this.$emit('change', this.fileName);
    },
    // 下载模板附件
    downAttachList(item) {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.target = '_blank';
      a.href = `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downModelAttachList?attachList=${item}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
  },
};
</script>
<style lang="scss" scoped>
.van-cell__title {
  flex: 2;
}
</style>
