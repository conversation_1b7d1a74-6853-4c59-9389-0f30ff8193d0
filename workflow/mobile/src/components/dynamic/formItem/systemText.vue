<template>
  <!-- <div> -->
  <el-input
    v-model="value"
    @change="change"
    style="max-width: 300px"
    size="small"
    :disabled="fieldInfo.readOnly"
    :placeholder="`请填写${fieldInfo.name}`"
  >
  </el-input>
  <!-- <Test :asd="asdf"/> -->
  <!-- </div> -->
</template>

<script>
// 针对档案编号的组件(现在在扩建工单里面不能用) 扩建工单里面的
import {getFlowCode} from '@/api/common/index.js';
// import Test from './test';
export default {
  name: 'formItem-systemText',
  props: {
    data: String,
    fieldInfo: Object,
    formdata: Object,
  },
  mounted() {
    this.change();
    this.value = this.data;
    this.getCode();
    // setTimeout(() => {
    //   this.asdf = 'qwe';
    //   console.log(this.asdf);
    // }, 5000);
  },
  data() {
    return {
      value: '',
      // asdf: '',
    };
  },
  watch: {
    data(val) {
      this.value = val;
      val || this.getCode();
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
    // 获取后台生成的代码
    getCode() {
      getFlowCode({
        contentId:
          this.formdata?.processVariables[
            this.fieldInfo.params && this.fieldInfo.params.variable
          ],
        typeId: this.fieldInfo.id,
      }).then((res) => {
        this.value = res.data[0].label;
        this.change();
      });
    },
  },
};
</script>
