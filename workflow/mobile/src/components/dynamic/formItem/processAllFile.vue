<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请选择${fieldInfo.name}`"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="options?.length > 0"
        value-key="name"
        title="请选择"
        show-toolbar
        :columns="options"
        @confirm="onConfirm"
        @cancel="onCancel"
      >
        <template #option="target">
          <div
            style="width: 60%; display: flex; justify-content: space-between"
          >
            <span>{{ target.name }}</span
            ><van-checkbox
              v-model="target.checked"
              ref="vanchecks"
            ></van-checkbox>
          </div>
        </template>
      </van-picker>
      <van-empty description="暂无数据" v-if="options?.length === 0" />
    </van-popup>
  </div>

  <!-- <el-select
    v-model="value"
    :multiple="'multiple'"
    :disabled="fieldInfo.readOnly"
    @change="change"
    style="width: 250px"
    :placeholder="`请选择${fieldInfo.name}`"
    size="small"
  >
    <el-option
      size="small"
      v-for="item of options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    ></el-option>
  </el-select> -->
</template>

<script>
import {getProcessAllFile} from '@/api/workflow/index.js';

export default {
  name: 'formItem-processAllFile',
  props: {
    fieldInfo: Object,
    data: Array,
    formdata: Object,
  },
  created() {
    this.getAllFile();
  },
  data() {
    return {
      value: '',
      options: [],
      optionsOpen: false,
    };
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = val;
      }
    },
  },
  methods: {
    onConfirm() {
      this.value = null;
      const option = this.$refs.vanchecks.$parent.options;
      const targetItem = option.filter((item) => item.checked);
      if (targetItem.length === 0) {
        this.$Toast('请选择');
        return;
      }
      this.value = targetItem.map((item) => item.name).join();
      this.$emit(
        'change',
        targetItem.map((item) => item.id)
      );
      if (this.value) this.optionsOpen = false;
    },
    onCancel() {
      this.optionsOpen = false;
    },
    async getAllFile() {
      const data = await getProcessAllFile(
        this.formdata && this.formdata.processInstanceId
      );
      this.options =
        data &&
        data.map((item) => ({
          id: item.id,
          name: item.name,
          checked: false,
        }));
    },
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
