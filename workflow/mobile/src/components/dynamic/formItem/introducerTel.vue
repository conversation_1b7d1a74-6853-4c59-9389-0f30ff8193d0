<template>
  <el-input
    v-model="value"
    @change="change"
    :placeholder="`请填写${fieldInfo.name}`"
    style="width: 100%; max-width: 300px"
    size="small"
  >
  </el-input>
</template>

<script>
export default {
  name: 'formItem-introducerTel',
  props: {
    data: String,
    fieldInfo: Object,
  },
  mounted() {
    this.value = this.data;
  },
  updated() {
    console.log('before');
    // this.value = this.data;
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = '';
      } else {
        this.value = this.data;
      }
    },
  },
  data() {
    return {
      value: null,
      once: false,
    };
  },
  methods: {
    change() {
      console.log('change');
      console.log(this.value);
      this.$emit('change', this.value);
    },
  },
};
</script>
