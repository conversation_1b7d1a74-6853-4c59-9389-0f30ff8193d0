<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="请选择人员"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-cascader
        ref="cascader"
        v-model="cascaderValue"
        title="请选择"
        :field-names="fieldName"
        :options="options"
        @close="optionsOpen = false"
        @finish="onFinish"
      />
      <van-empty description="暂无数据" v-if="options.length === 0" />
    </van-popup>
  </div>
</template>

<script>
import {allCode} from '@/utils/filter.js';
export default {
  name: 'formItem-moreSelect',
  props: {
    fieldInfo: Object,
    data: {
      type: [Array, String],
    },
    formdata: Object,
  },
  mounted() {
    try {
      this.value = this.data;
      const data = allCode(this.fieldInfo.params.selectId);
      console.log(data);
      let options = [];
      options = this.handlerCascaderData(data);
      this.options = options;
      this.value = this.findLabelByValue(
        options,
        this.fieldInfo.value,
        []
      )?.join('/');
      this.cascaderValue = this.fieldInfo.value;
      this.$emit('change', this.fieldInfo.value);
      // 从流程中获取默认值
      if (!!this.fieldInfo.params.default) {
        this.value = this.findLabelByValue(
          options,
          this.fieldInfo.params.default,
          []
        )?.join('/');
        this.cascaderValue = this.fieldInfo.params.default;
        this.$emit('change', this.fieldInfo.params.default);
      }
      // 从工单信息中获取
      this.$nextTick(() => {
        if (!!this.fieldInfo.params.defaultvari) {
          this.value = this.findLabelByValue(
            options,
            this.fieldInfo.params.defaultvari,
            []
          )?.join('/');
          this.cascaderValue =
            this.formdata.processVariables[this.fieldInfo.params.defaultvari];
          this.$emit(
            'change',
            this.formdata.processVariables[this.fieldInfo.params.defaultvari]
          );
        }
      });
      console.log(this.fieldInfo);
      console.log(this.formdata);
      console.log(this.data);
      console.log(this.options);
    } catch (error) {
      console.log('错误', error);
    }
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = '';
      }
    },
  },
  updated() {
    console.log('updated');
  },
  data() {
    return {
      options: [],
      cascaderValue: '',
      value: null,
      optionsOpen: false,
      fieldName: {text: 'label', value: 'value', children: 'children'},
    };
  },
  methods: {
    onFinish({selectedOptions}) {
      this.optionsOpen = false;
      this.value = selectedOptions.map((option) => option.label).join('/');
      this.$emit(
        'change',
        selectedOptions.map((option) => option.value)
      );
    },

    /**
     * @Author: fishDre
     * @description: 根据value获取所有父级数组
     * @param {*} root
     * @param {*} value
     * @param {*} stack
     * @return {*}
     */
    findLabelByValue(root, value, stack) {
      if (!root) return;
      if (Array.isArray(root)) {
        for (const iterator of root) {
          if (iterator.value === value) {
            stack.unshift(iterator.label);
            return stack;
          }
          if (iterator.children && Array.isArray(iterator.children)) {
            const target = this.findLabelByValue(
              iterator?.children,
              value,
              stack
            );
            if (target) {
              stack.unshift(iterator.label);
              return target;
            }
          }
        }
      }
    },
    // 处理cascader数据
    handlerCascaderData(arr) {
      if (Array.isArray(arr)) {
        arr.forEach((i) => {
          if (Array.isArray(i.childs) && i.childs.length) {
            i.children = i.childs;
            this.handlerCascaderData(i.childs);
          }
        });
      }
      return arr;
    },
  },
};
</script>
