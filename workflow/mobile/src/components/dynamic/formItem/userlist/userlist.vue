<template>
  <div>
    <el-popover
      class="popover"
      placement="bottom-start"
      width="540"
      trigger="click"
      @show="showPopover"
    >
      <div class="select_user">
        <span>城市：</span>
        <el-select
          v-model="departmentId"
          placeholder="请选择城市"
          size="small"
          class="selectdown"
          @change="getUser"
        >
          <el-option
            v-for="item in DepartmentList"
            :key="item.id"
            :label="item.dapartmentName"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <span>人员：</span>
        <el-select
          v-model="userid"
          placeholder="请选择人员"
          size="small"
          class="selectdown"
          :multiple="true"
          collapse-tags
          @change="getUserMan"
        >
          <el-option
            v-for="item in UserList"
            :key="item.userid"
            :label="item.username"
            :value="item.userid"
          >
          </el-option>
        </el-select>
      </div>

      <el-input
        slot="reference"
        size="small"
        v-model="username"
        placeholder="请选择处理人"
        readonly
        style="width: 40%"
      ></el-input>
    </el-popover>
    <el-input
      v-model="comments"
      @change="pushData"
      size="small"
      placeholder="请输入处理意见"
      style="width: 80%; display: block; margin-top: 8px"
    ></el-input>
  </div>
</template>

<script>
import {getDepartmentList, getUserList} from '@/api/form/index.js';
import filter from '@/utils/filter.js';
export default {
  components: {},
  name: 'UserList',
  props: {
    data: Array,
    fieldInfo: Object,
  },
  mounted() {
    // 字段名 userName
    this.userid = this.data;
  },
  data() {
    return {
      comments: '',
      useridArr: [],
      departmentId: '',
      DepartmentList: [],
      username: '',
      userid: [],
      UserList: [],
    };
  },
  created() {
    getDepartmentList().then((res) => {
      this.DepartmentList = res;
    });
  },
  // watch: {
  //   data(val) {
  //     // 清空数据
  //     if (val === '') {
  //       this.username = val;
  //       this.userid = '';
  //     }
  //   },
  // },
  methods: {
    showPopover() {},
    getUser() {
      const data = this.DepartmentList.filter(
        (item) => item.id === this.departmentId
      );
      const config = {
        cityLevelId: data[0].cityLevelId,
        departmentId: data[0].dapartmentId,
      };

      getUserList(config).then((res) => {
        if (res) {
          this.UserList = res;
        }
      });
    },
    getUserMan() {
      this.username = '';
      this.userid.forEach((item) => {
        this.username += `${filter.assignee(item)}  `;
      });
      // console.log('名字',this.username);
      // console.log('选择的',this.userid);
      this.pushData();
    },
    pushData() {
      this.$emit('push-info', {userid: this.userid, comments: this.comments});
    },
  },
};
</script>
<style>
.select_user {
  display: inline-flex;
  align-items: center;
}
.select_user span {
  white-space: nowrap; /*强制span不换行*/
  display: inline-block; /*将span当做块级元素对待*/
  width: 38px; /*限制宽度*/
  vertical-align: bottom;
}
.popover .el-input__inner:hover {
  border-color: #ff9900;
}
.select_user i.el-input__icon.el-input__validateIcon.el-icon-circle-close {
  display: none;
}
.select_user .el-select .el-input.is-focus .el-input__inner {
  border-color: #ff9900;
}
</style>
<style lang="scss" scoped>
$btn_color: #ff9900;
.btn1 {
  margin-left: 8px;
  font-family: SourceHanSansSC-Regular;
  position: relative;
  top: -1px;
}
.el-button:focus,
.el-button:hover {
  border-color: $btn_color;
  background: none;
  color: #606266;
}
</style>
