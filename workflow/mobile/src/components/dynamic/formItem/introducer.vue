<template>
  <el-select
    v-model="userid"
    placeholder="请选择或填写问题提出人"
    size="small"
    @change="getUserMan"
    @blur="judgeOption"
    style="width: 25%"
    filterable
    ref="search"
    allow-create
  >
    <el-option
      v-for="(item, index) in userList"
      :key="index"
      :label="item.username"
      :value="item.userid"
    >
    </el-option>
  </el-select>
</template>

<script>
import {getUserList} from '@/api/common/index.js';
export default {
  name: 'formItem-introducer',
  props: {
    data: [Array, String],
    fieldInfo: Object,
  },
  mounted() {
    this.getUserInfo();
  },
  created() {
    this.$emit('change', []);
  },
  data() {
    return {
      userid: '',
      userList: [],
    };
  },
  watch: {
    data(val) {
      if (val.length === 0) {
        this.userid = '';
      }
    },
  },
  methods: {
    // 获取数据
    getUserInfo() {
      getUserList({}).then((res) => {
        if (res) {
          this.userList = res;
        }
      });
    },
    // 选择回调
    getUserMan() {
      const selectedUser = this.userList.filter(
        (ele) => ele.userid === this.userid
      );
      if (selectedUser[0]) {
        this.$emit('change', selectedUser[0].username);
        this.$emit('userChange', selectedUser[0].mobilePhone);
      } else {
        this.$emit('change', this.userid);
        this.$emit('userChange', null);
      }
    },
    updated() {
      console.log('updated');
    },
    judgeOption() {
      // setTimeout(() => {
      //   setTimeout(() => {
      //     console.log(this.$refs.search.$children[0].$el.childNodes[1].value);
      //     console.dir(this.$refs.search.$children[0].$el.childNodes[1]);
      //   }, 100)
      // }, 0);
    },
  },
};
</script>
