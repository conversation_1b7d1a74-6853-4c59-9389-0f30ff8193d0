<template>
  <el-select
    v-model="value"
    :multiple="fieldInfo.optionType === 'multiple'"
    @change="change"
    size="small"
  >
    <el-option
      v-for="item of options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
// 未找到
import {getFlowCode} from '@/api/common/index.js';
export default {
  name: 'formItem-selectByVariable',
  props: {
    data: String,
    fieldInfo: Object,
    formdata: Object,
  },
  mounted() {
    // 判断是否有工单细类
    if (
      this.formdata.processVariables[
        this.fieldInfo.params && this.fieldInfo.params.variable
      ]
    ) {
      this.getCode();
    } else {
      // 无工单细类取默认值
      this.fieldInfo.value && (this.value = this.fieldInfo.value);
      this.change();
      if (this.fieldInfo) {
        this.options = this.fieldInfo.options;
        this.value = this.fieldInfo.options[0].id;
        this.change();
      }
    }
  },
  data() {
    return {
      options: [],
      value: '',
    };
  },
  watch: {
    data(val) {
      this.value = val;
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
    // 获取后台生成的数据
    getCode() {
      getFlowCode({
        contentId:
          this.formdata.processVariables[
            this.fieldInfo.params && this.fieldInfo.params.variable
          ],
        typeId: this.fieldInfo.id,
      }).then((res) => {
        console.log(res);
        this.options = res.data;
        // 取默认值
        this.value = res.data[0].value;
        this.change();
      });
    },
  },
};
</script>
