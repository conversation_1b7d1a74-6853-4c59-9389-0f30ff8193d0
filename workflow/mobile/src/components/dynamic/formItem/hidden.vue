<template>
  <van-field
    v-model="value"
    @input="change"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    disabled
    type="text"
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="`请填写${fieldInfo.name}`"
    :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
  />
</template>

<script>
export default {
  name: 'formItem-readOnly',
  props: {
    data: String,
    fieldInfo: Object,
  },
  mounted() {
    this.value = this.fieldInfo.value;
    this.change();
  },
  data() {
    return {
      value: '',
    };
  },
  watch: {
    // data(val) {
    //   this.value = val;
    // },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
