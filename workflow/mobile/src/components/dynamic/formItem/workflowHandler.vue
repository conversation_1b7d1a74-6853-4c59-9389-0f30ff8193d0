<template>
  <div>
    <van-field
      v-model="userid"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="请选择人员"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="UserList.length > 0"
        value-key="label"
        title="请选择"
        show-toolbar
        :columns="UserList"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <van-empty description="暂无数据" v-if="UserList.length === 0" />
    </van-popup>
  </div>
</template>

<script>
import {getUserByRole} from '@/api/common/index.js';
export default {
  components: {},
  name: 'formItem-workflowHandler',
  props: {
    data: String,
    fieldInfo: Object,
  },
  mounted() {
    // 字段名 userName
    this.getUserInfo();
  },
  created() {},
  data() {
    return {
      userid: '',
      UserList: [],
      optionsOpen: false,
    };
  },
  watch: {},
  methods: {
    onConfirm(value) {
      console.log(value);
      this.userid = value.label;
      this.optionsOpen = false;
      this.getUserMan();
    },
    onCancel() {
      this.optionsOpen = false;
    },
    // 获取数据
    getUserInfo() {
      getUserByRole({roleId: this.fieldInfo.params.userRoleId}).then(
        (res) => {
          if (res) {
            this.UserList = res.data;
          }
        }
      );
    },
    // 选择回调
    getUserMan() {
      this.$emit(
        'change',
        this.UserList?.find((item) => item.label === this.userid)?.value
      );
    },
  },
};
</script>
