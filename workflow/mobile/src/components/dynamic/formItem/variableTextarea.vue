<template>
  <van-field
    v-model="value"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    :disabled="fieldInfo.readOnly"
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="`请填写${fieldInfo.name}`"
    :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    :rows="fieldInfo.params.rows"
    autosize
    @input="change"
    type="textarea"
  />
</template>

<script>
// 针对个别档案编号
export default {
  name: 'formItem-variableTextarea',
  props: {
    data: String,
    fieldInfo: Object,
    formdata: Object,
  },
  mounted() {
    if (this.fieldInfo.params && this.fieldInfo.params.variable) {
      this.value =
        this.formdata.processVariables[
          this.fieldInfo.params && this.fieldInfo.params.variable
        ];
    } else {
      this.value = this.formdata.processVariables[this.fieldInfo.id];
    }
    this.change();
  },
  data() {
    return {
      value: '',
    };
  },
  watch: {
    data(val) {
      this.value = val;
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
