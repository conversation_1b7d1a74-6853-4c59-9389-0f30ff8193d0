<template>
  <Select
    v-model="value"
    :multiple="fieldInfo.optionType === 'multiple'"
    @change="change"
    filterable
    :disabled="fieldInfo.readOnly"
    size="small"
  >
    <el-option
      v-for="item of options"
      :key="item.id"
      :label="item.name"
      :value="item.name"
    ></el-option>
  </Select>
</template>

<script>
import {Select} from 'element-ui';
import linkageOption from '@/components/dynamic/formItem/linkageJSON/linkage.js';
export default {
  components: {Select},
  name: 'formItem-linkageL1',
  props: {
    fieldInfo: Object,
    data: String,
  },
  created() {
    this.value = this.data;
    // if (this.fieldInfo) {
    //   this.options = this.fieldInfo.options;
    // }
    this.options = linkageOption.options;
  },
  data() {
    return {
      options: null,
      value: null,
    };
  },
  watch: {
    data(value) {
      if (!value) {
        this.value = null;
      }
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
