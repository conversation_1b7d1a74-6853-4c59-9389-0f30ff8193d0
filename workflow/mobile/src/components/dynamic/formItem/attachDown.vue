<template>
  <div>
    <van-field
      v-if="fileName"
      v-model="value"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      placeholder="下载流程附件"
      @click="optionsOpen = true"
    />
    <van-field
      v-model="value"
      :label="fieldInfo.name + '：'"
      placeholder="该工单暂无附件"
      disabled
      v-else
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
      style="min-height: 40%"
    >
      <van-cell-group>
        <van-cell
          v-for="(item, index) in fileName ? fileName.split('|') : []"
          :key="index"
          value="下载"
          arrow-direction="down"
          is-link
          @click="downAttachList(item)"
        >
          <!-- 使用 title 插槽来自定义标题 -->
          <template #title>
            <span class="custom-title">{{ item }}</span>
          </template>
        </van-cell>
      </van-cell-group>
    </van-popup>
  </div>
</template>

<script>
// 回复附件下载的组件, 从流程变量获取文件名
export default {
  name: 'formItem-attachDown',
  props: {
    data: String,
    formdata: Object,
    fieldInfo: Object,
  },
  created() {
    this.$emit('change', '');
  },
  mounted() {
    this.fileName = this.formdata.processVariables.attachList;
  },
  data() {
    return {
      fileName: null,
      value: null,
      optionsOpen: false,
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList`,
    };
  },
  watch: {},
  methods: {
    change() {
      this.$emit('change', this.fileName);
    },
    // 下载模板附件
    downAttachList(item) {
      const a = document.createElement('a');
      a.style.display = 'none';
      a.target = '_blank';
      a.href = `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList?attachList=${item}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
  },
};
</script>
<style lang="scss" scoped>
.van-cell__title {
  flex: 2;
}
</style>
