<template>
  <Select
    v-model="value"
    :multiple="fieldInfo.optionType === 'multiple'"
    @change="change"
    filterable
    :disabled="fieldInfo.readOnly"
    placeholder="请先选择督办事项"
    size="small"
  >
    <el-option
      v-for="item of options"
      :key="item.id"
      :label="item.name"
      :value="item.name"
    ></el-option>
  </Select>
</template>

<script>
import linkageOption from '@/components/dynamic/formItem/linkageJSON/linkage.js';
import {Select} from 'element-ui';
export default {
  components: {Select},
  name: 'formItem-linkageL2',
  props: {
    fieldInfo: Object,
    data: String,
    info: {
      type: Object,
    },
  },
  created() {
    this.value = this.data;
  },
  data() {
    return {
      options: null,
      value: null,
    };
  },
  watch: {
    'info.supervisoryMatters'(val) {
      if (val) {
        this.options = [];
        this.value = null;
        this.change();
        const index = linkageOption.options.findIndex(
          (item) => item.name === val
        );
        this.options = linkageOption.options[index].children;
      } else {
        this.value = null;
        this.$emit('change', null);
      }
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
