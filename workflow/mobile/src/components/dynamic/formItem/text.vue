<template>
  <van-field
    v-model="value"
    :required="fieldInfo.required && !fieldInfo.readOnly"
    @input="change"
    type="text"
    :label="fieldInfo.name + '：'"
    :name="fieldInfo.id"
    :placeholder="`请填写${fieldInfo.name}`"
  />
</template>

<script>
import Vue from 'vue';
import {Field} from 'vant';
Vue.use(Field);
export default {
  name: 'formItem-text',
  props: {
    data: String,
    fieldInfo: Object,
  },
  mounted() {
    this.change();
    this.value = this.data;
  },
  data() {
    return {
      value: '',
    };
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = '';
      }
    },
  },
  methods: {
    change() {
      this.$emit('change', this.value);
    },
  },
};
</script>
