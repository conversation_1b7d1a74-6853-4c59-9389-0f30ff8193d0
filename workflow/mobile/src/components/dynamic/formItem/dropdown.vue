<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请填写${fieldInfo.name}`"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="fieldInfo.options.length > 0"
        value-key="name"
        title="请选择"
        show-toolbar
        :columns="fieldInfo.options"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <van-empty description="暂无数据" v-if="fieldInfo.options.length === 0" />
    </van-popup>
  </div>
  <!-- <el-select
    style="width: 250px"
    v-model="value"
    :multiple="fieldInfo && fieldInfo.optionType === 'multiple'"
    :disabled="fieldInfo.readOnly"
    @change="change($event)"
    :placeholder="`请选择${fieldInfo.name}`"
    size="small"
  >
    <el-option
      size="small"
      v-for="item of fieldInfo.options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    ></el-option>
  </el-select> -->
</template>

<script>
import {queryIdentyDetail} from '@/api/common/index.js';
import Vue from 'vue';
import {Popup, Form, Field, Empty, Picker} from 'vant';
Vue.use(Popup).use(Form).use(Field).use(Picker).use(Empty);
export default {
  name: 'formItem-dropdown',
  props: {
    fieldInfo: Object,
    data: String,
    subtype: String,
  },
  created() {
    this.change();
    if (this.subtype) {
      this.queryIdenty(this.subtype);
    }
  },
  mounted() {
    console.log(this.fieldInfo.options);
  },
  data() {
    return {
      value: '',
      options: [],
      optionsOpen: false,
    };
  },
  watch: {
    data(val) {
      if (!val) this.value = '';
    },
  },
  methods: {
    onConfirm(value) {
      this.value = value.name;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
    change() {
      const id = this.fieldInfo.options?.find(
        (item) => item.name === this.value
      )?.id;
      this.$emit('change', id);
      const hidden = [];
      const visible = [];
      if (this.fieldInfo.params && this.fieldInfo.params.setVisible) {
        for (const k in this.fieldInfo.params.setVisible) {
          if (id !== k) {
            hidden.push(...(this.fieldInfo.params.setVisible[k] || []));
          } else {
            visible.push(...(this.fieldInfo.params.setVisible[k] || []));
          }
        }
        this.$emit('setVisible', visible, hidden);
      }
    },
    queryIdenty(type) {
      const identySubtype = type;
      queryIdentyDetail({
        // 申请单判断处理
        identyType: identySubtype.substring(0, 2),
      }).then((res) => {
        if (res) {
          this.options = res.data[identySubtype];
        }
      });
    },
  },
};
</script>
