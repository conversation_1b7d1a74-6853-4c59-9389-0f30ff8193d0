<template>
  <div>
    <van-field
      v-model="value"
      :required="fieldInfo.required && !fieldInfo.readOnly"
      :disabled="fieldInfo.readOnly"
      readonly
      is-link
      type="text"
      :label="fieldInfo.name + '：'"
      :name="fieldInfo.id"
      :placeholder="`请填写${fieldInfo.name}`"
      @click="optionsOpen = true"
      :rules="[{ required: true, message: `${fieldInfo.name}不能为空` }]"
    />
    <van-popup
      v-model="optionsOpen"
      round
      get-container="body"
      position="bottom"
    >
      <van-picker
        v-if="options.length > 0"
        value-key="name"
        title="请选择"
        show-toolbar
        :columns="options"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
      <van-empty description="暂无数据" v-if="options.length === 0" />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'formItem-improvePoint_dropdown',
  props: {
    fieldInfo: null,
    data: {
      type: String,
    },
  },
  mounted() {
    if (this.fieldInfo) {
      this.options = this.fieldInfo.options;
    }
    this.value = this.data;
  },
  data() {
    return {
      value: '',
      options: [],
      optionsOpen: false,
    };
  },
  methods: {
    change() {
      console.log(this.value);
      this.$emit(
        'change',
        this.fieldInfo.options?.find((item) => item.name === this.value)?.id
      );
    },
    onConfirm(value) {
      this.value = value.name;
      this.optionsOpen = false;
      this.change();
    },
    onCancel() {
      this.optionsOpen = false;
    },
  },
  watch: {
    data(val) {
      if (!val) {
        this.value = val;
      }
    },
  },
};
</script>
