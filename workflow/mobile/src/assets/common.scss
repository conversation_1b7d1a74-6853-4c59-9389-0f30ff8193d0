 
.home_wrap {
    min-height: 100%;
    background: #eeeeef;
    font-family: 'Source <PERSON>s CN','Source <PERSON>' ,'PingFangSC' ,'Microsoft YaHei','Arial', 'sans-serif';
    div {
      box-sizing: border-box;
    }
    header {
      position: relative;
      background: #fff;
      height: 45px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFang SC;
      font-size: 17px;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: 0px;
      box-shadow: 1px 0.33329999446868896px 0px 0px #0000004D;
      .left_icon {
        position: absolute;
        left: 10px;
      }

    }

    .common_tab_wrap {
      // padding-bottom: 56px;
      width: 100%;
      height: 88px;
      display: flex;
      justify-content: center;
      background: #f7f7f7;
      box-shadow: 0px 1px 8px 0px #0000000F;
      .common_tab_item {
        transition-property: all;
        transition-duration: 0.6s;
        transition-delay: 0.1s;
        border-top: 1px solid #E1E1E1;
        border-left: 1px solid #E1E1E1;
        position: relative;
        width: 33.3%;
        height: 100%;
        box-shadow: rgba(0, 0, 0, 0.06) 0px 2px 4px 0px inset;
        // &:first-child {
        //   border-left: none;
        // }
        img {
          position: absolute;
          left: calc(50% - 14px);
          top: 15px;
        }
        &:first-child {
          img {
            top: 12px;
          }
        }
        .selected_line {
          transition-property: 'width';
          transition-duration: 0.6s;
          transition-delay: 0.1s;
          position: absolute;
          left: 0px;
          width: 100%;
          height: 4px;
          bottom: 0px;
          background: #f7f7f7;
          border-radius: 2px;
        }
        .text_bt {
          display: block;
          width: 100%;
          text-align: center;
          white-space: nowrap;
          position: absolute;
          bottom: 9px;
          left: 0px;
          font-family: Source Han Sans CN;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: 0px;
          color: #4D4D4D;
        }
      }
    }
  }
 .label_wrap_box {
        width: 80%;
        position: relative;
        height: 28px;
        display: flex;
        justify-content: center;
        overflow: auto;
        margin: 16px 0px;
        padding: 0px 10%;
        // 取消滚动条
        &::-webkit-scrollbar {
          display: none;
        }
         scrollbar-width: none;
         /* Firefox */
         -ms-overflow-style: none;
        .warning {
          position: absolute;
          right: 3px;
          top: 3px;
        }
        span {
          font-size: 15px;
          color: #888888;
          height: 26px;
          line-height: 26px;
          display: inline-block;
          white-space: nowrap;
          position: relative;
          padding: 0px 10px;
          &.active {
            color: #2f2f2f;
            font-weight: 700;
            &::after {
              content: '';
              position: absolute;
              left: calc(50% - 1em);
              bottom: -2px;
              height: 4px;
              width: 2em;
              background: #4685fe;
              border-radius: 2px;
            }
          }
        }
      }
.card_wrap_box {
  max-height: 340px;
  border-radius: 6px;
  width: 100%;
  overflow: scroll;
  margin-bottom: 16px;
}
.card_wrap_box.fix {
  max-height: 1000px;
}
footer {
  height: 12px;
  font-size: 11px;
  text-align: center;
  font-family: PingFangSC,
  PingFangSC-Regular;
  font-weight: 400;
  color: #a6a6a6;
  line-height: 12px;
}
//单行文本省略
.row-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

//多行文本省略
.rows-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
// 单位
.unit_text {
  font-size: 12px;
  color: #a8a8a8;
  margin-right: 16px;
}
.van-dialog__header {
  font-weight: bold;

}