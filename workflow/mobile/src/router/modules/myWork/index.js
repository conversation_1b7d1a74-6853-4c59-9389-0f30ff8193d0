export default [
  {
    path: '/workflow-panel',
    name: 'workflow-panel',
    component: () =>
      import(/* webpackChunkName: "workflow" */ '@/views/mywork/index.vue'),
    children: [
      {
        path: 'workflow-detail',
        name: 'workflow-detail',
        redirect: 'workflow-detail/basic-information',
        component: () =>
          import(
            /* webpackChunkName: "workflow" */ '@/views/mywork/workflow/index.vue'
          ),
        children: [
          {
            path: 'basic-information',
            name: 'basic-information',
            component: () =>
              import(
                /* webpackChunkName: "workflow" */ '@/views/mywork/workflow/basic-information/index.vue'
              ),
          },
          {
            path: 'approval-opinions',
            name: 'approval-opinions',
            component: () =>
              import(
                /* webpackChunkName: "workflow" */ '@/views/mywork/workflow/approval-opinions/index.vue'
              ),
          },
          {
            path: 'process-tracking',
            name: 'process-tracking',
            component: () =>
              import(
                /* webpackChunkName: "workflow" */ '@/views/mywork/workflow/process-tracking/index.vue'
              ),
          },
          {
            path: 'view-attachments',
            name: 'view-attachments',
            component: () =>
              import(
                /* webpackChunkName: "workflow" */ '@/views/mywork/workflow/view-attachments/index.vue'
              ),
          },
        ],
      },
    ],
  },
];
