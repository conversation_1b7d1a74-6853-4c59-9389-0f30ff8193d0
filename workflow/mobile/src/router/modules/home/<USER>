export default [
  {
    path: '/home',
    name: 'Home',
    // redirect: '/home/<USER>',
    component: () =>
      import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
    children: [],
  },
  {
    path: '/satisfaction',
    name: 'Satisfaction',
    component: () =>
      import(
        /* webpackChunkName: "satisfaction" */ '@/views/indexView/Satisfaction/index.vue'
      ),
  },
  {
    path: '/declaration',
    name: 'Declaration',
    component: () =>
      import(
        /* webpackChunkName: "declaration" */ '@/views/indexView/Declaration/index.vue'
      ),
  },
  {
    path: '/appraise',
    name: 'Appraise',
    component: () =>
      import(
        /* webpackChunkName: "appraise" */ '@/views/indexView/Appraise/index.vue'
      ),
  },
  {
    path: '/complaints',
    name: 'Complaints',
    component: () =>
      import(
        /* webpackChunkName: "complaints" */ '@/views/indexView/Complaints'
      ),
  },
  {
    path: '/myindex',
    name: 'Myindex',
    component: () =>
      import(
        /* webpackChunkName: "complaints" */ '@/views/indexView/Myindex'
      ),
  },
];
