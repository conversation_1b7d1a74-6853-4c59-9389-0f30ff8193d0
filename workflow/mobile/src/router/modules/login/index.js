export default [
  {
    path: '/login',
    name: 'login',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/login/index.vue'),
  },
  {
    path: '/login_moa',
    name: 'login-moa',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/loginMoa/index.vue'),
  },
  {
    path: '/login_moa2',
    name: 'login-moa2',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/loginMoa2/index.vue'),
  },
  {
    path: '/login_moa_group',
    name: 'login-moa-group',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/loginMoaGroup/index.vue'),
  },
  {
    path: '/login_grid',
    name: 'login_grid',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/loginNew/index.vue'),
  },
  {
    path: '/jump',
    name: 'jump',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/login/jump.vue'),
  }
];

