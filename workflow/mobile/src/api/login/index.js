import request from '../config';

const {investigate} = request.services;

// 获取首页数据
export function getMainData(data = {}) {
  return request({
    url: `${investigate}/login`,
    method: 'post',
    data,
  });
}

export function login(data = {}) {
  return request({
    url: '/login',
    method: 'get',
    params: data,
  });
}
export function gridLogin(data = {}) {
  return request({
    url: '/gridLogin',
    method: 'get',
    params: data,
  });
}
export function moaLogin(data = {}) {
  return request({
    url: '/moaLogin',
    method: 'get',
    params: data,
  });
}
export function moaLogin2(data = {}) {
  return request({
    url: '/moaLogin2',
    method: 'get',
    params: data,
  });
}

export function groupMoaLogin(data = {}) {
  return request({
    url: '/groupMoaLogin',
    method: 'get',
    params: data,
  });
}

export function loginByOAToken(data = {}) {
  return request({
    url: '/login-by-oa-token',
    method: 'get',
    params: data,
  });
}

export function getSendCopyFlag(data = {}) {
  return request({
    url: '/getSendCopyFlag',
    method: 'get',
    params: data,
  });
}
