import request from '../indexConfig';

export function mobileLoginFromMoa(data = {}) {
  return request({
    url: '/portal/login/moaLogin2',
    method: 'get',
    params: data,
  });
}
export function mobileLoginFromGrid(data = {}) {
  return request({
    url: '/portal/login/gridLogin',
    method: 'get',
    params: data,
  });
}

// 统一的请求函数
export default function ajaxRequest(restSerialNo, data = {}) {
  return request({
    url: `/api/rest-api/${restSerialNo}`,
    method: 'post',
    data
  });
}

// top10申述问题改善
export function getTopAppeal(data) {
  return request({
    url: 'portal/complaint-Monitor/topTenAppeal',
    method: 'post',
    data
  });
}

// top10申述问题改善下转
export function getTopAppealDown(data) {
  return request({
    url: 'portal/complaint-Monitor/topTenAppealDown',
    method: 'post',
    data
  });
}

// 用户即评参评量 下发量
export function getKpiIssueTakeNum(data) {
  return request({
    url: '/kpiIssue/getKpiIssueTakeNum',
    method: 'post',
    data
  });
}

// 查询投诉满意度统计数据-账期范围
export function queryStatDateRange(params) {
  return request({
    url: '/ComplaintSatisfactionStatController/queryStatDateRange',
    method: 'get',
    params
  });
}
// 查询投诉满意度统计数据-指标查询
export function queryComplaintSatisfactionStats(data) {
  return request({
    url: '/ComplaintSatisfactionStatController/queryComplaintSatisfactionStats',
    method: 'post',
    data
  });
}
// 查询投诉满意度统计数据-满意度区域柱状图
export function queryScoreAreaDatas(data) {
  return request({
    url: '/ComplaintSatisfactionStatController/queryScoreAreaDatas',
    method: 'post',
    data
  });
}
// 查询投诉样本量统计数据-账期范围
export function queryStatDateRange2(params) {
  return request({
    url: '/ComplaintSampleStatController/queryStatDateRange',
    method: 'get',
    params
  });
}
// 查询投诉样本量统计数据-指标查询
export function querySampleTypeDatas(data) {
  return request({
    url: '/ComplaintSampleStatController/querySampleTypeDatas',
    method: 'post',
    data
  });
}
// 查询投诉样本量统计数据-样本量占比
export function querySampleRatioDatas(data) {
  return request({
    url: '/ComplaintSampleStatController/querySampleRatioDatas',
    method: 'post',
    data
  });
}
// 查询投诉样本量统计数据-满意度柱状图
export function querySampleAreaDatas(data) {
  return request({
    url: '/ComplaintSampleStatController/querySampleAreaDatas',
    method: 'post',
    data
  });
}



// 集团考核模块
export function getServeData(data) {
  return request({
    url: `/api/rest-api/${data.restSerialNo}`,
    method: 'POST',
    data: {
    	opTime: data.opTime,
      statType:data.statType,
      targetId:data.targetId,
      statDate:data.statDate,
      parentId:data.parentId
    }
  });
}