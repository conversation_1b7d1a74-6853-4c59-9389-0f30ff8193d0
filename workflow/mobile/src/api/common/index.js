import request from '../config';

function queryIdentyRootCode(data) {
  return request({
    url: '/identyCommon/queryIdentyRootCode',
    method: 'post',
    data,
  });
}

export function getDepartmentChilds() {
  return request({
    url: '/user/getDepartmentChilds',
    method: 'post',
  });
}

export function getDepartmentUsers(data) {
  return request({
    url: '/user/getDepartmentUsers',
    method: 'post',
    data,
  });
}

export async function getDictionary() {
  // try {
  const problemType = await queryIdentyRootCode({
    attachList: '',
    contentId: '',
    typeId: 'problemType',
  });
  const dept = await getDepartmentChilds();
  const dic = await request({
    url: '/identyCommon/queryIdentyCode',
    method: 'post',
    noResponseInterceptors: true,
  });
  dic.data.problemType = problemType.data;
  dic.data.dept = dept.data;
  console.log(dic);
  return dic;
  // }
  // catch (error) {
  //   return error;
  // }
}

export function queryIdentySubtype(data) {
  return request({
    url: '/identyCommon/queryIdentySubtype',
    method: 'post',
    data,
  });
}
export function queryIdentyDetail(data) {
  return request({
    url: '/identyCommon/queryIdentyDetail',
    method: 'post',
    data,
  });
}
export function getImg(params) {
  return request({
    url: '/workflow/image',
    method: 'get',
    params,
    responseType: 'blob',
  });
}
// 根据自定义表单中的用户组查询选择用户
export function getUserByRole(data) {
  return request({
    url: '/user/getUserByRole',
    method: 'post',
    data,
  });
}
// 查询所有用户
export function getUserList(data) {
  return request({
    url: '/user/getUserList',
    method: 'post',
    data,
  });
}
// 更新节点处理人
export function updateFlowHandleUser(params, data) {
  return request({
    url: '/user/updateFlowHandleUser',
    method: 'post',
    params,
    data,
  });
}
// 流程配置页面的工单类型获取
export function queryIdentifyTypeAll() {
  return request({
    url: '/identyCommon/queryIdentifyAllTypeSub',
    method: 'post',
  });
}
// 动态组件接口调用
export function getFlowCode(data) {
  console.log(data);
  return request({
    url: '/identyCommon/queryIdentyTextContent',
    method: 'post',
    data,
  });
}

export function getByUrl(url, params, context) {
  return request({
    url,
    method: 'get',
    params: {params, context},
  });
}

export function postByUrl(url, data, context) {
  return request({
    url,
    method: 'post',
    data: {params: data, context},
  });
}
