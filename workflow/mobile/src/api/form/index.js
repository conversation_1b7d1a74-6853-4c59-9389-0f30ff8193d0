import request from '../config';
export function getFormData(data = {}) {
  return request({
    url: '/identyDisplay/queryIdentys',
    method: 'post',
    data,
  });
}
export function getFormDetail(data = {}) {
  return request({
    url: '/identyDisplay/queryIdentyDetail',
    method: 'post',
    data,
  });
}
export function getFormReply(data = {}) {
  return request({
    url: '/identyDisplay/replyCSS',
    method: 'post',
    data,
  });
}
export function getFormIdentyType(data = {}) {
  return request({
    url: '/identyDisplay/queryIdentyType',
    method: 'post',
    data,
  });
}
// 获取用户
export function getDepartmentList() {
  return request({
    url: '/user/getDepartmentList',
    method: 'post',
  });
}
// 获取用户信息
export function getUserList(data) {
  return request({
    url: '/user/getUserList',
    method: 'post',
    data,
  });
}
// 查询工单回复
export function queryIdentyReply(data) {
  return request({
    url: '/identyDisplay/queryIdentyReply',
    method: 'post',
    data,
  });
}
// 查询催办工单详情
export function queryUrge(data) {
  return request({
    url: '/identyDisplay/queryUrge',
    method: 'post',
    data,
  });
}
