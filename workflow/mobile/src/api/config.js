import axios from 'axios';
import {urlParser} from '@/utils';
import {hideLoading, showLoading} from '@/utils/loading';
import Vue from 'vue';
import {Toast} from 'vant';
Vue.use(Toast);
// 请求默认配置
axios.defaults.baseURL = process.env.VUE_APP_REQUEST_URL;
// 本地mock地址
axios.defaults.headers['Content-Type'] = 'application/json';
// axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8';
// 拦截器
axios.interceptors.request.use((req) => {
  console.log(req);
  if (req.data) {
    req.data.uuid = `${new Date().getTime()}`;
  } else if (req.params) {
    req.params.uuid = `${new Date().getTime()}`;
  }
  const token = sessionStorage.getItem('token-workflow');
  if (token) {
    req.headers['X-Auth-Token'] = token;
  }
  showLoading();
  // req.data.paramUserInfo = {
  //   userAccount: sessionStorage.userAccount || ''
  // };
  // if(req.method === 'get') {
  //   req.url += `?${serialize(req.data)}`;
  // }
  // req.url += '?debug';
  const urlQuery = urlParser(location.href);
  // 判断url是否包含debug字段，用于mock
  if (Object.keys(urlQuery).includes('debug')) {
    req.url += '&debug';
  }
  req.headers.Accept = '*/*';
  req.headers.token = sessionStorage.token;
  return req;
});
axios.interceptors.response.use(
  (res) => {
    console.log('返回值==>', res);
    if (res.status === 200) {
      // if (res.data.code === '0') {
      //   return res.data;
      // }
      hideLoading();
      return res.data;
      // Toast.info({
      //   Toast: res.data.msg,
      //   // center:true
      // });
      // return Promise.reject(res.data);
    }
    return Promise.reject(res);
  },
  (error) => {
    hideLoading();
    if (error.response.config.noResponseInterceptors) {
      return Promise.reject(error);
    }
    if (error.response.status === 403) {
      Toast.fail('无权限');
    } else if (error.response.status === 401) {
      Toast.fail('未认证');
    }
    return Promise.reject(error);
  }
);
// 配置服务
axios.services = {
  // 服务名称
  investigate: 'app',
};
export default axios;
