import axios from 'axios';
import {urlParser} from '@/utils';
import {hideLoading, showLoading} from '@/utils/loading';
import {getToken} from '@/utils/auth';
import Vue from 'vue';
import {Toast} from 'vant';
const errorCode =  {
  '401': '认证失败，无法访问系统资源',
  '403': '当前操作没有权限',
  '404': '访问资源不存在',
  'default': '系统未知错误，请反馈给管理员'
};
Vue.use(Toast);

const axiosInstance1  = axios.create({
  timeout: 15000,
  baseURL: process.env.VUE_APP_INDEX_URL
});
// 本地mock地址
axiosInstance1.defaults.headers['Content-Type'] = 'application/json';
// 拦截器
axiosInstance1.interceptors.request.use((req) => {
  console.log(req);
  // 是否需要设置 token
  const isToken = (req.headers || {}).isToken === false;
  if (getToken() && !isToken && req.url != '/portal/login/ssoLogin') {
    req.headers['Authorization'] = `Bearer ${getToken()}`; // 让每个请求携带自定义token 请根据实际情况自行修改
    req.headers['X-Requested-With'] = 'XMLHttpRequest';

    // X-Requested-With
  }

  // get请求映射params参数
  if (req.method === 'get' && req.url.indexOf('http://portalmgr.bmcc.com.cn') !== -1) {
  // if(req.url.indexOf('access_token') !== -1 ){
    req.data = true;
    req.headers['Content-Type'] = 'multipart/form-data;';
  }
  if (req.data) {
    req.data.uuid = `${new Date().getTime()}`;
  } else if (req.params) {
    req.params.uuid = `${new Date().getTime()}`;
  }
  const token = sessionStorage.getItem('token');
  if (token) {
    req.headers['X-Auth-Token'] = token;
  }
  showLoading();
  const urlQuery = urlParser(location.href);
  // 判断url是否包含debug字段，用于mock
  if (Object.keys(urlQuery).includes('debug')) {
    req.url += '&debug';
  }
  req.headers.Accept = '*/*';
  req.headers.token = sessionStorage.token;
  return req;
});
axiosInstance1.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    hideLoading();
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default'];
    if (code === 401) {
    } else if (code === 500) {
      if (msg == '客户群名称不可重复' || msg == '当前未筛选出客群' || msg == '未筛选出符合条件的客户' || msg == '数量超过100000，请选择筛选条件') {
        return Promise.reject({code: 500, msg});
      }
      if(msg == '你没有该地区的权限') {
        return Promise.reject({code: 500, msg});
      }
   
      return Promise.reject(new Error(msg));
    } else if (code == 200 || code == 0) {
      return res.data;
    } else if (code !== 200) {
  
      return Promise.reject('error');
    }
  },
  (error) => {
    
    console.log(`err${error}`);
    let {message} = error;
    if (message == 'Network Error') {
      message = '后端接口连接异常';
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时';
    } else if (message.includes(430)) {
      return Promise.resolve({code: '430'});
    } else if (message.includes('Request failed with status code')) {
      message = `系统接口${message.substr(message.length - 3)}异常`;
    }
    /* Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })*/
    return Promise.reject(error);
  }
);
// 配置服务
export default axiosInstance1;
