import request from '@/api/indexConfig';

// 获取用户信息
export function getName(data) {
  return request({
    url: '/portal/login/ssoLogin',
    method: 'post',
    data
  });
}
// 获取用户城市信息
export function getUserCityPermissInfo() {
  return request({
    url: '/system/user/getUserCityPermissInfo',
    method: 'get'
  });
}

// 本地环境登录模拟
export function loginUser(data) {
  return request({
    url: `/portal/login/testLogin?loginName=${data.username}`,
    method: 'post',
    data,
    headers: {
      isToken: false
    }
  });
}

// 获取首页菜单
export function getMenu(data) {
  return request({
    url: `/portal/sys-menu/getMenuBy4a/${data}`,
    method: 'get'
  });
}
// 获取大音门户菜单
export function getFrontMenu() {
  return request({
    url: '/getFrontRouters',
    method: 'get'
  });
}

// 单点登录集团大音获取票据接口
export function getGroupToken(data) {
  return request({
    url: '/portal/login/get-group-token',
    method: 'post',
    data
  });
}

// 获取待办列表数据
export function getIdenty(params) {
  return request({
    url: '/portal/login/getTaskByLoginUser',
    method: 'get',
    params
  });
}

export function postLog(params) {
  return request({
    url: '/portal/sys-menu/collect',
    method: 'post',
    data: params
  });
}

// 导出明细验证
export function oauthToken(params) {
  return request({
    url: '/portal/uninformed/oauth-token',
    method: 'post',
    data: params
  });
}

// 导出明细验证
export function jkStatus(params) {
  return request({
    url: '/portal/uninformed/jk-status',
    method: 'post',
    data: params
  });
}

// 2次导出明细验证
export function jkStatusTwo(params) {
  return request({
    url: '/portal/uninformed/jk-status-two',
    method: 'post',
    data: params
  });
}

// 下载接口（导出明细）
export function exportDetail(params) {
  return request({
    url: '/portal/uninformed/export-detail',
    method: 'post',
    data: params
  });
}

// 下载接口（导出明细）
export function exportDetailHigh(params) {
  return request({
    url: '/portal/uninformed/export-detail-high',
    method: 'post',
    data: params
  });
}

// 下载接口（导出明细）
export function exportDetailComplain(params) {
  return request({
    url: '/portal/uninformed/export-detail-complaint',
    method: 'post',
    data: params
  });
}

// /portal/forjump/get
export function getForjump(redirect) {
  return request({
    url: '/portal/forjump/get',
    method: 'get',
    params: {
      redirect
    }
  });
}

export function checkToken(data = {}) {
  return request({
    url: '/portal/login/check-token',
    method: 'post',
    data
  });
}

export function getFrontRouters(data = {}) {
  return request({
    url: '/getFrontRouters',
    method: 'get',
    params: data
  });
}

export function loginHeart() {
  return request({
    url: '/portal/login/heart',
    method: 'get',
  });
}