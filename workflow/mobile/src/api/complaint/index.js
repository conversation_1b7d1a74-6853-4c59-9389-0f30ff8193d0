/* eslint-disable */
import request from "../config";

// 获取期数
export function getPeriodByYear(params) {
  return request({
    url: `/layout/getPeriodByYear`,
    method: "get",
    params,
  });
}

// 根据期数查看处理情况
export function getHandlingInfo(data) {
  return request({
    url: `/layout/getHandlingInfo`,
    method: "post",
    data: data,
  });
}

// 获取工单列表
export function getHeadQuartersIssueList(
  params = {
    currentPage: 0,
    pageSize: 10,
    hasFinished: "ALL",
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
  }
) {
  return request({
    url: "/layout/process-instance-list",
    method: "post",
    params,
    data,
  });
  // return new Promise((r) => {
  //   setTimeout(() => {
  //     r({
  //         "msg": "请求成功",
  //         "status": "200",
  //         "obj": {
  //         "total": 3,
  //         "list": [
  //           {
  //           "periods": "2022年第1期",
  //           "currentTitle": "总部测试1",
  //           "unit": "宁夏",
  //           "headquarter": "客户服务部、网络事业部",
  //           "taskStatus": "已完成",
  //           "taskCondition": "大音平台",
  //           "createTime": "2022-09-08 13:12:45"
  //           },
  //           {
  //           "periods": "2022年第2期",
  //           "currentTitle": "总部测试2",
  //           "unit": "宁夏",
  //           "headquarter": "客户服务部、网络事业部",
  //           "taskStatus": "进行中",
  //           "taskCondition": "宁夏",
  //           "createTime": "2022-07-02 16:39:05"
  //           },
  //           {
  //           "periods": "2022年第3期",
  //           "currentTitle": "总部测试3",
  //           "unit": "宁夏",
  //           "headquarter": "客户服务部",
  //           "taskStatus": "进行中",
  //           "taskCondition": "大音平台",
  //           "createTime": "2022-06-13 16:00:05"
  //           }
  //         ]
  //       }
  //     })
  //   }, 2000);
  // })
}

// 获取省专问题清单
export function getProvinceIssueList(data) {
  // return request({
  // 	url: `/complaint/getProvinceIssueList`,
  // 	method: 'post',
  // 	data
  // })
  return new Promise((r) => {
    setTimeout(() => {
      r({
        msg: "请求成功",
        status: "200",
        obj: {
          total: 2,
          list: [
            {
              year: "2022",
              taskStatus: "进行中",
              taskCondition: "1/21",
              updateTime: "2022-07-13 16:00:05",
            },
            {
              year: "2021",
              taskStatus: "已完成",
              taskCondition: "2/2",
              updateTime: "2021-08-13 16:00:05",
            },
          ],
        },
      });
    }, 2000);
  });
}
