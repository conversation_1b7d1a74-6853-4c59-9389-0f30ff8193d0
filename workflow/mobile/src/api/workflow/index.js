/* eslint-disable */

import request from "../config";
export function getTaskListForOapush(
  params = {
    currentPage: 0,
    pageSize: 10,
    taskId: "",
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
    taskStatus: "NONE",
    taskVariables: {},
    variableNames: ["title", "content"],
  }
) {
  return request({
    url: "/workflow/oapush-task-list",
    method: "post",
    data,
    params,
  });
}
/**
 * 获取任务列表，只能获取未完成的
 * @param {*} params
 * @param {*} data
 * @returns
 */
export function getTaskList(
  params = {
    currentPage: 0,
    pageSize: 10,
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
    taskStatus: "NONE",
    taskVariables: {},
    variableNames: ["title", "content"],
  }
) {
  return request({
    url: "/workflow/task-list",
    method: "post",
    data,
    params,
  });
}
/**
 * 获取userTask的表单模型，用于构建动态表单
 * @param {*} params
 * @returns
 */
export function getTaskFormInfo(
  params = {
    taskId: "",
  }
) {
  return request({
    url: "/workflow/form-info",
    method: "get",
    params,
  });
}

/**
 * 获取任务列表，只能获取未完成的
 * @param {*} params
 * @param {*} data
 * @returns
 */
export function getHistoricTaskList(
  params = {
    currentPage: 0,
    pageSize: 10,
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
    taskStatus: "NONE",
    taskVariables: {},
    variableNames: ["title", "content"],
  }
) {
  return request({
    url: "/workflow/historic-task-list",
    method: "post",
    params,
    data,
  });
}

export function getHistoricProcessInstanceList(
  params = {
    currentPage: 0,
    pageSize: 10,
    hasFinished: false,
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
  }
) {
  return request({
    url: "/workflow/process-instance-list",
    method: "post",
    params,
    data,
  });
}

export function getHistoricProcessInstanceListOnlyComplete(
  params = {
    currentPage: 0,
    pageSize: 10,
  },
  data = {
    processDefinitionKey: null,
    processVariables: {},
    /**
     * 如果是管理员这个参数才生效，表示显示所有人的
     */
    showAll: true,
  }
) {
  return request({
    url: "/workflow/process-instance-list-only-complete",
    method: "post",
    params,
    data,
  });
}
/**
 *
 * @param {Object} params
 * @param {Object} data
 * @returns
 */
export function completeTask(
  params = {
    taskId: "",
  },
  data = {}
) {
  return request({
    url: "/workflow/complete",
    method: "post",
    params,
    data,
  });
}
export function assigneeTask(
  params = {
    taskId: "",
  }
) {
  return request({
    url: "/workflow/task-assignee",
    method: "post",
    params,
  });
}
export function getBackList(
  params = {
    taskId: "",
    processInstanceId: "",
  }
) {
  return request({
    url: "/workflow/back-list",
    method: "get",
    params,
  });
}
export function backToStepTask(
  params = { taskId: "", message: "", processInstanceId: "", nodeId: "" }
) {
  return request({
    url: "/workflow/back_to_step_task",
    method: "post",
    params,
  });
}
export function reject(
  params = {
    taskId: "",
  },
  data = {}
) {
  return request({
    url: "/workflow/reject",
    method: "post",
    params,
    data,
  });
}
export function getTaskDetail(
  params = {
    taskId: "",
  }
) {
  return request({
    url: "/workflow/task-detail",
    method: "get",
    params,
  });
}
// 获取动态表单(新增工单)
export function getAddFormInfo(
  params = {
    processKey: "",
  }
) {
  return request({
    url: "/workflow/process-start-form-info",
    method: "get",
    params,
  });
}

// 工单发起流程
export function processNewByVO(
  params = {
    processKey: "",
  },
  data = {}
) {
  return request({
    url: "/workflow/processNewByVO",
    method: "post",
    params,
    data,
  });
}
// 获取工单详情
export function getDetailForm(
  params = {
    processDefinitionProcessId: "",
  }
) {
  return request({
    url: "/workflow/process-identify-detail-form",
    method: "get",
    params,
  });
}
// 获取流程的xml
export function getflowXML(
  params = {
    processKey: "",
  }
) {
  return request({
    url: "/workflow/process-get-flow-xml",
    method: "get",
    params,
  });
}
// 获取流程的动态表单
export function getflowForm(
  params = {
    formKey: "",
  }
) {
  return request({
    url: "/workflow/process-get-site-form",
    method: "get",
    params,
  });
}
// 上传流程图xml
export function pushflowXML(data) {
  return request({
    url: "/workflow/process-load-flow-xml",
    method: "post",
    data,
  });
}
// 获取流程所有附件
export function getProcessAllFile(processInstanceId) {
  return request({
    url: `/workflow/all-attachments?processInstanceId=${processInstanceId}`,
    method: "get",
  });
}

//      工作流展示页面优化ui相关接口

// 1 查询各种工单类型的条数
export function workflowListCount() {
  return request({
    url: "/layout/task-list-count",
    method: "post",
    data: {
      showAll: false,
      processVariables: {
        title: null,
        identySubtype: null,
        identyType: "",
      },
      uuid: "1647395003495",
    },
  });
}

// 2 任务接口统一处理
export function TaskList(params, data) {
  let urls = `/layout/task-list-${data.name}`;
  // 全部任务的查询
  if (data.name === "count") {
    urls = "/layout/process-instance-list";
    params["hasFinished"] = "ALL";
  }
  return request({
    url: urls,
    method: "post",
    params,
    data,
  });
}
// 扩建表单 活的流程信息(扩建的表单列表没有携带流程信息)
export function getProcessId(params) {
  return request({
    url: "/layout/get-task-id",
    method: "post",
    params,
  });
}
// 优化表单 撤回新建的工单
export function removeTask(params) {
  return request({
    url: "/layout/remove-task-id",
    method: "post",
    params,
    transformRequest: [
      function (data, headers) {
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        return data;
      },
    ],
  });
}
// 导出工单表格
export function exportTaskList(data, params) {
  return request({
    url: "/layout/task-list-export",
    method: "post",
    data: Object.assign(
      {},
      {
        showAll: false,
        processVariables: {
          startTime: "",
          endTime: "",
          identifier: null,
          title: null,
          identySubtype: null,
        },
        uuid: "1661417231200",
      },
      data
    ),
    params,
  });
}

// 集团新增接口
export function groupAdd(data) {
  return request({
    url: "/Sound/Identy/add/group",
    method: "post",
    data: data,
  });
}
