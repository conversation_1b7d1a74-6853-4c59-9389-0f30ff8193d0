import request from '../indexConfig';
function reformatDates(dates) {
  if(dates.length == 0) return;
  const reformattedDates = [];
  dates.forEach((date) => {
    const [year, month, day] = date.split('-');
    let week = Math.floor((new Date(year, month - 1, day).getDate() / 7));
    if(week == 0) {
      week = 1;
    }
    reformattedDates.push(`${year}年${month}月第${week}周`);
  });
  return reformattedDates;
}
// 获取指标数据最新有数据时间
export function getNewTime(data) {
  return request({
    url: '/portal/evaluateAfterUse/getTargetNewestDate',
    method: 'get',
    params: data
  });
}
// 获取指标数据
export function getTargetIndexData(data) {
  if(data.statType == '2') {
    data.statDate = reformatDates(data.statDate);
  }
  data.targetIds = data.targetIds.filter((item) => item);
  return request({
    url: '/portal/evaluateAfterUse/getTargetData',
    method: 'post',
    data
  });
}

// 查询满意度趋势
export function getSatisfactionTrend(data) {
  if(data.statType == '2') {
    data.statDate = reformatDates(data.statDate);
  }
  return request({
    url: '/portal/evaluateAfterUse/getSatisfactionTrend',
    method: 'post',
    data
  });
}

// 查询满意度区域
export function getSatisfactionArea(data) {
  if(data.statType == '2') {
    data.statDate = reformatDates(data.statDate);
  }
  return request({
    url: '/portal/evaluateAfterUse/getSatisfactionArea',
    method: 'post',
    data
  });
}

// 查询区县排名
export function getTargetCountyRank(data) {
  if(data.statType == '2') {
    data.statDate = reformatDates(data.statDate);
  }
  return request({
    url: '/portal/evaluateAfterUse/getTargetCountyRank',
    method: 'post',
    data
  });
}