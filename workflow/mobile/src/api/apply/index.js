import request from '../config';

export function getTaskList(data) {
  return request({
    url: '/apply/getTaskList',
    method: 'post',
    data,
  });
}

export function getTaskListAll(data) {
  return request({
    url: '/apply/getTaskListAll',
    method: 'post',
    data,
  });
}

export function createWork(data) {
  return request({
    url: '/apply/createWork',
    method: 'post',
    data,
  });
}

export function acceptance(data) {
  return request({
    url: '/apply/acceptance',
    method: 'post',
    data,
  });
}
export function findGroupWork(data) {
  return request({
    url: '/apply/findGroupWork',
    method: 'post',
    data,
  });
}
export function findOperationNodeLog(data) {
  return request({
    url: '/apply/findOperationNodeLog',
    method: 'post',
    data,
  });
}
