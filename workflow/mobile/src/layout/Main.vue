<template>
  <div class="main-layout">
    <transition>
      <router-view></router-view>
    </transition>
    <!-- <div class="checkTab">
      <van-tabbar @change="changeRoute" v-model="active">
        <van-tabbar-item name="home" icon="home-o"
          >首页
          <template #icon="props">
            <img :src="props.active ? icon.homeActive : icon.homeDefault" />
          </template>
        </van-tabbar-item>
        <van-tabbar-item name="house" icon="desktop-o"
          >数据
          <template #icon="props">
            <img :src="props.active ? icon.dataActive : icon.dataDefault" />
          </template>
        </van-tabbar-item>
      </van-tabbar>
    </div> -->
  </div>
</template>
<script>
import Vue from 'vue';
import {Tabbar, TabbarItem} from 'vant';
Vue.use(Tabbar);
Vue.use(TabbarItem);
export default {
  name: 'MainLayout',
  data() {
    return {
      transitionName: 'slide-left',
      active: 'home',
      // icon: {
      //   homeDefault,
      //   homeActive,
      //   dataDefault,
      //   dataActive,
      // },
    };
  },
  methods: {
    // changeRoute(item) {
    //   console.log(item);
    //   this.active = item;
    //   this.$router.replace({path: `/${item}`});
    // },
  },
  watch: {
    // '$route.name': {
    //   handler() {
    //     this.active = this.$route.path.slice(1);
    //   },
    //   immediate: true,
    // },
  },
};
</script>
<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  background-color: #ffffff;
  overflow-y: scroll;
}
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: all 500ms;
  position: absolute;
}
.slide-right-enter {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}
.slide-right-leave-active {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-enter {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-leave-active {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}
.van-tabbar-item--active {
  color: #646566;
}
</style>
