// 封装过滤器
import {getDictionary} from '@/api/common/index';
import {dateFormat} from '@/utils';

let dic =
  localStorage.getItem('dic') && JSON.parse(localStorage.getItem('dic'));
export const allCode = (key) => dic[key];
const applyHandlerDept = (value) => {
  if (dic['applyHandlerDept']) {
    return dic['applyHandlerDept'].filter((ele) => ele.value === value)[0]
      ?.label;
  }
  return value;
};
const identyType = (value) => {
  if (dic['identyType']) {
    return dic['identyType'].filter((ele) => ele.value === value)[0].label;
  }
  return value;
};
const identySubtype = (value) => {
  if (dic['identySubtype']) {
    return dic['identySubtype'].filter((ele) => ele.value === value)[0].label;
  }
  return value;
};
const identyDetail = (value) => {
  if (dic['identyDetail']) {
    return dic['identyDetail'].filter((ele) => ele.value === value)[0].label;
  }
  return value;
};
const extEarlyWarningLevel = (value) => {
  if (dic['extEarlyWarningLevel']) {
    return dic['extEarlyWarningLevel'].filter((ele) => ele.value === value)[0]
      .label;
  }
  return value;
};
const assignee = (value) => {
  if (dic['user'] && value) {
    return dic['user'].filter((ele) => ele.value === value)[0].label;
  }
  return value;
};
const unitCode = (value) => {
  if (dic['unitCode']) {
    return dic['unitCode'].filter((ele) => ele.value === value)[0].label;
  }
  return value;
};
let isInit = false;

export function init() {
  return new Promise((r, j) => {
    if (isInit) {
      r();
      return;
    }
    getDictionary()
      .then((v) => {
        const {data} = v;
        // 预警类型的存储
        if (!sessionStorage.getItem('extEarlyWarningLevel')) {
          sessionStorage.setItem(
            'extEarlyWarningLevel',
            JSON.stringify(data.extEarlyWarningLevel)
          );
        }
        // 存储工单类型
        const identyType = [];
        console.log(data);
        data.identyType.forEach((item) => {
          item.children = [];
          data.identySubtype.forEach((item1) => {
            if (item1.value.substring(0, 2) === item.value) {
              item.children.push(item1);
            } else if (item1.value.startsWith('756') && item.value === '03') {
              item.children.push(item1);
            }
          });
          identyType.push(item);
        });
        console.log(identyType);
        if (!sessionStorage.getItem('identyType')) {
          // console.log('打印工单类型',identyType);
          sessionStorage.setItem('identyType', JSON.stringify(identyType));
        }
        Object.getOwnPropertyNames(data).forEach((vv) => {
          if (vv === 'problemType' || 'user') {
            return;
          }
          const arr = data[vv];
          const map = {};
          arr.forEach((vvv) => {
            map[vvv.value] = vvv.label;
          });
          data[vv] = map;
        });
        dic = data;
        localStorage.setItem('dic', JSON.stringify(data));
        console.log('打印码表', dic);
        r();
        isInit = true;
      })
      .catch((e) => {
        j(e);
      });
  });
}

// init();

function DataFormat(value) {
  if (value) {
    return `${value.substring(0, 4)}/${value.substring(4, 6)}/${value.substring(
      6,
      8
    )} ${value.substring(8, 10)}:${value.substring(10, 12)}:${value.substring(
      12,
      14
    )}`;
  }
  return value;
}

export default {
  dic,
  identyDetail,
  extEarlyWarningLevel,
  dateFormat,
  assignee,
  applyHandlerDept,
  identyType,
  identySubtype,
  DataFormat,
  unitCode,
};
