// 序列化请求参数到url
export const serialize = (obj) => {
  const str = [];
  Object.keys(obj).forEach((key) => {
    let value = obj[key];
    if (typeof value === 'object') {
      value = encodeURIComponent(JSON.stringify(obj[key]));
    } else {
      value = encodeURIComponent(value);
    }
    str.push(`${encodeURIComponent(key)}=${value}`);
  });
  return str.join('&');
};
// 反序列化URL请求参数转对象
export const urlParser = (url = location.href) => {
  const obj = {};
  try {
    const arr = url.split('?')[1].split('&'); // 先通过？分解得到？后面的所需字符串，再将其通过&分解开存放在数组里
    for (const i of arr) {
      // eslint-disable-next-line prefer-destructuring
      obj[i.split('=')[0]] = i.split('=')[1]; // 对数组每项用=分解开，=前为对象属性名，=后为属性值
    }
  } catch (error) {}
  return obj;
};
// 数组分组
export const groupBy = (array, f) => {
  const groups = {};
  array.forEach((o) => {
    const group = JSON.stringify(f(o));
    groups[group] = groups[group] || [];
    groups[group].push(o);
  });
  return Object.keys(groups).map((group) => groups[group]);
};

// 深度拷贝
export const deepClone = (obj) => {
  const objClone = Array.isArray(obj) ? [] : {};
  if (obj && typeof obj === 'object') {
    /* eslint-disable */
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 判断ojb子元素是否为对象，如果是，递归复制
        if (obj[key] && typeof obj[key] === "object") {
          objClone[key] = deepClone(obj[key]);
        } else {
          // 如果不是，简单复制
          objClone[key] = obj[key];
        }
      }
    }
  }
  return objClone;
};
// 格式化日期
export const dateFormat = (date, fmt) => {
  if (!date) return "-";
  let ret;
  const opt = {
    "y+": date.getFullYear().toString(), // 年
    "M+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "m+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
};
/**
 * 图片压缩
 * @param file
 * @returns {Promise<unknown>}
 */
export const compressImage = (file) => {
  // 大于1MB的jpeg和png图片都缩小像素上传1000000
  if (
    /\/(?:jpeg|png|jpg|bmp)/i.test(file.file.type) &&
    file.file.size > 1000000
  ) {
    return new Promise((resolve) => {
      console.log(`压缩前===>${file.file.size}`);
      // 创建Canvas对象(画布)
      const canvas = document.createElement("canvas");
      // 获取对应的CanvasRenderingContext2D对象(画笔)
      const context = canvas.getContext("2d");
      // 创建新的图片对象
      const img = new Image();
      // 指定图片的DataURL(图片的base64编码数据)
      img.src = this.createObjectURL(file.file);
      // 画布宽度
      const width = 512;
      // 监听浏览器加载图片完成，然后进行绘制
      img.onload = () => {
        // 画布大小按照图片的比例生成
        const height = width / (img.naturalWidth / img.naturalHeight);
        // 指定canvas画布大小，该大小为最后生成图片的大小
        canvas.width = width;
        canvas.height = height;
        /* drawImage画布绘制的方法。(0,0)表示以Canvas画布左上角为起点， canvas.width, canvas.height 是将图片按给定的像素进行缩小。*/
        /* 如果不指定缩小的像素，图片将以图片原始大小进行绘制，图片像素如果大于画布将会从左上角开始按画布大小部分绘制图片，最后得到的图片就是张局部图。图片小于画布就会有黑边。*/
        context.drawImage(img, 0, 0, canvas.width, canvas.height);
        // 将绘制完成的图片重新转化为base64编码，file.file.type为图片类型，0.92为默认压缩质量
        file.content = canvas.toDataURL(file.file.type, 0.92);
        // 将base64编码的图片转成文件(file)格式
        const lastfile = this.dataURLtoFile(file.content);
        console.log(`压缩后===>${lastfile.size}`);
        // 把file转换成二进制形式(binart)并进行上传
        // func(lastfile);
        resolve(lastfile);
      };
    });
  }
  return new Promise((resolve) => resolve(file.file));
};

// 日期格式化
export function parseTime(time, pattern, islastMonth) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time.replace(new RegExp(/-/gm), "/");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: islastMonth ? date.getMonth() : date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

export function downloadBlob(blob, fileName) {
  const url = window.URL.createObjectURL(new Blob([blob]));
  console.log(url);

  if (window.navigator.msSaveBlob) {
    try {
      window.navigator.msSaveBlob(blob, fileName);
    } catch (e) {
      console.log(e);
    }
  } else {
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", fileName);
    link.click();
  }
}

export default {
  // 序列化请求参数到url
  serialize: (obj) => {
    const str = []
    Object.keys(obj).forEach((key) => {
      let value = obj[key]
      if (typeof (value) === 'object') {
        value = encodeURIComponent(JSON.stringify(obj[key]))
      } else {
        value = encodeURIComponent(value)
      }
      str.push(`${encodeURIComponent(key)}=${value}`)
    })
    return str.join('&')
  },
  IEVersion: () => {
    const { userAgent } = navigator // 取得浏览器的userAgent字符串
    const isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 // 判断是否IE<11浏览器
    const isEdge = userAgent.indexOf('Edge') > -1 && !isIE // 判断是否IE的Edge浏览器
    const isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
    if (isIE) {
      const reIE = new RegExp('MSIE (\\d+\\.\\d+);')
      reIE.test(userAgent)
      const fIEVersion = parseFloat(RegExp.$1)
      if (fIEVersion === 7) {
        return 7
      } if (fIEVersion === 8) {
        return 8
      } if (fIEVersion === 9) {
        return 9
      } if (fIEVersion === 10) {
        return 10
      }
      return 6 // IE版本<=7
    } if (isEdge) {
      return 'edge' // edge
    } if (isIE11) {
      return 11 // IE11
    }
    return -1 // 不是ie浏览器
  },
  formatterDate(val, type) {
    val = new Date(val)
    const year = val.getFullYear()
    const month = (val.getMonth() + 1) < 10 ? `0${val.getMonth() + 1}` : val.getMonth() + 1
    const monthSpe = (val.getMonth() + 1) < 10 ? `${val.getMonth() + 1}` : val.getMonth() + 1
    const date = val.getDate() < 10 ? `0${val.getDate()}` : val.getDate()

    if (type === 'y-m-d') {
      return `${year}-${month}-${date} 23:59:59`
    } else if (type == 'yyyy-MM-dd') {
      return `${year}-${month}-${date}`
    } else if (type == 'yyyyMM') {
      return `${year}${month}`
    } else if (type == 'yyyy年MM月') {
      return `${year}年${month}月`
    } else if (type == 'yyyyMMS') {
      return `${year}${monthSpe}`
    }else if (type == 'yyyy-MM') {
      return `${year}-${month}`
    } else {
      return `${year}${month}${date}`
    }
  },
  // 日期格式化 yyyy年MM月
  formatDate: (date, type, slitType) => {
    let slit = slitType
    slit === undefined ? slit = '' : slit = '-'
    const myyear = date.getFullYear()
    let mymonth = date.getMonth() + 1
    let myweekday = date.getDate()

    if (mymonth < 10) {
      mymonth = `0${mymonth}`
    }
    if (myweekday < 10) {
      myweekday = `0${myweekday}`
    }
    if (type === 'day') {
      return (myyear + slit + mymonth + slit + myweekday)
    }
    return (myyear + slit + mymonth)
  },
  mergeObj: () => {
    const args = []
    const res = {}
    for (var i = 0; i < arguments.length; i++) {
      args.push(arguments[i])
    }
    args.reduce((last, item) => {
      Object.entries(item).forEach(arr => {
        const key = arr[0]
        const value = arr[1]
        if (Object.prototype.hasOwnProperty.call(last, key)) {
          if (value.constructor && value.constructor === Object) {
            last[key] = mergeObj(last[key], value)
          } else {
            last[key] = deepClone(value)
          }
        } else {
          last[key] = deepClone(value)
        }
      })
      return last
    }, res)
    return res
  },
  // 手机网络专题处理保留2位小数的逻辑
  dealFloatNum: function(v) {
    if (v === 0 || v === '0') { // 等于0的情况
      return v
    } else if (!v) { // 没有值
      return '-'
    } else if (isInt(v)) { // 有值的情况 并且是整数
      return v
    } else { // 有值 并且是小数
      const len = String(v).length
      const y = String(v).indexOf('.') + 1// 获取小数点的位置
      const count = String(v).length - y// 获取小数点后的个数

      if (count > 2) { // 2位小数
        if (v < 0.005) {
          return 0.01
        }
        return Number(v).toFixed(2)
      }
      if (count == 2) {
        //  判断是不是.00 .x0
        if (String(v).indexOf('.00') != -1) {
          return Number(v).toFixed(0)
        }
        if (String(v).slice(len - 1) == 0 || String(v).slice(len - 1) == '0') { // 判断最后一位是否是0
          return Number(v).toFixed(1)
        }
        return Number(v).toFixed(2)
      }
      if (count == 1) {
        if (String(v).indexOf(y + 1) == 0) { // .0 情况处理
          return Number(v).toFixed(0)
        }
        return Number(v).toFixed(1)
      }
    }
  },
  // 处理同比环比的政府取值
  handlerMomrateAndYoyrate: function(arr) {
    if (Array.isArray(arr)) {
      arr.forEach(x => {
        if (x.momrate && x.momrate != '-') {
          if (x.momrateType === '1' || x.momratetype === '1' || x.momratetype === 1 ||x.momrateType===1|| x.momrate_type == '1') {
            if (x.momrate === 0 || x.momrate === '0') {
              x.momrate = x.momrate
            } else {
              x.momrate = `-${x.momrate}`
            }
          }
        }
        if (x.yoyrate && x.yoyrate != '-') {
          if (x.yoyrateType === '1' || x.yoyratetype === '1' || x.yoyrate_type == '1') {
            if (x.yoyrate === 0 || x.yoyrate === '0') {
              x.yoyrate = x.yoyrate
            } else {
              x.yoyrate = `-${x.yoyrate}`
            }
          }
        }
        if (x.eoms && x.eoms != '-') {
          if (x.eomstype === '1' || x.eomsType === '1' || x.eoms_type === '1') {
            if (x.eoms === 0 || x.eoms === '0') {
              x.eoms = x.eoms
            } else {
              x.eoms = `${x.eoms}`
            }
          }
        }
        if (x.yearbasis && x.yearbasis != '-') {
          if (x.yearbasisType === '1' || x.yearbasistype || x.yearbasis_type == '1') {
            if (x.yearbasis === 0 || x.yearbasis === '0') {
              x.yearbasis = x.yearbasis
            } else {
              x.yearbasis = `-${x.yearbasis}`
            }
          }
        }
      })
    }
    return arr
  },
  /**
   * 处理百分比的取值
   * @param {*} arr  处理数组
   * @param {*} momrate 需要处理的百分比字段
   * @param {*} momratetype 用于判断升降的字段
   * @returns
   */
  handlerMomrate: function(arr, momrate, momratetype) {
    if (Array.isArray(arr)) {
      arr.forEach(x => {
        if (x[momrate] && x[momrate] != '-') {
          if (x[momratetype] === '1') {
            if (x[momrate] === 0 || x[momrate] === '0') {
              x[momrate] = x[momrate]
            } else {
              x[momrate] = `-${x[momrate]}`
            }
          }
        }
      })
    }
    return arr
  },
  // 全屏相关的js函数
  fullScreen(element) {
    var requestMethod = element.requestFullScreen || element.webkitRequestFullScreen || element.mozRequestFullScreen || element.msRequestFullScreen
    if (requestMethod) {
      requestMethod.call(element)
    } else if (typeof window.ActiveXObject !== 'undefined') { // for Internet Explorer
      var wscript = new ActiveXObject('WScript.Shell')
      if (wscript !== null) {
        wscript.SendKeys('{F11}')
      }
    }
  },
  // 退出全屏
  exitFullScreen() {
    var exitMethod = document.cancelFullScreen || document.webkitCancelFullScreen || document.mozCancelFullScreen || document.exitFullScreen
    if (exitMethod) {
      exitMethod.call(document)
    } else if (typeof window.ActiveXObject !== 'undefined') {
      var wscript = new ActiveXObject('WScript.Shell')
      if (wscript != null) {
        wscript.SendKeys('{F11}')
      }
    }
  },
  // 判断当前是否全屏。
  isFullScreen(element) {
    return (
      element.fullscreen ||
        element.mozFullScreen ||
        element.webkitIsFullScreen ||
        element.webkitFullScreen ||
        element.msFullScreen
    )
  },
  // 文件下载
  downloadBlob: (blob, fileName) => {
    const url = window.URL.createObjectURL(new Blob([blob]))

    if (window.navigator.msSaveBlob) {
      try {
        window.navigator.msSaveBlob(blob, fileName)
      } catch (e) {
        console.log(e)
      }
    } else {
      const link = document.createElement('a')
      link.href = url
      link.setAttribute(
        'download',
        fileName
      )
      link.click()
    }
  }
}

function isInt(n) {
  if (n && typeof n === 'string') {
    n = Number(n)
    return Number(n) === n && n % 1 === 0
  }
  return Number(n) === n && n % 1 === 0
}


