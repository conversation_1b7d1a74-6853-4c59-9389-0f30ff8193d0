/**
 * 获取用户权限信息
 */
import {setToken} from '@/utils/auth';
import {loginUser} from '@/api/charts.js';
import {getUserCityPermissInfo} from '@/api/charts';
import {mobileLoginFromMoa,mobileLoginFromGrid} from '@/api/satisfaction/index';

/**
 * 获取用户的token信息
 */
async function getUserToken(that,loginName) {

  if (process.env.NODE_ENV == 'development') { // 开发环境
    /**
    * guo_xin   916   640000
    * wangzejin   917   640100
    * wangzhening   918   640104
    * wupeng 无权限数据
    */

    //     admin,1,超级管理员
    // huwen,2,郝挺
    // wangfei,3,测试

    //     matingting
    // pq_kouxiaoju
    // pq_yangping

    const {code, msg} = await loginUser({
      username: 'admin',
      password: 'admin123'
    });

    if (code == 200) {
      console.log('msg=>', msg);
      sessionStorage.setItem('Authorization', msg);
      setToken(msg);
    }
  }
  console.log('that.$route.query=>', that.$route);

  if(that.$route.path.indexOf('/login_grid') > -1) {
    const {code, msg} = await mobileLoginFromGrid({
      loginName: loginName || '',
    });
    if (code == 200) {
      console.log('msg=>', msg);
      sessionStorage.setItem('Authorization', msg);
      setToken(msg);
    }
  }else{
    const {code, msg} = await mobileLoginFromMoa({
      loginName: loginName || '',
    });
    if (code == 200) {
      console.log('msg=>', msg);
      sessionStorage.setItem('Authorization', msg);
      setToken(msg);
    }
  }

}

/**
 * 获取用户地图权限信息
 */
async function getCityPermission() {
  const mapCodeName = {'640000': '宁夏', '640100': '银川市', '640200': '石嘴山市', '640300': '吴忠市', '640400': '固原市', '640500': '中卫市'};
  const {code, data} = await getUserCityPermissInfo();
  const mapPermission = {};
  if (code == 200) {
    const props = {
      cityId: 'cityId1', cityid: 'cityId1', name: 'cityName1', mapLevel: 'level1', parentCityId: 'parentId1',
      cityId2: 'cityId2', name2: 'cityName2', mapLevel2: 'level2', parentCityId2: 'parentId2'
    };

    for (const [key, field] of Object.entries(props)) {
      mapPermission[key] = key.includes('mapLevel') ? Number(data[field]) : data[field];
    }
    mapPermission.parentCityName = mapCodeName[mapPermission.parentCityId];
  }
  sessionStorage.setItem('nx-map-permission', JSON.stringify(mapPermission));
}

export default async function getUserPermission(that,loginName) {
  await getUserToken(that,loginName);
  await getCityPermission();
  // await uploadInfo();
}
