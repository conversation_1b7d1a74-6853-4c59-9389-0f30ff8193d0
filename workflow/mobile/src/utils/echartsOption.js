/**
 * 服务指标的echarts图表配置
 * 大类指标id_二级指标id_(趋势/地市)Id：{
 *  option: 图表组件配置
 *  keyMap: 图表组件字段配置
 *  echartComponent: 配置特定的echarts组件，默认是ServiceBarLine组件
 *  mapMaxLevelSelect: true 地图最大级别时，是否关联选中城市
 * }
 */
export const KPI_ECHARTS = {
  // 工信部满意度
  'chart_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name, data: {value1, value2, value3}}] = params;
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}工信部综合满意度: <br/> ${item.value || '-'}
          环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: '满意度',
      legend: ['移动', '电信', '联通'],
      control: ['line', 'line', 'line'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'chart_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
             环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: '表现值',
      legend: ['工信部综合表现值'],
      control: ['bar'],
      series: ['value1']
    },
    mapMaxLevelSelect: true
  },
  'chart_3_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name, data: {value1, value2, value3, childSplitActiveName}}] = params;
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}${childSplitActiveName}: <br/>  ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: '满意度',
      legend: ['移动', '电信', '联通'],
      control: ['line', 'line', 'line'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'chart_3_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: '表现值',
      legend: ['工信部综合表现值'],
      control: ['bar'],
      series: ['value1']
    },
    mapMaxLevelSelect: true
  },
  // 工信部申诉
  'KPI02_302_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name, data: {value1, value2, value3}}] = params;
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`];
          const tips = ['移动工信部百万申告量', '电信工信部百万申告量', '联通工信部百万申告量', '移动工信部百万申告率', '电信工信部百万申告率', '联通工信部百万申告率'];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}:${item.value || '-'}<br/> 
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_302_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}<br/> 
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部百万申告量', '工信部百万申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_305_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name, data: {value1, value2, value3}}] = params;
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`];
          const tips = ['移动工信部携号转网申告量', '电信工信部携号转网申告量', '联通工信部携号转网申告量', '移动工信部携号转网申告率', '电信工信部携号转网申告率', '联通工信部携号转网申告率'];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}: ${item.value || '-'}<br/> 
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_305_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部携号转网申告量', '工信部携号转网申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_307_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name, data: {value1, value2, value3}}] = params;
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`];
          const tips = ['移动工信部营销宣传申告量', '电信工信部营销宣传申告量', '联通工信部营销宣传申告量', '移动工信部营销宣传申告率', '电信工信部营销宣传申告率', '联通工信部营销宣传申告率'];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}: ${item.value || '-'}<br/> 
          环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_307_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部营销宣传申告量', '工信部营销宣传申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_303_1': {
    // echartComponent 配置特定的echarts组件，默认是ServiceBarLine组件
    echartComponent: 'ServiceTopSwitchChart'
  },
  'KPI02_303_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            );
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['申告量', '申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  // 自测满意度
  'KPI03_KPI0301_1': { // 手机满意度-趋势
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['手机满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_KPI0301_2': {// 手机满意度-地市
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['手机满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  'KPI03_KPI0302_1': { // 家宽满意度-趋势
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['家宽满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_KPI0302_2': { // 家宽满意度-地市
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['家宽满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  'KPI03_103_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['政企满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_103_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['政企满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  // 质量提升
  'KPI04_20201': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${item.value || '-'}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '客户数(万户)',
          nameLocation: 'end',
          nameTextStyle: {
            padding: [0, 0, 0, 5]
          }
        },
        {
          name: '比值',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1, 0],
      legend: ['三高客户数', '三高占比', '改善值'],
      control: ['bar', 'line', 'bar'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'KPI04_20202_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20202_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20102_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '驻留比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['驻留比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20102_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '驻留比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['驻留比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  'KPI04_20105_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '解决率',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['解决率', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20105_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '解决率',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['解决率', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20106_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker}  ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '时长(分钟)',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['时长', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20106_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker}  ${formatterTextUnit(item)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '时长(分钟)',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['时长', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },

  // 影响客户工单占比
  'KPI04_20302_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '占比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['占比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20302_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`);
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '占比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['占比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  // 趋势图 / 满意度趋势   --- 用后即评模块
  'KPI04-ART03': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`);
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
            }
            
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityName',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',
          
          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['score', 'momrate']
    }
  },
  'KPI04-ART01': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`);
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
            }
            
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'statDate',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',
          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['score', 'momrate']
    }
  },
  'KPI04-ART02': {
    option: {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params) => {
          const [{name}] = params;
          const str = [name];
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`);
              str.push(`<span style="display:inline-block;
              margin-right:4px;border-radius:10px;width:10px;
              height:10px;background-color:#4afede;"></span> 环比:  
              ${item.data.momrate ? (`${item.data.momrate}%`)  : '-'}`);
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`);
            }
            
          });
          return str.join('<br/>');
        }
      }
    },
    keyMap: {
      xAxis: 'cityName',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',
          visible: false ,
          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度'],
      control: ['bar'],
      series: ['score','momrate']
    }
  }
};

/**
 * 计算领先值
 * @param {移动} first
 * @param {电信} second
 * @param {联通} third
 * @returns
 */
function calLeadingValue(first, second, third) {
  if (!second && !third) return null;
  let secleading, thileading;
  if (second) {
    secleading = (Number(first) - Number(second)).toFixed(2);
  }
  if (third) {
    thileading = (Number(first) - Number(third)).toFixed(2);
  }
  if (secleading == undefined) return thileading;
  if (thileading == undefined) return secleading;
  return Math.min(secleading, thileading);
}

/**
 * 空值数组
 */
const emptyValueArray = [null, undefined, '', '-'];
/**
 * 单位配置
 */
const percentUnitTextObj = {'环比': '%', '同比': '%'};
/**
 * tooltip加单位判断
 * @param {*} value
 * @param {*} unit
 * @returns
 */
function formatterValueUnit(value, unit = '%') {
  return emptyValueArray.includes(value) ? '-' : `${value}${unit}`;
}
/**
 * tooltip文字百分比的判断
 * @param {tooltip数据} item
 */
export function formatterTextUnit(item) {
  const {seriesName, value} = item;
  return `${seriesName}: ${formatterValueUnit(value, percentUnitTextObj[seriesName] || '')}`;
}

// 根据年度第几周算出日期
export const getStartAndEndDate = (year, week) => {
  const startDate = new Date(year, 0, 1 + (week - 1) * 7);
  const endDate = new Date(year, 0, 1 + week * 7);
  const dayOfWeek = startDate.getDay();

  // 如果一年的第一天不是周一，需要调整计算
  if (dayOfWeek !== 1) {
    startDate.setDate(startDate.getDate() - dayOfWeek + 1);
  }

  // 如果一年的最后一天不是周日，需要调整计算
  if (endDate.getDay() !== 0) {
    endDate.setDate(endDate.getDate() + (7 - endDate.getDay()));
  }

  return {
    startDate: startDate.toISOString().slice(0, 10),
    endDate: endDate.toISOString().slice(0, 10)
  };
};

// yyyyMMdd格式转换为yyyy年第ww周
export const formatDateWeek = (inputDate, format = 'yyyy年第ww周') => {
  const year = inputDate.getFullYear();
  const month = inputDate.getMonth() + 1; // 月份从0开始，所以要加1
  const day = inputDate.getDate();

  // 计算周数
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((inputDate - startOfYear) / 86400000); // 一天的毫秒数
  const weekNumber = Math.floor(days / 7) + 1;

  // 格式化月份和日期，确保是两位数
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const map = new Map([
    ['yyyy年第ww周', `${year}年第${weekNumber}周`],
    ['yyyy-MM-dd', `${year}-${formattedMonth}-${formattedDay}`],
    ['yyyyMMdd', `${year}${formattedMonth}${formattedDay}`],
    ['yyyy-MM', `${year}-${formattedMonth}`],
    ['yyyyMM', `${year}${formattedMonth}`],
  ]);
  return map.get(format) || '';
};