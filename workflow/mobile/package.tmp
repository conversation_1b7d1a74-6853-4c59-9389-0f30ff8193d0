{"name": "{{projectName}}", "version": "0.1.0", "description": "{{description}}", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"core-js": "^3.6.4", "vant": "^2.12.5", "vue": "^2.6.11", "vue-router": "^3.1.6", "vuex": "^3.1.3", "axios": "^0.19.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-plugin-unit-mocha": "^4.3.0", "@vue/cli-service": "^4.3.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "1.0.0-beta.31", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "chai": "^4.1.2", "chokidar": "^3.3.1", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "postcss-px-to-viewport": "^1.1.1", "sass-loader": "^8.0.2", "terser-webpack-plugin": "^2.3.5", "vue-template-compiler": "^2.6.11", "husky": "^4.0.0", "lint-staged": "^10.5.4", "prettier": "^2.2.1", "@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1"}, "lint-staged": {"src/**/*.{vue,js,json,css,md,ts,tsx}": ["prettier --write", "eslint --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged --allow-empty", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}