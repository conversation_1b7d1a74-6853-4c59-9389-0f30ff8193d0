# 通用版督办单轨迹上报功能实现说明

## 功能概述
为通用版督办单流程实现了每次处理后自动将当前轨迹上报集团的功能。

## 实现方案

### 1. 创建轨迹上报监听器

#### TrackReportEventListener（用户任务监听器）
- **位置**: `src/main/java/com/asiainfo/sound/service/flow/TrackReportEventListener.java`
- **功能**: 监听用户任务的完成事件，在任务完成后自动上报轨迹
- **触发时机**: 用户任务完成时（complete事件）

#### ServiceTaskTrackReportListener（服务任务监听器）
- **位置**: `src/main/java/com/asiainfo/sound/service/flow/ServiceTaskTrackReportListener.java`
- **功能**: 监听服务任务的执行完成，在服务任务结束后自动上报轨迹
- **触发时机**: 服务任务结束时（end事件）

### 2. 修改流程配置

在通用版督办单流程文件中为以下节点添加了轨迹上报监听器：

#### 用户任务节点
- **省侧接口人审批** (sid-5654C886-6881-4A5C-84FF-001D402A3013)
- **工单再处理** (sid-04BAA818-DBD6-4547-9A8C-A4B285596B13)
- **三级领导审批** (sid-7DEFD704-2895-4652-9FDF-CE478B44BF48)
- **副总经理审批** (sid-131F3E43-5FCA-41E3-9932-89CE15000A2A)
- **部门领导审批** (sid-44439427-7889-4080-845C-D23892849627)
- **工单派发** (sid-88834BA8-1555-47A4-90BA-F4A6F37B64B8)

#### 服务任务节点
- **生成附件** (sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D)
- **省内归档** (sid-07598425-EECC-4390-A381-EFD89FBF616D)

### 3. 创建支持服务

#### OrderTrackTaskLogService
- **接口**: `src/main/java/com/asiainfo/sound/service/OrderTrackTaskLogService.java`
- **实现**: `src/main/java/com/asiainfo/sound/service/impl/OrderTrackTaskLogServiceImpl.java`
- **功能**: 管理工单轨迹任务日志的CRUD操作

#### WorkOrderTrackinforService新增方法
- **方法**: `handleCurrentNodeTrackinfor()`
- **功能**: 只上报当前节点的轨迹记录，而不是所有轨迹记录
- **参数**: 包含任务名称和任务ID，用于精确定位当前节点轨迹
- **时间调整**: 自动调整handingTime字段，增加10分钟补偿服务器时间差

#### WorkOrderTrackinforService增强方法
- **方法**: `adjustHandingTime()`
- **功能**: 调整处理时间，增加10分钟来补偿服务器时间差
- **输入格式**: YYYYMMDDHHMMSS（如：20241228143000）
- **输出格式**: YYYYMMDDHHMMSS（如：20241228144000）
- **异常处理**: 格式错误或调整失败时返回原始时间

#### OrderTrackRecordMapper新增方法
- **方法**: `getCurrentNodeTrackRecords()`
- **功能**: 根据工单编号、任务名称和任务ID查询当前节点的轨迹记录
- **SQL**: 支持按任务ID和任务名称精确查找，如果找不到则返回最新记录

## 工作流程

1. **任务完成触发**: 当督办单流程中的任务完成时，相应的监听器被触发
2. **构建上报请求**: 监听器收集当前流程变量和用户信息，构建轨迹上报请求
3. **创建任务日志**: 创建OrderTrackTaskLog记录，记录上报任务的基本信息
4. **事务同步执行**: 使用TransactionSync确保在当前事务提交后执行轨迹上报
5. **执行当前节点轨迹上报**: 调用WorkOrderTrackinforService.handleCurrentNodeTrackinfor()方法只上报当前节点的轨迹
6. **更新日志状态**: 根据上报结果更新任务日志的状态和结果

## 轨迹上报策略

### 当前节点轨迹上报
- **策略**: 每个用户操作节点轨迹上报时只上报当前节点的轨迹记录
- **实现**: 通过任务名称和任务ID查找当前节点对应的轨迹记录
- **备选方案**: 如果找不到当前节点的轨迹记录，则使用最新的一条轨迹记录
- **优势**: 避免重复上报历史轨迹，提高上报效率，减少网络传输量

### 时间调整策略
- **问题**: 服务器时间与现实时间存在10分钟的差异
- **解决方案**: 在轨迹上报时自动将handingTime字段增加10分钟
- **实现**: 通过adjustHandingTime方法处理时间字符串格式（YYYYMMDDHHMMSS）
- **边界处理**: 支持跨日、跨月、跨年的时间调整
- **异常处理**: 如果时间格式不正确或调整失败，返回原始时间并记录日志

## 日志状态说明

- **190**: 待处理 - 任务日志创建时的初始状态
- **191**: 附件处理中 - 表示处理失败的状态
- **200**: 可回复 - 表示轨迹上报成功的状态

## 异常处理

- 所有轨迹上报过程都有完整的异常处理机制
- 异常信息会记录到日志中，便于问题排查
- 任务日志会记录详细的处理结果和错误信息

## 使用说明

1. 部署更新后的流程配置文件
2. 确保相关的监听器类已正确编译和部署
3. 流程执行时会自动触发轨迹上报，无需手动操作

## 注意事项

- 轨迹上报是异步执行的，不会影响主流程的执行
- 如果轨迹上报失败，不会影响流程的正常进行
- 所有上报操作都有详细的日志记录，便于监控和排查问题