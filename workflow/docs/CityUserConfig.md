# 通用城市用户配置功能

## 功能概述

本功能将原本在各种预警处理方法中硬编码的城市用户配置改为从统一的数据库表中动态读取，实现了通用化的配置管理。不仅支持Warning93，也支持Warning94、Warning95等其他类型的预警单配置，大大提高了系统的可维护性和灵活性。

## 主要特性

### 1. 通用化设计
- **多类型支持**: 支持warning_93、warning_94、warning_95等多种配置类型
- **统一管理**: 所有类型的城市用户配置都在同一张表中管理
- **扩展性强**: 可以轻松添加新的配置类型

### 2. 技术特点
- **无XML配置**: 使用MyBatis注解方式，不需要XML映射文件
- **注解驱动**: 所有SQL都通过注解定义，代码更简洁
- **事务支持**: 支持事务管理，保证数据一致性

## 主要改动

### 1. 数据库层面

#### 新增表：`city_user_config`
- **表名**: `city_user_config`
- **用途**: 存储通用的城市用户配置
- **主要字段**:
  - `id`: 主键
  - `config_type`: 配置类型（如：warning_93、warning_94等）
  - `city_prefix`: 城市名称前缀（如：银川、石嘴山等）
  - `user_id`: 对应的用户ID
  - `user_name`: 用户姓名（冗余字段）
  - `description`: 配置描述
  - `enabled`: 是否启用（1：启用，0：禁用）
  - `sort_order`: 排序字段（数字越小优先级越高）
  - `create_time`: 创建时间
  - `update_time`: 更新时间
  - `create_by`: 创建人
  - `update_by`: 更新人

#### 建表SQL
```sql
-- 主建表脚本（包含所有配置类型的默认数据）
-- 执行 workflow/src/main/resources/sql/city_user_config.sql

-- Warning94/95/96补充脚本（示例用户ID）
-- 执行 workflow/src/main/resources/sql/city_user_config_warning94_95.sql

-- Warning96专用脚本（升级投诉预警单，包含特殊部门配置）
-- 执行 workflow/src/main/resources/sql/city_user_config_warning96.sql

-- 实际用户配置脚本（需要根据实际情况修改用户ID）
-- 执行 workflow/src/main/resources/sql/city_user_config_real_users.sql

-- 配置更新脚本（用于后续维护）
-- 执行 workflow/src/main/resources/sql/update_city_user_config.sql
```

### 2. 后端代码

#### 新增文件
1. **实体类**: `CityUserConfig.java`
2. **Mapper接口**: `CityUserConfigMapper.java` (使用注解，无XML)
3. **Service接口**: `CityUserConfigService.java`
4. **Service实现**: `CityUserConfigServiceImpl.java`
5. **Controller**: `CityUserConfigController.java`
6. **DTO**: `CityUserConfigDto.java`
7. **初始化器**: `CityUserConfigInitializer.java`

#### 修改文件
- **SoundCssServiceImpl.java**: 
  - 修改 `handleWarning93` 方法，从数据库读取配置
  - 新增 `handleWarning94`、`handleWarning95` 方法
  - 新增通用处理方法 `handleWarningByConfigType`

### 3. 前端代码

#### 新增文件
1. **Vue组件**: `cityUserConfig.vue`
2. **API接口**: `cityUserConfig.js`
3. **路由配置**: `config/index.js`

## 使用说明

### 1. 数据库初始化

#### 方案一：完整初始化（推荐）
```bash
# 1. 执行主建表脚本（包含Warning93的真实用户配置）
mysql -u username -p database_name < workflow/src/main/resources/sql/city_user_config.sql

# 2. 根据实际需求选择执行以下脚本之一：

# 选项A：使用示例用户ID（适合测试环境）
mysql -u username -p database_name < workflow/src/main/resources/sql/city_user_config_warning94_95.sql

# 选项B：使用实际用户ID（适合生产环境，需要先修改脚本中的用户ID）
mysql -u username -p database_name < workflow/src/main/resources/sql/city_user_config_real_users.sql
```

#### 方案二：仅建表，通过应用自动初始化
```bash
# 只执行建表语句，应用启动时会自动初始化默认配置
mysql -u username -p database_name -e "
CREATE TABLE IF NOT EXISTS city_user_config (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  config_type varchar(50) NOT NULL,
  city_prefix varchar(50) NOT NULL,
  user_id varchar(100) NOT NULL,
  user_name varchar(100) DEFAULT NULL,
  description varchar(255) DEFAULT NULL,
  enabled tinyint(1) NOT NULL DEFAULT '1',
  sort_order int(11) NOT NULL DEFAULT '0',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  create_by varchar(100) DEFAULT NULL,
  update_by varchar(100) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY uk_config_type_city_prefix (config_type, city_prefix)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
"
```

### 2. 应用启动

应用启动时会自动检查配置表，如果为空会自动初始化所有类型的默认配置。

### 3. 配置管理

#### 访问管理页面
- URL: `/config/cityUser`
- 功能: 增删改查城市用户配置，支持按配置类型筛选

#### API接口
- `GET /cityUserConfig/list` - 查询所有启用的配置
- `GET /cityUserConfig/list/{configType}` - 根据配置类型查询启用的配置
- `GET /cityUserConfig/all` - 查询所有配置
- `POST /cityUserConfig/save` - 保存配置
- `PUT /cityUserConfig/update` - 更新配置
- `DELETE /cityUserConfig/delete/{id}` - 删除配置
- `DELETE /cityUserConfig/deleteByType/{configType}` - 根据配置类型删除所有配置
- `POST /cityUserConfig/init/{configType}` - 初始化指定类型的默认配置
- `POST /cityUserConfig/initAll` - 初始化所有类型的默认配置
- `GET /cityUserConfig/test/{configType}/{cityName}` - 测试功能

### 4. 配置说明

#### 默认配置类型
| 配置类型 | 说明 | 真实用户 | 特殊说明 |
|---------|------|----------|----------|
| warning_93 | 整体申告预警单 | liufang, jihongyan等 | 基础城市配置 |
| warning_94 | 重点产品投诉预警单 | liufang, pq_kouxiaoju等 | 基础城市+特殊部门配置 |
| warning_95 | 重点政策投诉预警单 | liufang, pq_kouxiaoju等 | 基础城市+特殊部门配置 |
| warning_96 | 升级投诉预警单 | liufang, wanglimin等 | 基础城市+特殊部门配置 |

#### 匹配规则
- 使用 `cityName.startsWith(cityPrefix)` 进行匹配
- 只匹配启用状态的配置
- 按 `sort_order` 字段排序，数字越小优先级越高
- 找到第一个匹配的即返回

## 核心逻辑变更

### 原始代码（硬编码方式）
```java
private void handleWarning93(Map<String, Object> dispatchMap, JSONObject jo) {
    String cityName = String.valueOf(jo.get("cityName"));
    if (StringUtils.isBlank(cityName)) {
        return;
    }
    if (cityName.startsWith("银川")){
        dispatchMap.put("cityUser","liufang");
    }
    if (cityName.startsWith("石嘴山")){
        dispatchMap.put("cityUser","jihongyan");
    }
    // ... 其他硬编码配置
}
```

### 新代码（通用配置方式）
```java
private void handleWarning93(Map<String, Object> dispatchMap, JSONObject jo) {
    handleWarningByConfigType(dispatchMap, jo, "warning_93", "整体申告预警单(warning_93)");
}

private void handleWarning94(Map<String, Object> dispatchMap, JSONObject jo) {
    handleWarningByConfigType(dispatchMap, jo, "warning_94", "Warning94预警单");
}

private void handleWarning95(Map<String, Object> dispatchMap, JSONObject jo) {
    handleWarningByConfigType(dispatchMap, jo, "warning_95", "Warning95预警单");
}

private void handleWarning96(Map<String, Object> dispatchMap, JSONObject jo) {
    handleWarningByConfigType(dispatchMap, jo, "warning_96", "升级投诉预警单(warning_96)");
}

private void handleWarningByConfigType(Map<String, Object> dispatchMap, JSONObject jo, String configType, String warningTypeName) {
    String cityName = String.valueOf(jo.get("cityName"));
    if (StringUtils.isBlank(cityName)) {
        return;
    }
    
    // 从通用配置表中读取指定类型的用户配置
    String userId = cityUserConfigService.getUserIdByConfigTypeAndCityName(configType, cityName);
    if (StringUtils.isNotBlank(userId)) {
        dispatchMap.put("cityUser", userId);
        log.info("{} - 城市: {}, 分配给用户: {}", warningTypeName, cityName, userId);
    } else {
        log.warn("{} - 未找到城市 {} 对应的用户配置", warningTypeName, cityName);
    }
}
```

## 优势

1. **通用化**: 一套代码支持多种预警类型的配置
2. **可维护性**: 配置不再硬编码，可通过管理界面动态修改
3. **灵活性**: 支持新增、删除、禁用配置，无需修改代码
4. **扩展性**: 可以轻松扩展到其他类似的配置场景
5. **可追溯性**: 记录配置的创建和修改历史
6. **无XML**: 使用注解方式，代码更简洁

## 扩展使用

### 添加新的配置类型
1. 在 `CityUserConfigServiceImpl` 中添加新的配置类型常量
2. 在 `createDefaultConfigsByType` 方法中添加对应的默认配置创建逻辑
3. 在 `SoundCssServiceImpl` 中添加对应的处理方法
4. 在前端页面的配置类型选择器中添加新选项

### 示例：添加Warning96配置
```java
// 1. 添加常量
public static final String CONFIG_TYPE_WARNING_96 = "warning_96";

// 2. 添加处理方法
private void handleWarning96(Map<String, Object> dispatchMap, JSONObject jo) {
    handleWarningByConfigType(dispatchMap, jo, "warning_96", "Warning96预警单");
}

// 3. 添加默认配置创建逻辑
case CONFIG_TYPE_WARNING_96:
    configs.addAll(createWarning96DefaultConfigs(createBy, now));
    break;
```

## Warning94/95/96 详细配置说明

### Warning94（重点产品投诉预警单）
基于原有硬编码 `handleWarning94` 方法的配置：

**基础城市配置（5个）**：
- **银川**：liufang（刘芳）
- **石嘴山**：pq_kouxiaoju（寇小菊）
- **吴忠**：pq_wangxianjun（王先军）
- **固原**：renwei1（任伟）
- **中卫**：pq_heyingxia（何英霞）

**特殊部门配置（2个）**：
- **互联网销售分公司**：baojianming（包建明）
- **品质**：pq_zhouzhaoqin（周兆琴）

### Warning95（重点政策投诉预警单）
基于原有硬编码 `handleWarning95` 方法的配置：

**基础城市配置（5个）**：
- **银川**：liufang（刘芳）
- **石嘴山**：pq_kouxiaoju（寇小菊）
- **吴忠**：pq_wangxianjun（王先军）
- **固原**：renwei1（任伟）
- **中卫**：pq_heyingxia（何英霞）

**特殊部门配置（2个）**：
- **互联网销售分公司**：baojianming（包建明）
- **品质**：pq_zhouzhaoqin（周兆琴）

### Warning96（升级投诉预警单）
基于原有硬编码 `handleWarning96` 方法的配置：

**基础城市配置（5个）**：
- **银川**：liufang（刘芳）
- **石嘴山**：pq_kouxiaoju（寇小菊）
- **吴忠**：pq_wangxianjun（王先军）
- **固原**：renwei1（任伟）
- **中卫**：pq_heyingxia（何英霞）

**特殊部门配置（5个）**：
- **市场部**：wanglimin（王利民）
- **网络部**：shangcuili（尚翠丽）
- **互联网销售分公司**：baojianming（包建明）
- **信息安全管理部**：gaoming（高明）
- **品质**：chenjing2（陈静）

### 配置特点
1. **真实用户ID**：所有配置都使用原有硬编码中的真实用户ID
2. **双重匹配**：既支持城市匹配，也支持部门匹配
3. **优先级排序**：通过 `sort_order` 字段控制匹配优先级
4. **专用脚本**：每种类型都提供专门的SQL脚本进行配置

## 注意事项

1. **数据库权限**: 确保应用有对新表的读写权限
2. **缓存考虑**: 如果配置变更频繁，可考虑添加缓存机制
3. **备份**: 在修改配置前建议备份原有配置
4. **测试**: 使用测试功能验证配置是否正确
5. **唯一约束**: 配置类型+城市前缀的组合必须唯一

## SQL脚本说明

### 脚本文件列表

| 脚本文件 | 用途 | 说明 |
|---------|------|------|
| `city_user_config.sql` | 主建表脚本 | 包含表结构和Warning93的真实用户配置 |
| `city_user_config_warning94_95.sql` | Warning94/95/96补充脚本 | 包含示例用户ID，适合测试环境 |
| `city_user_config_warning94_95_real.sql` | Warning94/95真实用户脚本 | 基于原硬编码的真实用户ID |
| `city_user_config_warning96.sql` | Warning96专用脚本 | 升级投诉预警单配置，包含特殊部门 |
| `city_user_config_real_users.sql` | 实际用户配置脚本 | 提供真实用户ID配置模板 |
| `update_city_user_config.sql` | 配置更新脚本 | 用于后续维护和更新配置 |

### 使用场景

#### 1. 新环境部署
```bash
# 测试环境
mysql -u username -p database_name < city_user_config.sql
mysql -u username -p database_name < city_user_config_warning94_95.sql

# 生产环境
mysql -u username -p database_name < city_user_config.sql
# 修改 city_user_config_real_users.sql 中的用户ID后执行
mysql -u username -p database_name < city_user_config_real_users.sql
```

#### 2. 配置维护
```bash
# 使用更新脚本进行日常维护
mysql -u username -p database_name < update_city_user_config.sql
```

#### 3. 用户ID配置指南

**Warning94用户ID配置**：
- 如果Warning94与Warning93使用相同处理人，直接使用方案一
- 如果Warning94需要专门的处理人，修改方案二中的用户ID

**Warning95用户ID配置**：
- 同Warning94的配置方式
- 支持与其他类型使用相同或不同的处理人

## 扩展建议

1. **缓存优化**: 可以添加Redis缓存提高查询性能
2. **配置版本**: 可以添加配置版本管理功能
3. **批量操作**: 可以添加批量导入导出功能
4. **权限控制**: 可以添加配置修改权限控制
5. **审计日志**: 可以添加配置变更审计日志
6. **配置模板**: 可以添加配置模板功能，快速创建新类型配置
