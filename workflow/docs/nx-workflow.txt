Swagger文档地址：
开发环境:
    http://localhost:10101/login?userName=jindongxun
    http://localhost:10101/doc.html
测试环境：
    http://basenx-workflow-api.asiainfo.work/login?userName=jindongxun
    http://basenx-workflow-api.asiainfo.work/doc.html
UAT环境：
    http://**************:18090/graydoc/login?userName=jindongxun
    http://**************:18090/graydoc/doc.html

正式环境doc：
    http://**************:8090/swaggerdoc/login?userName=jindongxun
    http://**************:8090/swaggerdoc/doc.html



流程日志获取方式

UAT环境
SELECT * FROM ACT_HI_VARINST WHERE NAME_='identifier' AND TEXT_='20221115GZL00571138709';
SELECT * FROM ACT_HI_ATTACHMENT WHERE proc_inst_id_='587b0cd3-64c4-11ed-bcc5-005056b487af';
delete from ACT_HI_ATTACHMENT WHERE proc_inst_id_='587b0cd3-64c4-11ed-bcc5-005056b487af';


删除接口删除不了处理方法
先执行以下删除操作，在执行swagger删除

SELECT proc_inst_id_ FROM workflow_nx.ACT_HI_VARINST WHERE NAME_ = 'identifier' AND TEXT_='20221111GZL00574738339';

delete from ACT_HI_ACTINST where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_ATTACHMENT where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_COMMENT where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_DETAIL where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_IDENTITYLINK where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_PROCINST where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_TASKINST where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_TSK_LOG where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';
delete from ACT_HI_VARINST where PROC_INST_ID_ = 'bcc379cd-6118-11ed-807e-005056b487af';


SELECT * FROM act_hi_varinst WHERE name_='identifier' AND text_='';




#删除工单号对应的所有
 curl --request POST http://10.250.162.240:8090/api/backstage/deleteProcessInstance2?workOrderId=20250614GZL62310011141
 
 #删除某一条实例
 1、curl --request POST http://10.250.162.240:8090/api/backstage/deleteProcessInstance?taskId=xxx
 2、curl --request POST http://10.250.162.240:8090/api/backstage/deleteHistoricProcessInstance?taskId=xxx
 
 
 http://10.250.162.240:8090/swaggerdoc/workflowLoad/sendOpen
 
 http://10.250.162.240:8090/swaggerdoc/workflowLoad/sendClose
 
 
 select * from sound_group_identy_log where identifier =
 
 
 select * from order_track_record where identifier = '20250725GZL00570918321'
 
 
 http://10.250.162.240:8090/swaggerdoc/demo?userName=jindongxun&token=12

http://10.250.162.240:8090/swaggerdoc/doc.html

wangzhening