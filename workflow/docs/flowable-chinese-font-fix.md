# Flowable 工作流图片中文乱码解决方案

## 问题描述

在使用 Flowable 工作流引擎生成流程图时，中文节点名称、标签等显示为乱码或方框。

## 问题原因

1. **字体不支持中文**：Flowable 默认使用的字体不支持中文字符
2. **字符编码问题**：JVM 字符编码设置不正确
3. **图形环境配置**：容器环境中图形渲染配置不当

## 解决方案

### 1. 添加 Flowable 字体配置类

已创建 `FlowableConfig.java` 配置类：

```java
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {
    
    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        // 设置支持中文的字体
        engineConfiguration.setActivityFontName("SimSun");
        engineConfiguration.setLabelFontName("SimSun");
        engineConfiguration.setAnnotationFontName("SimSun");
    }
}
```

### 2. 配置文件设置

在 `application.yml` 中添加字体配置：

```yaml
flowable:
  font:
    activity: SimSun      # 活动节点字体
    label: SimSun         # 标签字体  
    annotation: SimSun    # 注释字体
```

### 3. JVM 系统属性设置

配置类中已设置必要的 JVM 属性：

```java
System.setProperty("java.awt.headless", "true");
System.setProperty("file.encoding", "UTF-8");
System.setProperty("sun.jnu.encoding", "UTF-8");
```

### 4. Docker 镜像支持

项目使用的 `openjdk:8-with-font` 镜像已包含中文字体支持。

## 字体优先级

配置类会按以下优先级选择字体：

1. SimSun (宋体) - 首选
2. Microsoft YaHei (微软雅黑)
3. SimHei (黑体)
4. SansSerif (系统默认) - 最后备选

## 测试和验证

### 1. 使用测试接口

访问以下接口验证字体配置：

- `GET /font-test/available-fonts` - 获取所有可用字体
- `GET /font-test/check-chinese-fonts` - 检查中文字体可用性
- `GET /font-test/check-font?fontName=SimSun` - 检查指定字体

### 2. 查看日志

启动应用时会在日志中显示字体配置信息：

```
INFO  - JVM图形环境属性设置完成
INFO  - Flowable流程图字体配置完成 - Activity: SimSun, Label: SimSun, Annotation: SimSun
```

### 3. 测试流程图生成

调用 `GET /workflow/image?processInstanceId=xxx` 接口生成流程图，检查中文是否正常显示。

## 故障排除

### 1. 字体不可用

如果 SimSun 字体不可用，系统会自动选择备用字体：

```
WARN - 字体 SimSun 不可用，使用备用字体: Microsoft YaHei
```

### 2. 容器环境问题

确保使用包含字体的 Docker 镜像：

```dockerfile
FROM docker-registry-apaas-dnc.asiainfo.com.cn/openjdk:8-with-font
```

### 3. 调试模式

启用 DEBUG 日志级别查看详细的字体检查信息：

```yaml
logging:
  level:
    com.asiainfo.sound.config.FlowableConfig: DEBUG
```

## 常见字体列表

| 字体名称 | 中文名称 | 适用场景 |
|---------|---------|---------|
| SimSun | 宋体 | 通用，推荐 |
| Microsoft YaHei | 微软雅黑 | 现代界面 |
| SimHei | 黑体 | 标题显示 |
| KaiTi | 楷体 | 正式文档 |
| SansSerif | 系统默认 | 备用选择 |

## 注意事项

1. **重启应用**：修改字体配置后需要重启应用生效
2. **缓存清理**：如果使用了图片缓存，需要清理缓存
3. **性能影响**：字体渲染可能会略微影响图片生成性能
4. **兼容性**：不同操作系统的字体名称可能不同

## 验证成功标志

配置成功后，工作流图片中的中文应该能够正常显示，不再出现乱码或方框。