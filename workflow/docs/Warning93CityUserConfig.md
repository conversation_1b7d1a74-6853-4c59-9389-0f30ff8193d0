# 通用城市用户配置功能

## 功能概述

本功能将原本在各种预警处理方法中硬编码的城市用户配置改为从统一的数据库表中动态读取，实现了通用化的配置管理，不仅支持Warning93，也支持Warning94、Warning95等其他类型的预警单配置，大大提高了系统的可维护性和灵活性。

## 主要改动

### 1. 数据库层面

#### 新增表：`warning_93_city_user_config`
- **表名**: `warning_93_city_user_config`
- **用途**: 存储整体申告预警单的城市用户配置
- **主要字段**:
  - `id`: 主键
  - `city_prefix`: 城市名称前缀（如：银川、石嘴山等）
  - `user_id`: 对应的用户ID
  - `user_name`: 用户姓名（冗余字段）
  - `description`: 配置描述
  - `enabled`: 是否启用（1：启用，0：禁用）
  - `create_time`: 创建时间
  - `update_time`: 更新时间
  - `create_by`: 创建人
  - `update_by`: 更新人

#### 建表SQL
```sql
-- 执行 workflow/src/main/resources/sql/warning_93_city_user_config.sql
```

### 2. 后端代码

#### 新增文件
1. **实体类**: `Warning93CityUserConfig.java`
2. **Mapper接口**: `Warning93CityUserConfigMapper.java`
3. **XML映射**: `Warning93CityUserConfigMapper.xml`
4. **Service接口**: `Warning93CityUserConfigService.java`
5. **Service实现**: `Warning93CityUserConfigServiceImpl.java`
6. **Controller**: `Warning93CityUserConfigController.java`
7. **DTO**: `Warning93CityUserConfigDto.java`
8. **初始化器**: `Warning93ConfigInitializer.java`

#### 修改文件
- **SoundCssServiceImpl.java**: 修改 `handleWarning93` 方法，从数据库读取配置

### 3. 前端代码

#### 新增文件
1. **Vue组件**: `warning93CityUserConfig.vue`
2. **API接口**: `warning93Config.js`
3. **路由配置**: `config/index.js`

## 使用说明

### 1. 数据库初始化

执行建表SQL：
```bash
mysql -u username -p database_name < workflow/src/main/resources/sql/warning_93_city_user_config.sql
```

### 2. 应用启动

应用启动时会自动检查配置表，如果为空会自动初始化默认配置。

### 3. 配置管理

#### 访问管理页面
- URL: `/config/warning93`
- 功能: 增删改查城市用户配置

#### API接口
- `GET /warning93/config/list` - 查询启用的配置
- `GET /warning93/config/all` - 查询所有配置
- `POST /warning93/config/save` - 保存配置
- `PUT /warning93/config/update` - 更新配置
- `DELETE /warning93/config/delete/{id}` - 删除配置
- `POST /warning93/config/init` - 初始化默认配置
- `GET /warning93/config/test/{cityName}` - 测试功能

### 4. 配置说明

#### 默认配置
| 城市前缀 | 用户ID | 用户姓名 | 描述 |
|---------|--------|----------|------|
| 银川 | liufang | 刘芳 | 银川地区整体申告预警单处理人 |
| 石嘴山 | jihongyan | 纪红燕 | 石嘴山地区整体申告预警单处理人 |
| 吴忠 | maxiaojuan1 | 马小娟 | 吴忠地区整体申告预警单处理人 |
| 固原 | renwei1 | 任伟 | 固原地区整体申告预警单处理人 |
| 中卫 | pq_heyingxia | 何英霞 | 中卫地区整体申告预警单处理人 |

#### 匹配规则
- 使用 `cityName.startsWith(cityPrefix)` 进行匹配
- 只匹配启用状态的配置
- 按配置顺序进行匹配，找到第一个匹配的即返回

## 核心逻辑变更

### 原始代码
```java
private void handleWarning93(Map<String, Object> dispatchMap, JSONObject jo) {
    String cityName = String.valueOf(jo.get("cityName"));
    if (StringUtils.isBlank(cityName)) {
        return;
    }
    if (cityName.startsWith("银川")){
        dispatchMap.put("cityUser","liufang");
    }
    if (cityName.startsWith("石嘴山")){
        dispatchMap.put("cityUser","jihongyan");
    }
    // ... 其他硬编码配置
}
```

### 新代码
```java
private void handleWarning93(Map<String, Object> dispatchMap, JSONObject jo) {
    String cityName = String.valueOf(jo.get("cityName"));
    if (StringUtils.isBlank(cityName)) {
        return;
    }
    
    // 从数据库表中读取用户配置
    String userId = warning93CityUserConfigService.getUserIdByCityName(cityName);
    if (StringUtils.isNotBlank(userId)) {
        dispatchMap.put("cityUser", userId);
        log.info("整体申告预警单(warning_93) - 城市: {}, 分配给用户: {}", cityName, userId);
    } else {
        log.warn("整体申告预警单(warning_93) - 未找到城市 {} 对应的用户配置", cityName);
    }
}
```

## 优势

1. **可维护性**: 配置不再硬编码，可通过管理界面动态修改
2. **灵活性**: 支持新增、删除、禁用配置，无需修改代码
3. **可追溯性**: 记录配置的创建和修改历史
4. **易扩展**: 可以轻松扩展到其他类似的配置场景

## 注意事项

1. **数据库权限**: 确保应用有对新表的读写权限
2. **缓存考虑**: 如果配置变更频繁，可考虑添加缓存机制
3. **备份**: 在修改配置前建议备份原有配置
4. **测试**: 使用测试功能验证配置是否正确

## 扩展建议

1. **缓存优化**: 可以添加Redis缓存提高查询性能
2. **配置版本**: 可以添加配置版本管理功能
3. **批量操作**: 可以添加批量导入导出功能
4. **权限控制**: 可以添加配置修改权限控制
5. **审计日志**: 可以添加配置变更审计日志
