<template>
  <div>
    <div>
      <el-descriptions>
        <el-descriptions-item label="工单类型"
        
          >{{ tableShow.processVariables.identyType | identyType }}
        </el-descriptions-item>
        <el-descriptions-item label="工单子类型"
          >{{ tableShow.processVariables.identySubtype | identySubtype }}
        </el-descriptions-item>
        <el-descriptions-item
          v-for="item of detailForm"
          :key="item.id"
          :span="item.params.col ? item.params.col : 1"
          :label="item.name"
        >
          <component
            :is="`detailItem_${item.type}`"
            :info="tableShow.processVariables[item.id]"
            :form="item"
          ></component>
        </el-descriptions-item>
        <el-descriptions-item label="是否展示流程图" >
            <el-button
            class="hide_button"
            style="cursor: pointer;"
            :loading="imgLoading"
            @click.native="getImgUrl(tableShow.processInstanceId)"
            type="warning"
            >{{imgflag ? '隐藏' : '展示'}}
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item
          label="集团推送信息"
          v-if="
            tableShow.processVariables.type == '1' || tableShow.processVariables.identyType == '03'
          "
        >
          <el-button
            class="hide_button"
            style="cursor: pointer"
            @click="openDialog"
            type="warning"
            >打开
          </el-button>
        </el-descriptions-item>
      </el-descriptions>

       <div style="display:flex; justify-content:center;">
          <img v-if="imgflag" :src="imgdata"/>
       </div>

    </div>
    <el-table
      v-loading="loading"
      element-loading-text="拼命加载中"
      :header-cell-style="{
        background: '#f9f9f9',
        color: '#606266',
        fontWeight: '500',
      }"
      :data="tableShow.taskHistory"
      height="370"
      style="width: 100%; overflow-x: hidden; overflow-y: auto"
      row-key="id"
      @expand-change="loadTaskDetail"
    >
      <el-table-column type="expand">
        <template slot-scope="scope" v-if="scope.row.formDetail">
          <div v-if="scope.row.endTime !== null" style=" padding: 0 ">
            <el-card v-for="(item, index) of scope.row.formDetail" :key="index" style="overflow-y: auto;height:210px">
              <div class="el-card__body__option">
                <div class="el-card__child_" v-for="(it, index) of item.childs[0]" :key="index" :style="{display: (index === 0 ||  isShowItem(item.childs[0][0], it.id)) ? 'block' : 'none'}">
                  <span v-if="index === 0 ||  isShowItem(item.childs[0][0], it.id)">{{ it.name }}:  {{ geneVaule(it.value, it.options, it.params, it.type, it.id) }}</span>
                </div>
              </div>
              <div v-if="scope.row.detail.length !== 0">
                <div  v-for="(it, index) of scope.row.detail[0].childs" :key="index">
                  <span v-if="it.type === 'comment'"
                    >处理意见：
                    <Ecard :res='it'></Ecard>
                  </span>
                  <span v-if="it.type === 'file'"
                    >附件：
                    <a
                      target="_blank"
                      :href="
                        downloadUrlWork +
                        `?attachmentId=${it.id}&fileName=${encodeURIComponent(
                          it.name
                        )}`
                      "
                      >{{ it.name }}</a
                    >
                  </span>
                  <span v-if="it.type === 'system'"
                    >日志:{{ it.fullMessage }}
                  </span>
                </div>
                <p style="text-align: right">
                  {{ scope.row.detail[0].userId | assignee }} 提交于 {{ scope.row.detail[0].time }}
                </p>
              </div>
            </el-card>
          </div>
          <div v-else>
            <p style="text-align: center">暂无处理数据</p>
          </div>
          <!-- <el-card v-for="(item, index) of scope.row.detail" :key="index" style="overflow-y: auto;height:210px">
            <div v-for="(it, index) of item.childs" :key="index">
              <span v-if="it.type === 'comment'"
                >处理意见：
                <Ecard :res='it'></Ecard>
              </span>
              <span v-if="it.type === 'file'"
                >附件：
                <a
                  target="_blank"
                  :href="
                    downloadUrlWork +
                    `?attachmentId=${it.id}&fileName=${encodeURIComponent(
                      it.name
                    )}`
                  "
                  >{{ it.name }}</a
                >
              </span>
              <span v-if="it.type === 'system'"
                >日志:{{ it.fullMessage }}
              </span>
            </div>
            <p style="text-align: right">
              {{ item.userId | assignee }} 提交于 {{ item.time }}
            </p>
          </el-card>

          <div v-if="scope.row.detail.length === 0">
            <p style="text-align: center">暂无处理数据</p>
          </div> -->
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="节点名称"
        width="200"
      ></el-table-column>
      <el-table-column prop="assignee" label="处理人" width="200">
        <template slot-scope="scope">
          {{ scope.row.assignee | assignee }}
        </template>
      </el-table-column>

      <el-table-column label="开始时间" width="auto">
        <template slot-scope="scope">
          {{
            scope.row.createTime
              ? new Date(scope.row.createTime)
              : null | dateFormat("yyyy/MM/dd HH:mm:ss")
          }}
        </template>
      </el-table-column>

      <el-table-column label="完成时间" width="auto">
        <template slot-scope="scope">
          {{
            scope.row.endTime
              ? new Date(scope.row.endTime)
              : null | dateFormat("yyyy/MM/dd HH:mm:ss")
          }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 再处理信息 -->
    <!-- <el-divider></el-divider> -->
    <template>
      <div class="el-descriptions__header">
        <div
          class="el-descriptions__title"
          style="color: #303133; margin: 16px 0 0 0px"
        >
          集团通知
        </div>
      </div>
      <el-table
        v-loading="loading"
        element-loading-text="拼命加载中"
        :header-cell-style="{
          background: '#f9f9f9',
          color: '#606266',
          fontWeight: '500',
        }"
        :data="handleInfos"
        height="200"
        style="
          width: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          margin-top: 16px;
        "
        row-key="id"
      >
        <el-table-column
          prop="stateCode"
          label="操作节点"
          width="200"
        ></el-table-column>
        <!-- <el-table-column
        prop="handler"
        label="处理人"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.handler }}
        </template>
      </el-table-column> -->
       <el-table-column label="发起省端" width="auto">
          <template slot-scope="scope">
            {{ scope.row.launchCompany }}
          </template>
        </el-table-column>
         <el-table-column label="接收省端" width="auto">
          <template slot-scope="scope">
            {{ scope.row.forwardCompany }}
          </template>
        </el-table-column>
        <el-table-column label="处理意见" width="auto">
          <template slot-scope="scope">
            {{ scope.row.handlingOpinion }}
          </template>
        </el-table-column>
        <el-table-column label="处理时间" width="auto">
          <template slot-scope="scope">
            {{
              scope.row.handingTime
                ? new Date(scope.row.handingTime)
                : null | dateFormat("yyyy/MM/dd HH:mm:ss")
            }}
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!-- 集团推送信息 -->
    <el-dialog
      title="推送信息"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      center
      width="80%"
    >
      <div class="descriptions-content">
       <DescriptionItem
        v-for="item in replyInfo === null ? [] : replyInfo"
        :key="item.handingTime"
        :item= item
        ></DescriptionItem>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="dialogVisible = false"
          class="subButton"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable camelcase */
import {
  getHistoricTaskList,
  getTaskDetail,
  getDetailForm,
} from '@/api/workflow/index';
import Ecard from '@/components/global/detail/components/Ecard';
import DescriptionItem from '@/components/global/detail/components/DescriptionItem';
import {findOperationNodeLog} from '@/api/apply/index.js';
import {queryIdentyReply} from '@/api/form/index.js';
import {getImg} from '@/api/common/index.js';
import {dateFormat} from '@/utils/index.js';
import filter, {allCode} from '@/utils/filter.js';
import modules from '@/components/dynamic/detailItem.js';
console.log(modules);
export default {
  name: 'Detail',
  filters: filter,
  props: {
    f5: Boolean,
    data: Object,
  },
  components: {
    ...modules,
    Ecard,
    DescriptionItem
  },
  data() {
    return {
      // 再处理数据
      handleInfos: [],
      loading: false,
      imgLoading: false,
      downloadUrlWork: `${process.env.VUE_APP_REQUEST_URL}/workflow/attachment`,
      downloadUrlCommon: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downAttachList`,
      downloadUrlReply: `${process.env.VUE_APP_REQUEST_URL}/identyCommon/downReplyAttachList`,
      // 是否展示流程图
      imgflag: false,
      // 详情数据
      tableShow: this.data,
      imgdata: '',
      dialogVisible: false,
      // 集团信息
      replyInfo: null,
      detailfields: null,


    };
  },
  watch: {
    f5(val) {
      if (val) {
        this.showData();
      }
    },
    // tableShow(val) {
    //   val && this.showData();
    // },
  },
  computed: {
    detailForm() {
      const orderData =
        this.detailfields === null
          ? []
          : this.detailfields.filter(
            (item) =>
              item.params.showType === '2' || item.params.showType === '0'
          );
      orderData.sort((a, b) => a.params.order - b.params.order);
      console.log(orderData);
      return orderData;
    },
  },
  created() {
    getDetailForm({
      processDefinitionProcessId: this.tableShow.processDefinitionId,
    }).then((res) => {
      if (res.data.formModel) {
        this.detailfields = res.data.formModel.fields;
        // this.$emit('detailInfo',res.data);
        // 2023 gkh 修改 工单类型为 通用版任务单 且 是否需要回复 为不回复 在受理的时候隐藏 回复集团 下拉选项
        const info = res.data;
        console.log('this.tableShow:',this.tableShow);
        if(info.name === '通用版任务单') {
          if(info && info.formModel && Array.isArray(info.formModel.fields)) {
            info.formModel.fields.forEach((i) => {
              if(i.id === 'isReplyCss' && (i.params.showType === '0' || i.params.showType === '1')) {
                console.log('=======>',this.tableShow.processVariables['isReplyCss']);
                if(this.tableShow.processVariables['isReplyCss'] === '1') {
                  // 在点击受理时 隐藏回复集团 选项
                  this.$emit('hiddenOptionReplyGroup',true);
                }
              }
            });
          }
        }

      }
    });
  },
  mounted() {
    this.showData();
    this.getIdentyReply();
    this.findNodeLog();
  },
  methods: {
    // 处理受理后显示
    geneVaule(val, opt, params, type, id) {
      console.warn(params);
      if (type === 'deptSelect') {
        let tmpChild = allCode(id);
        if(tmpChild) {
          val.forEach((ele) => {
            [tmpChild] = tmpChild.filter((ele1) => ele1.value === ele);
            tmpChild.childs !== null && (tmpChild = tmpChild.childs);
          });
        }

        return tmpChild?.label;
      }
      if (opt) {

        if (!val) {
          return val;
        }
        return opt.filter((ele) => ele.id === val.toString())[0]?.name;
      } else if (params.hasOwnProperty('selectId')) {
        if (typeof val !== 'string') {
          return val;
        }
        if (params.selectId === 'problemType') {
          if (val.length === 4) {

            return allCode(params.selectId).filter((v) => v.value === val)[0]?.label;
          } else if (val.length === 6) {

            const res = allCode(params.selectId).filter((v) => v.value === val.substring(0, 4));
            return `${res[0]?.label}/${res[0].childs[parseInt(val.substring(4, 6)) - 1]?.label}`;
          }
        }
        return allCode(params.selectId).filter((ele) => ele.value === val)[0]?.label;
      } else if (params.hasOwnProperty('userRoleId')) {
        if (typeof val === 'object' && val !== null) {
          let label = '';
          val.forEach((item) => {
            label += ` ${allCode('user').filter((ele) => ele.value === item)[0]?.label}`;
          });
          return label;
        }
        return allCode('user').filter((ele) => ele.value === val)[0]?.label;
      }
      return val;
    },
    // 判断是否显示条目
    isShowItem(itemOne, id) {
      if(itemOne.params) {
        if(itemOne.params.setVisible[itemOne.value]) {
          return itemOne.params.setVisible[itemOne.value].includes(id);
        }
        return false;
      }
      return false;
    },
    // 调用再处理接口
    findNodeLog() {
      findOperationNodeLog({
        identifier: this.tableShow.processVariables.identifier,
      }).then((res) => {
        this.handleInfos = res.data;
      });
    },
    openDialog() {
      if (this.replyInfo === null) {
        this.$message.info('没有数据');
      } else {
        this.dialogVisible = true;
      }
    },
    getIdentyReply() {
      queryIdentyReply({
        identifier: this.tableShow.processVariables.identifier,
        identyState: this.tableShow.processVariables.identyState,
      }).then((res) => res && (this.replyInfo = res.data));
    },
    loadTaskDetail(rowData) {
      console.log(rowData.hasOwnProperty('detail'));
      console.log(this.tableShow);
      if (rowData.formDetail) {
        return;
      }
      getTaskDetail({taskId: rowData.id}).then((v) => {
        console.log('详细', v.data);
        const arr = [
          ...v.data.attachments,
          ...v.data.comments,
          ...v.data.systemComments,
        ];
        const formArrs = [];
        console.log('扁平化', arr);
        // if(arr.length !== 0) {
        //   arr.sort((a,b) => new Date(a.time).getTime() - new Date(b.time).getTime());
        //   console.log('排序后',arr);
        // }
        // 按照时间分类数据
        const arrs = [];
        arr.forEach((item) => {
          const parent = arrs.find(
            (cur) =>
              dateFormat(new Date(cur.time), 'yyyy/MM/dd HH:mm') ===
              dateFormat(new Date(item.time), 'yyyy/MM/dd HH:mm')
          );
          if (parent) {
            parent.childs.push(item);
          } else {
            console.log('here');
            const obj = {
              time: dateFormat(new Date(item.time), 'yyyy/MM/dd HH:mm'),
              userId: item.userId,
              childs: [item],
            };
            arrs.push(obj);
          }
        });
        formArrs.push({
          id: v.data.formInfo.id,
          name: v.data.formInfo.name,
          key: v.data.formInfo.key,
          childs: [v.data.formInfo.formModel.fields],
        });
        console.log('分类后', arrs);
        console.log('分类后', formArrs);
        // this.$set(rowData, 'detail', v.data);
        this.$set(rowData, 'detail', arrs);
        this.$set(rowData, 'formDetail', formArrs);
        console.log(rowData);
      });
    },
    getImgUrl(processInstanceId) {
      if(this.imgflag === false) {
        this.imgflag = true;
        this.imgLoading = true;
        getImg({processInstanceId}).then((res) => {
          this.imgdata = window.URL.createObjectURL(res);
          this.imgLoading = false;
        });
      }else{
        this.imgflag = false;
      }
    },
    showData() {
      this.loading = true;
      console.log(this.tableShow.hasOwnProperty('taskHistory'));
      getHistoricTaskList(
        {
          currentPage: 0,
          pageSize: 100,
        },
        {
          processInstanceId: this.tableShow.processInstanceId,
          processDefinitionKey: null,
          processVariables: {},
          /**
           * 如果是管理员这个参数才生效，表示显示所有人的
           */
          showAll: true,
          taskStatus: 'NONE',
          taskVariables: {},
          variableNames: null,
        }
      )
        .then((v) => {
          this.$set(this.tableShow, 'taskHistory', v.data.list);
          const [confirmdata] = v.data.list.filter(
            (item) =>
              item.assignee === sessionStorage.getItem('userName') &&
              item.endTime === null
          );
          this.$emit('putconfirm', confirmdata);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped>
.hide_button {
    height: 26px;
    padding: 0px 12px;
}
/* .el-card .is-always-shadow {
  background: red;
} */
  /*滚动条样式*/
::v-deep.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    /*height: 4px;*/
}
::v-deep.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background: rgba(0,0,0,0.2);
}
::v-deep.el-table--scrollable-y .el-table__body-wrapper::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 0;
    background: rgba(0,0,0,0.1);
}
.el-card::-webkit-scrollbar {
    width: 6px;
    /*height: 4px;*/
}
.el-card::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background: rgba(0,0,0,0.2);
}
.el-card::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 0;
    background: rgba(0,0,0,0.1);

}
/* 展开文字左对齐 */
.el-table td.el-table__cell div {
  text-align: left;
}
::v-deep.el-table {
  overflow: scroll;
  flex: auto;
  height: 400px;
  border: 1px solid #e1e1e1;
}

::v-deep.el-table td,
.el-table th.is-leaf,
.el-table--border,
.el-table--group {
  background-color: #ffffff;
}

::v-deep.el-table tbody tr:hover > td {
  background-color: #f6e3c6 !important;
}

::v-deep.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #e1e1e1;
  line-height: 40px;
  padding: 0px;
  box-sizing: border-box;
}

.subButton {
  height: 32px;
  line-height: 32px;
  background-color: rgba(247, 138, 28, 1);
  padding: 0px 16px;
  border-radius: 4px;
  color: #262626;
  border: 1px solid rgba(247, 138, 28, 1);
  box-sizing: border-box;
}

.descriptions-content {
  width: 100%;
  /* min-width: 550px; */
  max-height: 400px;
  overflow: auto;
}

.margin-top {
  margin-top: 10px;
}
.handle_style {
  padding: 0px;
  width: 100%;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between !important;
}
.el-card__body__option {
  display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}
.el-card__child_ {
  width: 33.33%;
}
</style>
