variables:
  CACHE_DIR: "/mnt/temp"
  GIT_SUBMODULE_STRATEGY: recursive
  MAVEN_OPTS: "-Dmaven.repo.local=$CACHE_DIR/.m2_workflow/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version"
  BUILD_OUTPUT: "$CI_PROJECT_NAME/$CI_PIPELINE_ID"
  DOCKER_REGISTRY_PASSWD: ""

stages:
  - check
  - package
  - deploy
  - run
  - clean

#sonarqube-check:
#  stage: check
#  image: maven:3.6.3-jdk-11
#  variables:
#    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#  cache:
#    key: "${CI_JOB_NAME}"
#    paths:
#      - .sonar/cache
#  script:
#    - mvn verify sonar:sonar
#  allow_failure: true
#  only:
#    - master # or the name of your main branch

# sonarqube-check-ui:
#   stage: check
#   image:
#     name: sonarsource/sonar-scanner-cli:latest
#     entrypoint: [""]
#   variables:
#     SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#     GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#   cache:
#     key: "${CI_JOB_NAME}"
#     paths:
#       - .sonar/cache
#   script:
#     - sonar-scanner
#   allow_failure: true
#   only:
#     - master


# 1. 构建成jar包
# 2. 拷贝到指定服务器的指定目录
# 3. 在目标机器上用docker-compose拉起应用
package_service:
  stage: package
  image: docker-registry-apaas-dnc.asiainfo.com.cn/maven:latest
  rules:
   - when: manual
  script:
    - whoami
    - mkdir -p $CACHE_DIR/$BUILD_OUTPUT/docker-build
    - mvn package -Dmaven.test.skip=true
    - bash copy-to-cache.sh $CACHE_DIR/$BUILD_OUTPUT/docker-build jar

package_web:
  stage: package
  image: docker-registry-apaas-dnc.asiainfo.com.cn/node-gyp:1.0
  rules:
   - when: manual
  cache:
    paths:
      - ui/node_modules
  script:
    - mkdir -p $CACHE_DIR/$BUILD_OUTPUT
    - mkdir -p $CACHE_DIR/.npm
    - ln -s $CACHE_DIR/.npm ~/.npm
    - cd ui
    - npm install &&  npm run build
    - cd ..
    - cd mobile
    - npm install &&   npm run build:stage
    - cd ..
    - sh copy-to-cache.sh $CACHE_DIR/$BUILD_OUTPUT/docker-build ui

build_docker_image:
  stage: deploy
  image: docker-registry-apaas-dnc.asiainfo.com.cn/docker:latest
  variables:
    DOCKER_HOST: tcp://docker-in-docker:2375
  dependencies:
    - package_service
    - package_web
  script:
    - docker info
    - docker images
    - cd $CACHE_DIR/$BUILD_OUTPUT/docker-build
    - docker login -u ai -p $DOCKER_REGISTRY_PASSWD docker-registry-apaas-dnc.asiainfo.com.cn
    - sh build-and-push.sh v$CI_PIPELINE_ID

run_in_k8s:
  stage: run
  image: docker-registry-apaas-dnc.asiainfo.com.cn/kubectl:1.19.11
  dependencies:
    - build_docker_image
  script:
    - sed -i "s/IMAGE_TAG/basenx-workflow-v$CI_PIPELINE_ID/g" kubernetes.yaml
    - kubectl --kubeconfig /root/.kube_ai_app/config apply -f kubernetes.yaml

clean_docker_in_docker_image_and_temp:
  stage: clean
  image: docker-registry-apaas-dnc.asiainfo.com.cn/docker:latest
  variables:
    DOCKER_HOST: tcp://docker-in-docker:2375
  when: always
  script:
    - cd $CACHE_DIR/$BUILD_OUTPUT/docker-build
    - sh clean-docker-in-docker.sh v$CI_PIPELINE_ID
    - rm -rf $CACHE_DIR/$BUILD_OUTPUT

