FROM docker-registry-apaas-dnc.asiainfo.com.cn/openjdk:8-with-font
ARG JAR_FILE=target/basenx-workflow.jar
WORKDIR /app
COPY target/lib lib
COPY target/resources resources
COPY ${JAR_FILE} app.jar

ENV TZ=Asia/Shanghai JAVA_OPTS="-Xms1024m -Xmx1024m" SPRING_BOOT_PROFILE="dev"
EXPOSE 10101/tcp
ENTRYPOINT java -jar -Dloader.path=./resources,./lib app.jar ${JAVA_OPTS} --spring.profiles.active=${SPRING_BOOT_PROFILE}