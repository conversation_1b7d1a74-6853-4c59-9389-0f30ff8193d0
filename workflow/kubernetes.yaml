---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    k8s.kuboard.cn/name: basenx-workflow
  name: basenx-workflow
  namespace: ai-app
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      k8s.kuboard.cn/name: basenx-workflow
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        k8s.kuboard.cn/name: basenx-workflow
    spec:
      imagePullSecrets:
        - name: docker-registry
      containers:
        - image: 'docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-client:IMAGE_TAG'
          name: ui
        - image: 'docker-registry-apaas-dnc.asiainfo.com.cn/asiainfo-server:IMAGE_TAG'
          name: service
          volumeMounts:
            - mountPath: /data
              name: volume-km8ks
      volumes:
        - name: volume-km8ks
          persistentVolumeClaim:
            claimName: basenx-workflow
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: basenx-workflow
  namespace: ai-app
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 500M
  storageClassName: rook-cephfs
  volumeMode: Filesystem
---
apiVersion: v1
kind: Service
metadata:
  labels:
    k8s.kuboard.cn/name: basenx-workflow
  name: basenx-workflow
  namespace: ai-app
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
      name: nginx
    - port: 82
      protocol: TCP
      targetPort: 82
      name: nginx2
    - port: 81
      protocol: TCP
      targetPort: 10101
      name: java
  selector:
    k8s.kuboard.cn/name: basenx-workflow
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  annotations: {}
  labels:
    k8s.kuboard.cn/name: basenx-workflow
  name: basenx-workflow
  namespace: ai-app
spec:
  ingressClassName: nginx
  rules:
    - host: basenx-workflow.asiainfo.work
      http:
        paths:
          - backend:
              serviceName: basenx-workflow
              servicePort: 80
            path: /
            pathType: Prefix
    - host: basenx-workflow-mobile.asiainfo.work
      http:
        paths:
          - backend:
              serviceName: basenx-workflow
              servicePort: 82
            path: /
            pathType: Prefix
    - host: basenx-workflow-api.asiainfo.work
      http:
        paths:
          - backend:
              serviceName: basenx-workflow
              servicePort: 81
            path: /
            pathType: Prefix
