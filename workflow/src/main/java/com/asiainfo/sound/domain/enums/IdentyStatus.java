package com.asiainfo.sound.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/11/2 14:58
 * @desc 工单任务运行状态
 */
@AllArgsConstructor
@Getter
public enum IdentyStatus {
    dispatch("01","已派发","01"),
    reply("02","已回复","04"),
    statement("03","已归档","05"),
    withdraw("04","已撤单","03"),
    reprocess("05","再处理","01"),
    urge("06","已催办","01"),
    reject("07","驳回","01"),
    return_("08","已驳回","01"),
    replyEor("100","回复异常","10"),
    syncData("101","工单同步","10"),
    returnCss("07","已驳回","07"),
    ;
    private final String value;
    private final String label;
    private final String relaState;

    public static IdentyStatus getIdentyStatus(String value){
        for(IdentyStatus vo:IdentyStatus.values() ){
            if(vo.getValue().equals(value)){
                return vo;
            }
        }
        return null;
    }
}
