package com.asiainfo.sound.domain.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.asiainfo.sound.domain.dto.IdentyHeader;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/20 9:33
 * @desc 工单回复发送参数
 */
@Data
public class ReplyCssReq extends IdentyHeader implements Serializable {

    private static final long serialVersionUID = -5734575923445664275L;
    /**
     * 工单处理意见
     */
    @ApiModelProperty(value = "工单处理意见")
    @JSONField(name="HandlingOpinion")
    private String handlingOpinion;
    /**
     * 工单处理人
     */
    @ApiModelProperty(value = "工单处理人")
    @JSONField(name="Handler")
    private String handler;
    /**
     * 处理人联系方式
     */
    @ApiModelProperty(value = "处理人联系方式")
    @JSONField(name="HandlerInfor")
    private String handlerInfor;
    /**
     * 工单处理时间
     */
    @ApiModelProperty(value = "工单处理时间",hidden = true)
    @JSONField(name="HandingTime")
    private String handingTime;
    /**
     * 处理人职级
     */
    @ApiModelProperty(value = "处理人职级")
    @JSONField(name="HandlerRank")
    private String handlerRank;
    /**
     * 处理人员部门
     */
    @ApiModelProperty(value = "处理人员部门")
    @JSONField(name="HandingDepartment")
    private String handingDepartment;
    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    @JSONField(name="AttachList")
    private String attachList;

    @ApiModelProperty(value = "附件实际名称")
    @JSONField(name="AttachNameList")
    private String attachNameList;

    @ApiModelProperty(value = "发起省专")
    @JSONField(name="LaunchCompany")
    private String launchCompany;

    @ApiModelProperty(value = "转发省专")
    @JSONField(name="ForwardCompany")
    private String forwardCompany;
}
