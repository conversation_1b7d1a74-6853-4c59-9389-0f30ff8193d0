package com.asiainfo.sound.domain.req;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/3
 **/
@Data
public class TrackinforIdentyLog {

    @ApiModelProperty(value = "接口环节类型")
    @JSONField(name = "InterfaceLink")
    private String interfaceLink;

    @ApiModelProperty(value = "轨迹类型")
    @JSONField(name = "TrackType")
    private String trackType;

    @ApiModelProperty(value = "处理人领导级别")
    @JSONField(name = "HandlerLeadLevel")
    private String handlerLeadLevel;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    @JSONField(name = "HandingTime")
    private String handingTime;

    /**
     * 当前环节处理部门
     */
    @ApiModelProperty(value = "处理部门")
    @JSONField(name = "HandingDepartment")
    private String handingDepartment;

    /**
     * 工单处理人
     */
    @ApiModelProperty(value = "工单处理人")
    @JSONField(name = "Handler")
    private String handler;

    /**
     * 工单处理人联系方式
     */
    @ApiModelProperty(value = "工单处理人联系方式")
    @JSONField(name = "HandlerContactInfor")
    private String handlerContactInfor;

    /**
     * 工单处理意见
     */
    @ApiModelProperty(value = "工单处理意见")
    @JSONField(name = "HandingOpinions")
    private String handingOpinions;

    /**
     * 处理人职级
     *
     */
    @JSONField(name = "HandlerRank")
    private String handlerRank;

    @ApiModelProperty(value = "附件")
    @JSONField(name="AttachList")
    private String attachList;

    @ApiModelProperty(value = "附件实际名称")
    @JSONField(name="AttachNameList")
    private String attachNameList;

}
