package com.asiainfo.sound.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.asiainfo.sound.domain.req.ParaList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/2 14:50
 * @desc 工单公共信息
 */
@Data
public class IdentyHeader {

    @ApiModelProperty(value = "工单状态")
    private String identyState;

    @ApiModelProperty(value = "工单编号")
    @JSONField(name="Identifier")
    private String identifier;

    @ApiModelProperty(value = "请求参数列表")
    @JSONField(name="ParaList")
    private List<ParaList> paraList;
}
