package com.asiainfo.sound.domain.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @desc:
 * @author: zhengxy
 * @create: 2021/12/22 11:04
 */
@Data
public class IdentyLogPo {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "工单单号")
    private String identifier;

    @ApiModelProperty(value = "工单子类型")
    private String identySubtype;

    @ApiModelProperty(value = "日期")
    private String identyDate;

    @ApiModelProperty(value = "具体时间")
    private String identyTime;

    @ApiModelProperty(value = "报文")
    private String identyMsg;

    @ApiModelProperty(value = "工单任务运行状态")
    private String identyStatus;

}
