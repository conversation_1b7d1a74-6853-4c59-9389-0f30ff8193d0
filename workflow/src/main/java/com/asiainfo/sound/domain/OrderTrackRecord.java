package com.asiainfo.sound.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单轨迹记录
 * @TableName order_track_record
 */
@TableName(value ="order_track_record")
@Data
@Accessors(chain = true)
public class OrderTrackRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 工单编号
     */
    @TableField(value = "identifier")
    private String identifier;

    /**
     * 流程实例ID
     */
    @TableField(value = "process_instance_id")
    private String processInstanceId;

    /**
     * 任务ID
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 接口环节类型 01：工单派发环节 02：工单处理环节
     */
    @TableField(value = "interface_link")
    private String interfaceLink;

    /**
     * 轨迹类型 01：流转环节 02：审核环节 03：其他环节
     */
    @TableField(value = "track_type")
    private String trackType;

    /**
     * 处理时间 YYYYMMDDHHMMSS
     */
    @TableField(value = "handing_time")
    private String handingTime;

    /**
     * 当前环节处理部门
     */
    @TableField(value = "handing_department")
    private String handingDepartment;

    /**
     * 工单处理人
     */
    @TableField(value = "handler")
    private String handler;

    /**
     * 处理人领导级别 01：省专公司领导 02：二级领导 03：三级领导
     */
    @TableField(value = "handler_lead_level")
    private String handlerLeadLevel;

    /**
     * 处理人职级
     */
    @TableField(value = "handler_rank")
    private String handlerRank;

    /**
     * 工单处理人联系方式
     */
    @TableField(value = "handler_contact_infor")
    private String handlerContactInfor;

    /**
     * 工单处理意见
     */
    @TableField(value = "handing_opinions")
    private String handingOpinions;

    /**
     * 附件实际名称用于显示展示，多个附件传输用“|”隔开
     */
    @TableField(value = "attach_name_list")
    private String attachNameList;

    /**
     * 多个附件传输用“|”隔开。
     */
    @TableField(value = "attach_list")
    private String attachList;

    /**
     * 附件文件名称，多个附件用,号隔开，对应order_track_attach表数据
     */
    @TableField(value = "attach_file_name")
    private String attachFileName;

    /**
     * 是否是回复 0：轨迹 1：回复
     */
    @TableField(value = "is_reply")
    private Integer isReply;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}