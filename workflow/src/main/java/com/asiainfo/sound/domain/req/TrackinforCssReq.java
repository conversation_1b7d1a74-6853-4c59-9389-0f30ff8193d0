package com.asiainfo.sound.domain.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.asiainfo.sound.domain.dto.IdentyHeader;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 轨迹信息上报
 *
 * <AUTHOR>
 * @date 2022/11/3
 **/
@Data
public class TrackinforCssReq extends IdentyHeader {

    @ApiModelProperty(value = "发起省专")
    @JSONField(name = "LaunchCompany")
    private String launchCompany;

    @ApiModelProperty(value = "转发省专")
    @JSONField(name = "ForwardCompany")
    private String forwardCompany;

    @ApiModelProperty(value = "转发省专")
    @JSONField(name = "ExtIdentylogList")
    private List<TrackinforIdentyLog> extIdentylogList;


}
