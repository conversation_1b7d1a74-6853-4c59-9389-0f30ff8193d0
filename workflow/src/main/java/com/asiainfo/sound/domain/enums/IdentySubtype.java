package com.asiainfo.sound.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/27 14:10
 * @desc 工单子类
 */
@AllArgsConstructor
@Getter
public enum IdentySubtype {
    warning_00("0000","通用版预警单","00","预警单",0),
    warning_01("0001","重点政策投诉预测预警单","00","预警单",-1),
    warning_02("0002","重点产品投诉预测预警单","00","预警单",-1),
    warning_03("0003","投诉热点预测预警单","00","预警单",-1),
    warning_04("0004","不满客户预测预警单","00","预警单",0),
    warning_05("0005","不知情定制行为预测预警单","00","预警单",0),
    warning_06("0006","产品洞察预警单","00","预警单",-1),
    warning_07("0007","触点感知洞察预警单","00","预警单",-1),
    warning_08("0008","服务质量标准预警单","00","预警单",0),

    warning_10("0010","升级投诉预警单","00","预警单",-1),
    warning_11("0011","整改工作申诉预警单","00","预警单",0),
    warning_12("0012","整改工作投诉预警单","00","预警单",0),
    warning_13("0013","营业厅未解决客户问题预警单","00","预警单",0),
    warning_14("0014","不合理设限强制挽留预警单","00","预警单",0),
    warning_15("0015","订购质疑潜在侵权风险预警单","00","预警单",0),
    warning_16("0016","投诉治理监测预警单","00","预警单",0),
    warning_17("0017","用后即评预警单","00","预警单",0),
    warning_18("0018","移动网络感知不佳预警单","00","预警单",0),
    warning_19("0019","业务指标异常预警单","00","预警单",0),
    warning_20("0020","服务响应工单预警单","00","预警单",0),
    warning_25("0025","服务质量标准数据质量预警单","00","预警单",0),
    warning_23("0023","移动云低满客户修复预警单","00","预警单",0),
    warning_24("0024","智能研判预警单","00","预警单",0),
    warning_51("0051","端到端预警单","00","预警单",0),
    warning_52("0052","通报预警单","00","预警单",0),
    warning_53("0053","不知情定制通报预警单","00","预警单",-1),
    warning_93("0093","整体申告预警单","00","预警单",-1),
    warning_94("0094","重点产品投诉预警单","00","预警单",-1),
    warning_95("0095","重点政策投诉预警单","00","预警单",-1),
    warning_96("0096","升级投诉指标预警单","00","预警单",-1),
    warning_97("0097","家宽省内预警单","00","预警单",-1),
    warning_98("0098","通用版预警单(省内)","00","预警单",-1),
    warning_99("0099","两类差错或反悔办理焦点投诉预警单","00","预警单",-1),
    handle_00("0100","通用版督办单","01","督办单",-1),
    handle_01("0101","重点政策投诉预警督办单","01","督办单",-1),
//    handle_02("0102","省内督办单","01","督办单",0),
    handle_02("0102","全网升级投诉中心督办单","01","督办单",-1),
    handle_51("0151","端到端溯源督办单","01","督办单",0),
    handle_99("0199","通用版溯源督办单(省内)","01","督办单",-1),
    handle_09("0109","业务订购争议工单处理质量督办单","01","督办单",-1),


    task_00("0200","通用版任务单","02","任务单",-1),
    // 0297 是应对审计 造的没用的工单
    task_97("0297","四级管控约谈单","02","任务单",0),
    // 0298 是应对审计 造的没用的工单
    task_98("0298","四级管控考核单","02","任务单",0),
    task_99("0299","通用版任务单(省内)","02","任务单",0),
    task_01("0201","不知情定制任务单","02","任务单",-1),
    task_02("0202","不满客户预测任务单","02","任务单",0),
    task_04("0204","站店听音申请任务单","02","任务单",0),
    task_05("0205","站店听音建议任务单","02","任务单",0),
    task_06("0206","投诉焦点治理任务单","02","任务单",0),
    task_09("0209","不知情定制投诉任务单","02","任务单",-1),
    task_10("0208","业务订购争议判责确认任务单","02","任务单",0),
    task_11("0211","总部重点案例任务单","02","任务单",-1),
    task_12("0212","投诉内部升级任务单","02","任务单",-1),
    task_17("0217","移动云客户绿色关怀任务单","02","任务单",0),

    apply_01("0301","省专公司申请单","03","申请单",0),
    apply_02("0302","一线服务之声申请单","03","申请单",0),
    apply_05("0305","服务号码申请单","03","申请单",0),
    apply_70("7560","主动关怀发短信申请单","03","申请单",-1),
    apply_71("7561","互动调研短信问卷申请单","03","申请单",-1),
    apply_72("7562","服务号码申请单(省内)","03","申请单",0),
    repair_01("8660","不满客户修复单","04","客户修复单",0),
    repair_02("8670","不满客户修复单(领导)","04","客户修复单",0),
    repair_99("0499","重保客户修复单","04","客户修复单",-1),
    control_01("7001","约谈管控单","70","管控单",0),
    control_02("7002","考核管控单","70","管控单",0);

    private final String value;
    private final String label;
    private final String superValue;
    private final String superLabel;
    /**
     * 集团 1 省份0
     */
    private final Integer senderFlag;


    public static IdentySubtype getIdentySubtype(String value){
        for(IdentySubtype vo:IdentySubtype.values() ){
            if(vo.getValue().equals(value)){
                return vo;
            }
        }
        return null;
    }
}
