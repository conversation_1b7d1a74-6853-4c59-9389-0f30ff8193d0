package com.asiainfo.sound.domain.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工单轨迹信息上报信息响应
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Data
public class TrackinforInfoResp {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工单编号")
    private String identifier;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "接口环节类型 01：工单派发环节 02：工单处理环节")
    private String interfaceLink;

    @ApiModelProperty(value = "轨迹类型 01：流转环节 02：审核环节 03：其他环节")
    private String trackType;

    @ApiModelProperty(value = "处理时间 YYYYMMDDHHMMSS")
    private String handingTime;

    @ApiModelProperty(value = "格式化的处理时间")
    private String formattedHandingTime;

    @ApiModelProperty(value = "当前环节处理部门")
    private String handingDepartment;

    @ApiModelProperty(value = "工单处理人")
    private String handler;

    @ApiModelProperty(value = "处理人领导级别 01：省专公司领导 02：二级领导 03：三级领导")
    private String handlerLeadLevel;

    @ApiModelProperty(value = "处理人职级")
    private String handlerRank;

    @ApiModelProperty(value = "工单处理人联系方式")
    private String handlerContactInfor;

    @ApiModelProperty(value = "工单处理意见")
    private String handingOpinions;

    @ApiModelProperty(value = "附件实际名称列表")
    private List<String> attachNameList;

    @ApiModelProperty(value = "附件列表")
    private List<String> attachList;

    @ApiModelProperty(value = "附件文件名称列表")
    private List<String> attachFileNameList;

    @ApiModelProperty(value = "是否是回复 0：轨迹 1：回复")
    private Integer isReply;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "轨迹类型描述")
    private String trackTypeDesc;

    @ApiModelProperty(value = "接口环节类型描述")
    private String interfaceLinkDesc;

    @ApiModelProperty(value = "处理人领导级别描述")
    private String handlerLeadLevelDesc;

    @ApiModelProperty(value = "是否包含附件")
    private Boolean hasAttachment;
}
