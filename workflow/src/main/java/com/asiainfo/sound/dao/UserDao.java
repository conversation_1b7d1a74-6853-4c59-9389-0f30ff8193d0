package com.asiainfo.sound.dao;

import com.ai.srd.bd.common.annotation.DataSource;
import com.ai.srd.bd.common.enums.DataSourceType;
import com.asiainfo.sound.domain.dto.FlowSiteUserDto;
import com.asiainfo.sound.domain.dto.UserDto;
import com.asiainfo.sound.domain.dto.UserRoleDto;
import com.asiainfo.sound.domain.entity.LkgStaff;
import com.asiainfo.sound.domain.query.UserRoleQuery;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.vo.DepartmentVo;
import com.asiainfo.sound.domain.vo.SelectBean;
import com.asiainfo.sound.domain.vo.SelectRootBean;
import com.asiainfo.sound.domain.vo.UserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@DataSource(DataSourceType.SLAVE)
public interface UserDao extends BaseMapper<LkgStaff> {

    List<UserVo> selectUserList(UserDto userDto);

    List<DepartmentVo> selectDepartment();

    /**
     * 查询操作人员信息
     * @param query
     * @return
     */
    ReplyCssReq selectUserHandlerInfo(UserTokenQuery query);

    /**
     * 查询所有用户信息
     * @return
     */
    List<SelectBean> selectAllUserInfo();

    /**
     * 查询用户角色信息
     * @param query
     * @return
     */
    List<UserRoleDto> selectUserRoleInfos(UserTokenQuery query);

    UserVo selectUserByUserId(@Param("userId") String userId);

    /**
     * 根据角色名查询用户
     * @param roleName
     * @return
     */
    List<FlowSiteUserDto> selectUserInfoByRoleName(@Param("roleType")String roleType,@Param("roleName")String roleName);

    /**
     * 查询用户组对应的用户
     * @param query
     * @return
     */
    List<SelectBean> selectUserListByRoleId(UserRoleQuery query);

    void deleteIdentyUserRoleByRoleId(UserRoleQuery query);

    /**
     * 新增工单用户处理
     * @param query
     * @return
     */
    Integer insertIdentyUserRoleRelation(UserRoleQuery query);

    /**
     * 根据父级查询子部门
     * @return
     */
    List<SelectRootBean> queryAllDepartment();

    /**
     * 查询部门下用户
     * @param userDto
     * @return
     */
    List<SelectBean> queryDepartmentUsers(UserDto userDto);
    String queryDepartmentName(@Param("deptId") String dept);

    String selectGridUserInfo(@Param("name") String gridManager,@Param("phone") String gridPhone);
    String selectHomeWideWarnUserInfo(@Param("phone") String gridPhone);

    List<SelectBean> queryUsersByPostAndOrg(@Param("postNameList") List<String> postNameList,@Param("orgName") String orgName);
    String selectOrgName(@Param("loginName") String loginName);

    List<Map<String,String>> selectCityByGrid(@Param("gridList") List<String> gridList);

    List<Map<String,String>> selectOrgInfo(@Param("loginNameList") List<String> loginNameList);
}
