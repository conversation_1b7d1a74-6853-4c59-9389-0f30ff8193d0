package com.asiainfo.sound.dao;

import com.ai.srd.bd.common.annotation.DataSource;
import com.ai.srd.bd.common.enums.DataSourceType;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_track_record(工单轨迹记录)】的数据库操作Mapper
* @createDate 2023-02-17 11:21:21
* @Entity generator.domain.OrderTrackRecord
*/
@Repository
@DataSource(DataSourceType.MASTER)
public interface OrderTrackRecordMapper extends BaseMapper<OrderTrackRecord> {

	List<OrderTrackRecord> queryByTaskId(@Param("taskId") String taskId);
    /**
     * 获取当前工单所有的工单轨迹
     *
     * @param identifier
     * @return
     */
    List<OrderTrackRecord> getOrderTrackRecordListByIdentifier(String identifier);

    /**
     * 获取当前工单带附件的所有工单轨迹
     *
     * @param identifier
     * @return
     */
    List<OrderTrackRecord> getExistsAttachTrackListByIdentifier(String identifier);

    int updateAttachListById(@Param("orderTrackRecordId") Long orderTrackRecordId,
                             @Param("attachList") String attachList,
                             @Param("groupRealAttachNameList") String groupRealAttachNameList);

    int getExistsAttachTrackCountByIdentifier(String identifier);

    OrderTrackRecord getLastByIdentifier(String identifier);

    int updateBasicInfoReplyContent(@Param("identifier") String identifier,
                                   @Param("replyJson") String replyJson);

    void saveMessageLog(@Param("mobile")String mobile,
                        @Param("message")String message,
                        @Param("sysTime")String sysTime,
                        @Param("identifier")String identifier,
                        @Param("loginName")String loginName,
                        @Param("type")int type);

    int getIdentifierCount(@Param("identifier")String identifier);

    /**
     * 获取当前节点的轨迹记录
     *
     * @param identifier 工单编号
     * @param taskName 任务名称
     * @param taskId 任务ID
     * @return
     */
    List<OrderTrackRecord> getCurrentNodeTrackRecords(@Param("identifier") String identifier,
                                                     @Param("taskName") String taskName,
                                                     @Param("taskId") String taskId);

    /**
     * 根据条件查询工单轨迹记录
     *
     * @param identifier 工单编号
     * @param trackType 轨迹类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<OrderTrackRecord> getOrderTrackRecordsByCondition(@Param("identifier") String identifier,
                                                          @Param("trackType") String trackType,
                                                          @Param("startTime") String startTime,
                                                          @Param("endTime") String endTime);

    /**
     * 新增工单轨迹记录
     *
     * @param record 轨迹记录
     * @return 影响行数
     */
    int insertOrderTrackRecord(OrderTrackRecord record);

    /**
     * 根据ID修改工单轨迹记录
     *
     * @param record 轨迹记录
     * @return 影响行数
     */
    int updateOrderTrackRecordById(OrderTrackRecord record);

    /**
     * 根据ID删除工单轨迹记录
     *
     * @param id 轨迹记录ID
     * @return 影响行数
     */
    int deleteOrderTrackRecordById(@Param("id") Long id);

    /**
     * 根据工单编号删除轨迹记录
     *
     * @param identifier 工单编号
     * @return 影响行数
     */
    int deleteOrderTrackRecordByIdentifier(@Param("identifier") String identifier);
}




