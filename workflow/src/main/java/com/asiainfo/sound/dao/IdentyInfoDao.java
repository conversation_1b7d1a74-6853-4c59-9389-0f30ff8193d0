package com.asiainfo.sound.dao;

import com.ai.srd.bd.common.annotation.DataSource;
import com.ai.srd.bd.common.enums.DataSourceType;
import com.asiainfo.sound.domain.dto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @desc:
 * @author: zhengxy
 * @create: 2022/3/17 10:14
 */
@Repository
@DataSource(DataSourceType.MASTER)
public interface IdentyInfoDao {
    @DataSource(DataSourceType.SLAVE)
    void insertIdentyBasicInfo(DispatchCssDto dto);

    /**
     * 更新任务状态
     * @param identyHeader
     */
    @DataSource(DataSourceType.SLAVE)
    void modifyIdentyBasicState(IdentyHeader identyHeader);

    /**
     * 给大音平台插入代办任务
     * @param dto
     */
    @DataSource(DataSourceType.SLAVE)
    void insertDyPortalDealTask(PortalDealTaskDto dto);

    /**
     * 取消代办任务
     * @param dto
     */
    @DataSource(DataSourceType.SLAVE)
    void modifyDyPortalDealTask(PortalDealTaskDto dto);

    /**
     * 删除工单记录
     * @param identyHeader
     */
    @DataSource(DataSourceType.SLAVE)
    void deleteIdentyBasicInfo(IdentyHeader identyHeader);

    /**
     * 获取工单zi类
     * @param identifier
     * @return
     */
    @DataSource(DataSourceType.SLAVE)
    String getIdentySubType(String identifier);

    /**
     * 插入一线声音回传集团操作类型
     * @param identifier
     * @param operationType
     */
	void insertGroupHandleType(@Param("identifier") String identifier,@Param("operationType") String operationType);

    /**
     * 获取工单细类
     * @param identifier
     * @return
     */
    String getIdentyDetail(String identifier);

    /**
     * 插入转发的省专
     * @param identifier
     * @param forwardCompany
     */
    void insertFowardCompany(@Param("id") String identifier,@Param("forwardCompany") String forwardCompany);

    /**
     * 查询转发的省专
     * @param identifier
     * @return
     */
    String getFowardCompany(String identifier);

    /**
     * 更新状态为轨迹上传192
     * @param identifier
     */
    void updateTrackStatus(@Param("identifier")String identifier);

    /**
     * 将文件名插入到记录表
     * @param fileName
     * @param newFileName
     */
	void insertOriginFileNameAndNew(@Param("fileName") String fileName, @Param("newFileName") String newFileName);

    /**
     * 将工单号更新到表中
     * @param identifier
     * @param attachList
     */
    void updateTrackInfo(@Param("identifier")String identifier, @Param("fileName") String attachList);

    List<Map<String,String>> getVariableByIdentifier(@Param("identifierList")List<String> identifierList,@Param("varList")List<String> varList);
    List<Map<String,String>> getHisVariableByIdentifier(@Param("identifierList")List<String> identifierList,@Param("varList")List<String> varList);

    List<TwoTypeErrorDto> getTwoTypeErrorIdenty(@Param("time")String time,@Param("startTime")String startTime);

    List<WorkflowStatusDto> queryWorkflowStatusByMonth(@Param("time")String time,@Param("startTime")String startTime);

    List<String> queryUncompleteIdentifier(@Param("time")String time,@Param("identySubtype")String identySubtype,@Param("startTime")String startTime);
    List<String> queryUncompleteAssignee(@Param("identifierList")List identifierList);
}
