package com.asiainfo.sound.util;

import com.asiainfo.sound.domain.enums.UnitCode;

/**
 * <AUTHOR>
 * @date 2021/10/25 10:04
 * @desc 大音平台常量类
 */
public  final class SoundConstant {

    /**
     * 大音平台系统编码
     */
    public final static String USER_PARTY_ID = "CSVB9510";
    /**
     * 签名算法
     */
    public final static String APP_SECRET = "qSi4hwZdg7";

    /**
     * 外部系统
     */
    public final static Integer OTHER_SYSTEM = 2;

    /**
     * 校验文件的内容
     */
    public final static String CHECK_LAYOUT = "1005_";

    /**
     * sign加密算法
     */
    public final static String SIGN_METHOD = "md5";
    /**
     * 机构DOMAIN
     */
    public static final String DOMAIN = "CSVC";
    /**
     * 转换站点
     */
    public static final String SWITCH_SITE = "998";

    /**
     * 省份代码
     */
    public final static String PROVINCE_CODE = UnitCode.ningxia.getValue();

    /**
     * 集团大音平台
     */
    public final static String GROUP_SOUND = "0057";

    /**
     * 评论类型
     */
    public static final String USER_COMMENT = "comment";

    /**
     * 集团编码
     */
    public final static String JITUAN_CODE = "998";

    /**
     * 大音平台编码
     */
    public final static String SOUND_CODE = "CSVC";

    /**
     * 版本
     */
    public final static String VERSION = "1.0.1";

    public final static String DEPARTMENT = "中国移动通信集团宁夏有限公司/网络部";

    public final static String HAND_RANK = "04";

    public final static String GROUP_IDENTY = "集团服务工单处理\\";
    /**
     * 是
     */
    public final static Integer OK = 1;
    /**
     * 否
     */
    public final static Integer NOT = 0;

    public final static String PROVINCE_USER = "jindongxun";
    /**
     * 传递集团文件前缀
     */
    public final static String FILE_PRE = "GZLSEND_";

    /**
     * 宁夏省创建文件前缀
     */
    public final static String PROVINCE_FILE_PRE = "GZLCRETA_";
    /**
     * 门户创建
     */
    public final static String PORTAL_FILE_PRE = "PORTAL_";

    public final static String ATTACH_SUB = "_";

    /**
     * 集团下发再处理与归档的标志
     */
    public final static String RESTART_FLAG = "restartFlag";

    /**
     * 回复标识
     */
    public final static String REPLY_FLAG = "replyFlag";

    /**
     * 回复流程标记
     */
    public final static String GROUP_REPLY_FLAG = "waiting_for_reply";

    /**
     * 催办标志
     */
    public final static String URGE_FLAG = "urgeFlag";

    /**
     * 撤单标志
     */
    public final static String WITHDRAW_FLAG = "403";

    public final static String PASS_FLAG = "passFlag";

    public final static String USER_LIST_STR = "userNameList";

    /**
     * 附件文件命名格式:0001_
     */
    public final static String ATTACH_LAYOUT = "1001_";

    /**
     * 大音平台工单对账明细文件数据格式:0002_
     */
    public final static String IDENTY_LAYOUT = "1002_";


    /**
     * 发起省专launchCompany
     */
    public final static String LAUNCH_COMPANY = "launchCompany";

    /**
     * 转发省专forwardCompany
     */
    public final static String FORWARD_COMPANY = "forwardCompany";

    /**
     * 转发省专ForwardCompany
     */
    public final static String PARA_LIST = "paraList";

    /**
     * 派发额外字段的前缀
     */
    public final static String DISPATH_PRE = "dispatch";

    /**
     * 连接符
     */
    public final static String JOIN_MARK = "|";

    public final static String JOIN_MARK_DEC = "\\|";

    public final static String GOVERN_MESSAGE = "GovernanceMeasures";

    /**
     * 工单通用接口的标志
     */
    public final static String CURRENCY_FLAG = "currencyFlag";

    public final static String GROUP_REPLY_SUCCESS = "00000";

    public final static String GROUP_MSG_SUCCESS = "成功";

    /**
     * 附件列表选择的id
     */
    public final static String SELECT_ATTACH = "attachSelects";
    /**
     * 手机使用节点的标志
     */
    public final static String PHONE_FLAG = "phoneFlag";

    public final static Integer REPLY_LIMIT_SUM = 3;

    public static final String FORCE_JUMP = "0487F81F-D4CE-4B33-9E8B-2D0995448F77-ALLOW-FORCE-JUMP";

    /**
     * 顶级部门的父级
     */
    public static final String PARENT_ROOT_DEPT = "0";

    /**
     * 回复流程标记
     */
    public final static String GROUP_REPLY_REJECT_FLAG = "replyOrReject";
}
