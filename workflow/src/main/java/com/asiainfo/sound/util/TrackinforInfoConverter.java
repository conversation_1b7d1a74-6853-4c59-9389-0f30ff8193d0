package com.asiainfo.sound.util;

import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.resp.TrackinforInfoResp;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单轨迹信息转换工具类
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
public class TrackinforInfoConverter {

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 转换单个轨迹记录
     *
     * @param record 轨迹记录
     * @return 响应DTO
     */
    public static TrackinforInfoResp convert(OrderTrackRecord record) {
        if (record == null) {
            return null;
        }

        TrackinforInfoResp resp = new TrackinforInfoResp();
        resp.setId(record.getId());
        resp.setIdentifier(record.getIdentifier());
        resp.setProcessInstanceId(record.getProcessInstanceId());
        resp.setTaskId(record.getTaskId());
        resp.setInterfaceLink(record.getInterfaceLink());
        resp.setTrackType(record.getTrackType());
        resp.setHandingTime(record.getHandingTime());
        resp.setHandingDepartment(record.getHandingDepartment());
        resp.setHandler(record.getHandler());
        resp.setHandlerLeadLevel(record.getHandlerLeadLevel());
        resp.setHandlerRank(record.getHandlerRank());
        resp.setHandlerContactInfor(record.getHandlerContactInfor());
        resp.setHandingOpinions(record.getHandingOpinions());
        resp.setIsReply(record.getIsReply());
        resp.setCreateTime(record.getCreateTime());

        // 格式化处理时间
        resp.setFormattedHandingTime(formatHandingTime(record.getHandingTime()));

        // 处理附件信息
        resp.setAttachNameList(parseAttachmentString(record.getAttachNameList()));
        resp.setAttachList(parseAttachmentString(record.getAttachList()));
        resp.setAttachFileNameList(parseAttachmentString(record.getAttachFileName()));
        resp.setHasAttachment(hasAttachment(record));

        // 设置描述信息
        resp.setTrackTypeDesc(getTrackTypeDesc(record.getTrackType()));
        resp.setInterfaceLinkDesc(getInterfaceLinkDesc(record.getInterfaceLink()));
        resp.setHandlerLeadLevelDesc(getHandlerLeadLevelDesc(record.getHandlerLeadLevel()));

        return resp;
    }

    /**
     * 转换轨迹记录列表
     *
     * @param records 轨迹记录列表
     * @return 响应DTO列表
     */
    public static List<TrackinforInfoResp> convertList(List<OrderTrackRecord> records) {
        if (records == null || records.isEmpty()) {
            return new ArrayList<>();
        }
        return records.stream().map(TrackinforInfoConverter::convert).collect(Collectors.toList());
    }

    /**
     * 格式化处理时间
     *
     * @param handingTime 原始时间字符串 YYYYMMDDHHMMSS
     * @return 格式化后的时间字符串 yyyy-MM-dd HH:mm:ss
     */
    private static String formatHandingTime(String handingTime) {
        if (StringUtils.isBlank(handingTime) || handingTime.length() != 14) {
            return handingTime;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(handingTime, INPUT_FORMATTER);
            return dateTime.format(OUTPUT_FORMATTER);
        } catch (Exception e) {
            return handingTime;
        }
    }

    /**
     * 解析附件字符串
     *
     * @param attachmentStr 附件字符串，用|分隔
     * @return 附件列表
     */
    private static List<String> parseAttachmentString(String attachmentStr) {
        if (StringUtils.isBlank(attachmentStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(attachmentStr.split("\\|"))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否包含附件
     *
     * @param record 轨迹记录
     * @return 是否包含附件
     */
    private static Boolean hasAttachment(OrderTrackRecord record) {
        return StringUtils.isNotBlank(record.getAttachNameList()) ||
               StringUtils.isNotBlank(record.getAttachList()) ||
               StringUtils.isNotBlank(record.getAttachFileName());
    }

    /**
     * 获取轨迹类型描述
     *
     * @param trackType 轨迹类型
     * @return 轨迹类型描述
     */
    private static String getTrackTypeDesc(String trackType) {
        if (StringUtils.isBlank(trackType)) {
            return "";
        }
        switch (trackType) {
            case "01":
                return "流转环节";
            case "02":
                return "审核环节";
            case "03":
                return "其他环节";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取接口环节类型描述
     *
     * @param interfaceLink 接口环节类型
     * @return 接口环节类型描述
     */
    private static String getInterfaceLinkDesc(String interfaceLink) {
        if (StringUtils.isBlank(interfaceLink)) {
            return "";
        }
        switch (interfaceLink) {
            case "01":
                return "工单派发环节";
            case "02":
                return "工单处理环节";
            default:
                return "未知环节";
        }
    }

    /**
     * 获取处理人领导级别描述
     *
     * @param handlerLeadLevel 处理人领导级别
     * @return 处理人领导级别描述
     */
    private static String getHandlerLeadLevelDesc(String handlerLeadLevel) {
        if (StringUtils.isBlank(handlerLeadLevel)) {
            return "";
        }
        switch (handlerLeadLevel) {
            case "01":
                return "省专公司领导";
            case "02":
                return "二级领导";
            case "03":
                return "三级领导";
            default:
                return "未知级别";
        }
    }
}
