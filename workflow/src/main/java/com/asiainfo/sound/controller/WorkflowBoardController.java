package com.asiainfo.sound.controller;

import com.ai.srd.bd.common.core.domain.R;
import com.ai.srd.bd.common.utils.file.FileUtils;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.sound.domain.dto.WorkflowDto;
import com.asiainfo.sound.domain.vo.WorkflowCountVo;
import com.asiainfo.sound.service.FileUploadService;
import com.asiainfo.sound.service.IdentyDisplayService;
import com.asiainfo.sound.service.WorkflowBoardService;
import com.asiainfo.sound.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/24 16:58
 */
@Api(tags = "工作流看板接口")
@RestController
@RequestMapping("/workflowBoard")
@Slf4j
public class WorkflowBoardController {

    @Autowired
    private WorkflowBoardService workflowBoardService;
    @Autowired
    private IdentyDisplayService identyDisplayService;
    @Autowired
    private FileUploadService fileUploadService;

    //原来freemark接口名   tWkqGErX
    @ApiOperation("工作流看板")
    @PostMapping("/count/listWorkOrderType")
    public R listWorkOrderType(@RequestBody WorkflowDto workflowDto) {
        Map<String, List<WorkflowCountVo>> result = workflowBoardService.listWorkOrderType(workflowDto);
        return R.success(result);
    }

    //原来freemark接口名   PK42jx1r
    @ApiOperation("工单统计")
    @PostMapping("/count/listWorkOrderSbuType")
    public R listWorkOrderSbuType(@RequestBody WorkflowDto workflowDto) {
        Map<String, List<WorkflowCountVo>> result = workflowBoardService.listWorkOrderSbuType(workflowDto);
        return R.success(result);
    }

    //原来freemark接口名   FNKutfbu
    @ApiOperation("工单分析---折线图")
    @PostMapping("/listAnalyseSbuTypeLine")
    public R listAnalyseSbuTypeLine(@RequestBody WorkflowDto workflowDto) {
        List<Map<String, Object>> result = workflowBoardService.listAnalyseSbuTypeLine(workflowDto);
        return R.success(result);
    }

    //原来freemark接口名   qXDxPiTO
    @ApiOperation("工单分析---柱状图")
    @PostMapping("/listAnalyseSbuTypeColumnar")
    public R listAnalyseSbuTypeColumnar(@RequestBody WorkflowDto workflowDto) {
        List<Map<String, Object>> result = workflowBoardService.listAnalyseSbuTypeColumnar(workflowDto);
        return R.success(result);
    }

    //sound_identy_dim_workflow表数据入库
    @ApiOperation("字典表入数据")
    @GetMapping("/insertDimWorkflow")
    public R insertDimWorkflow() {
        boolean result = workflowBoardService.insertDimWorkflow();
        return R.success(result);
    }

    @PostMapping("queryTwoTypeError")
    public R queryTwoTypeError(@RequestBody JSONObject jso) {
        String time = jso.getString("time");
        Integer pageCurrent = jso.getIntValue("pageCurrent");
        Integer pageSize = jso.getIntValue("pageSize");
        return R.success(identyDisplayService.queryTwoTypeErrorByPage(time,pageCurrent,pageSize));
    }

    @GetMapping("analyse")
    public void analyse(@RequestParam String time,@RequestParam(required = false) String startTime, HttpServletResponse response) {
        response.setContentType("application/force-download");
            response.setHeader("Content-Disposition", "attachment; filename=" + time + ".txt");
        String s = identyDisplayService.analyse(time,startTime);
        try(InputStream inputStream =  new ByteArrayInputStream(s.getBytes())){
            IOUtils.copy(inputStream, response.getOutputStream());
        } catch (IOException e) {
            log.error("生成统计文件出错",e);
        }
    }

    @GetMapping("downloadTwoTypeError")
    public void downloadTwoTypeError(@RequestParam String time, HttpServletResponse response) {
        String fileName = identyDisplayService.downloadTwoTypeError(time);
        response.setContentType("application/force-download");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName));
        try(InputStream inputStream = new FileInputStream(fileName)){
            IOUtils.copy(inputStream, response.getOutputStream());
        } catch (IOException e) {
            log.error("生成两类差错月统计文件出错",e);
        }finally {
            new File(fileName).delete();
        }
    }

    private DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private DateTimeFormatter dayFormatter2 = DateTimeFormatter.ofPattern("yyyyMMdd");

    @GetMapping(value = "/testDownload")
    public void testDownload(@RequestParam(required=false) String date, HttpServletResponse response) {
        try {
            String fileName;
            if (StringUtil.isEmpty(date)) {
                fileName = "log.log";
            }else {
                LocalDate localDate = LocalDate.parse(date,dayFormatter2);
                date = dayFormatter.format(localDate);
                fileName = String.format("log-%s-0.log",date);
            }
            String filePath = "logs/" + fileName;
            response.setContentType("application/octet-stream");
            FileUtils.setAttachmentResponseHeader(response, fileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (IOException e) {
            log.error("testDownload error",e);
        }
    }
}
