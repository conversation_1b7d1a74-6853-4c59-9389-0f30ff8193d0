package com.asiainfo.sound.controller;

import com.alibaba.fastjson.JSONObject;
import com.asiainfo.sound.dao.UserDao;
import com.asiainfo.sound.domain.dto.UserDto;
import com.asiainfo.sound.domain.enums.RetCode;
import com.asiainfo.sound.domain.query.UserRoleQuery;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.vo.*;
import com.asiainfo.sound.service.UserAuthService;
import com.asiainfo.sound.util.CollectionUtil;
import com.asiainfo.sound.util.SoundTokenUtil;
import com.asiainfo.sound.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 用户
 * @date 2021/11/26
 */
@Api(tags = {"用户"})
@RestController
@RequestMapping("user")
public class UserController {

    @Autowired
    UserDao userDao;
    @Autowired
    UserAuthService authService;


    @ApiOperation(value = "获取用户部门",notes = "获取用户部门")
    @PostMapping(value = "/getDepartmentList")
    public List<DepartmentVo> getDepartmentList() {
        List<DepartmentVo> departmentVoList = userDao.selectDepartment();
        return departmentVoList;
    }

    @ApiOperation(value = "获取用户",notes = "获取用户")
    @PostMapping(value = "/getUserList")
    public List<UserVo> getUserList(@RequestBody UserDto userDto) {
        List<UserVo> userVoList = userDao.selectUserList(userDto);
        return userVoList;
    }

    @ApiOperation(value = "通过对接参数获取用户权限",notes = "获取用户token")
    @PostMapping(value = "/getUserLogin")
    @ApiImplicitParams({
            @ApiImplicitParam(name="userInfo",value="大音平台传入的用户信息*",required=true,paramType="query")})
    public BaseResponse<UserLoginVo> getUserLogin(@RequestBody UserTokenQuery query) {
        UserLoginVo loginVo = authService.queryUserLoginByToken(query);
        return BaseResponse.sendResponse(RetCode.SUCCESS,loginVo);
    }


    @ApiOperation(value = "获取用户token:用于获取测试数据",notes = "获取用户token")
    @GetMapping(value = "/getUserToken")
    @ApiImplicitParams({
            @ApiImplicitParam(name="userId",value="用户ID*",required=true,paramType="query")})
    public BaseResponse<String> getUserToken(@RequestBody UserTokenQuery query) {
        String userToken = SoundTokenUtil.getTokenCodeByUser(query.getUserId());
        if(StringUtil.isNotEmpty(userToken)){
            return BaseResponse.sendResponse(RetCode.SUCCESS,userToken);
        }
        return BaseResponse.sendResponse(RetCode.ERROR_ENCRYPT);
    }

    @ApiOperation(value = "根据自定义表单中的用户组查询选择用户",notes = "根据自定义表单中的角色查询选择用户")
    @PostMapping(value = "/getUserByRole")
    @ApiImplicitParams({
            @ApiImplicitParam(name="roleId",value="自定义表单的用户组*",required=true,paramType="query")})
    public BaseResponse<List<SelectBean>> getUserByRole(@RequestBody UserRoleQuery query) {
        List<SelectBean> selectBeans = authService.queryUserListByRoleId(query);
        return BaseResponse.sendResponse(RetCode.SUCCESS,selectBeans);
    }

    @ApiOperation(value = "更新流程操作用户",notes = "更新流程操作用户")
    @PostMapping(value = "/updateFlowHandleUser")
    @ApiImplicitParams({
            @ApiImplicitParam(name="roleId",value="自定义表单的用户组*",required=true,paramType="query"),
            @ApiImplicitParam(name="newUserIds",value="新用户ID，userid1,userid2",required=true,paramType="query")})
    public BaseResponse<List<SelectBean>> updateFlowHandleUser(@RequestBody UserRoleQuery query) {
        Boolean flag = authService.updateFlowHandleUser(query);
        if(flag) {
            return BaseResponse.sendResponse(RetCode.SUCCESS, flag);
        }
        return BaseResponse.sendResponse(RetCode.DATA_ERROR);
    }


    @ApiOperation(value = "获取4A的部门信息",notes = "获取4A的部门信息")
    @PostMapping(value = "/getDepartmentChilds")
    public BaseResponse<List<SelectRootBean>> queryDepartmentChilds() {
        List<SelectRootBean> rootBeans = authService.queryDepartmentChilds();
        if(CollectionUtil.isNotEmpty(rootBeans)){
            return BaseResponse.sendResponse(RetCode.SUCCESS,rootBeans);
        }
        return BaseResponse.sendResponse(RetCode.DATA_ERROR);
    }

    @ApiOperation(value = "获取4A的部门对应人员",notes = "获取4A的部门对应人员")
    @PostMapping(value = "/getDepartmentUsers")
    public BaseResponse<List<SelectBean>> queryDepartmentUsers(@RequestBody UserDto userDto) {
        List<SelectBean> rootBeans = authService.queryDepartmentUsers(userDto);
        if(CollectionUtil.isNotEmpty(rootBeans)){
            return BaseResponse.sendResponse(RetCode.SUCCESS,rootBeans);
        }
        return BaseResponse.sendResponse(RetCode.SUCCESS,new ArrayList<SelectBean>());
    }

    @ApiOperation(value = "工作流-人员下拉选项")
    @PostMapping(value = "/getUsersByPostAndOrg")
    public BaseResponse<List<SelectBean>> queryUsersByPostAndOrg(@RequestBody JSONObject jsonObject) {
        String taskId = jsonObject.getString("context");
        JSONObject params = jsonObject.getJSONObject("params");
        String orgName = params.getString("orgName");
        String postName = params.getString("postName");
        List<SelectBean> rootBeans = authService.queryUsersByPostAndOrg(taskId,orgName,postName);
        if(CollectionUtil.isNotEmpty(rootBeans)){
            return BaseResponse.sendResponse(RetCode.SUCCESS,rootBeans);
        }
        return BaseResponse.sendResponse(RetCode.SUCCESS,new ArrayList<SelectBean>());
    }

    @ApiOperation(value = "整体申告预警单-人员下拉选项")
    @PostMapping(value = "/queryUsersFor0093")
    public BaseResponse<List<SelectBean>> queryUsersFor0093(@RequestBody JSONObject jsonObject) {
        String taskId = jsonObject.getString("context");
        JSONObject params = jsonObject.getJSONObject("params");
        String orgName = params.getString("orgName");
        String postName = params.getString("postName");
        List<SelectBean> rootBeans = authService.queryUsersFor0093(taskId,orgName,postName);
        if(CollectionUtil.isNotEmpty(rootBeans)){
            return BaseResponse.sendResponse(RetCode.SUCCESS,rootBeans);
        }
        return BaseResponse.sendResponse(RetCode.SUCCESS,new ArrayList<SelectBean>());
    }

}
