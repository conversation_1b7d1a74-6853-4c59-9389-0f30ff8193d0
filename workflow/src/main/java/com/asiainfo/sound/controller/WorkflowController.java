package com.asiainfo.sound.controller;

import cn.hutool.core.date.DateUtil;
import com.ai.flow.dto.*;
import com.ai.flow.dto.enums.BooleanFilter;
import com.ai.flow.service.IFlowService;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.dto.TaskDetailDto;
import com.asiainfo.sound.domain.dto.TaskForNXOAPushDel;
import com.asiainfo.sound.domain.entity.LkgStaff;
import com.asiainfo.sound.domain.enums.RetCode;
import com.asiainfo.sound.domain.query.FlowConfigLoadQuery;
import com.asiainfo.sound.domain.req.DispatchCssReq;
import com.asiainfo.sound.domain.req.OtherSystemHeaderReq;
import com.asiainfo.sound.domain.req.QueryIdentifierBy4AReq;
import com.asiainfo.sound.domain.req.TasksReq;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.domain.resp.OtherSystemHeaderResp;
import com.asiainfo.sound.domain.resp.QueryIdentifierBy4AResp;
import com.asiainfo.sound.domain.vo.BaseResponse;
import com.asiainfo.sound.domain.vo.DispatchCssVo;
import com.asiainfo.sound.domain.vo.HistoricTaskInstanceVo;
import com.asiainfo.sound.service.ILkgStaffService;
import com.asiainfo.sound.service.SoundCssService;
import com.asiainfo.sound.service.WorkflowService;
import com.asiainfo.sound.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.io.Util;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.content.api.ContentItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

/**
 * <AUTHOR>
 * 为什么要用BaseResponse来返回数据呢。restful就应该遵循restful规范，直接返回内容。将method和http-status用起来。
 * 现在很多action中都用try-catch将错误信息包装在BaseResponse里面，会多一些和业务无关但代码不太好。加全局异常处理可以缓解但不是最优选择。
 * 但代码风格统一大于外部规范，因此在团队达成共识之前统一风格更有利于团队协作。
 */
@Api(tags = {"工作流列表、详情、受理等操作"})
@RestController
@RequestMapping("workflow")
@Slf4j
public class WorkflowController {
    public static final String ZIP = "zip";
    public static final String USER_COMMENT = "comment";
    public static final String SYSTEM_COMMENT = "system";
    private final IFlowService flowService;

    @Value("${oa.pushUrl}")
    String oaPushUrl;
    @Value("${oa.id}")
    String appId;
    @Value("${oa.pwd}")
    String apppwd;
    @Value("${spring.profiles.active}")
    String profile;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private SoundCssService cssService;

    @Autowired
    private ILkgStaffService staffService;

    public WorkflowController(IFlowService flowService) {
        this.flowService = flowService;
    }

    @ApiOperation(value = "工作流定义列表")
    @GetMapping("process-definition")
    public BaseResponse<List<ProcessDefinition>> getProcessDefinitions() {
        return BaseResponse.sendResponse(RetCode.SUCCESS,
                flowService.getProcessDefinitions(null, null));
    }

    @ApiOperation(value = "endEmos")
    @PostMapping("endEmos")
    public BaseResponse<String> endEmos(@RequestBody OtherSystemHeaderReq headerReq) {
        cssService.endEmos(headerReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "OA代办：从OA进入的代办任务的列表")
    @PostMapping("oapush-task-list")
    public BaseResponse<PageInfo<Task>> getOnapushTasks(@RequestBody(required = true) TasksReq tasksReq,
                                                        @RequestParam(required = true) String taskId,
                                                        @RequestParam(defaultValue = "0", required = true) Integer currentPage,
                                                        @RequestParam(defaultValue = "10", required = true) Integer pageSize) {
        Page<Task> tasks = workflowService.getOnapushTasks(tasksReq, taskId, currentPage, pageSize);
        return BaseResponse.sendResponse(RetCode.SUCCESS, PageInfo.of(tasks));
    }

    @ApiOperation(value = "任务列表：代办")
    @PostMapping("task-list")
    public BaseResponse<PageInfo<Task>> getTasks(@RequestBody(required = true) TasksReq tasksReq,
                                                 @RequestParam(defaultValue = "0", required = true) Integer currentPage,
                                                 @RequestParam(defaultValue = "10", required = true) Integer pageSize) {
        Page<Task> tasks = workflowService.getWaitingTasks(tasksReq, currentPage, pageSize);
        return BaseResponse.sendResponse(RetCode.SUCCESS, PageInfo.of(tasks));
    }

    @ApiOperation(value = "任务列表：全部")
    @PostMapping(value = "process-instance-list")
    public BaseResponse<?> getHistoricProcessInstances(@RequestBody() TasksReq tasksReq,
                                                       @RequestParam(defaultValue = "0") BooleanFilter hasFinished,
                                                       @RequestParam(defaultValue = "0") Integer currentPage,
                                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<HistoricProcessInstance> tasks = workflowService.getHistoricProcessInstances(tasksReq, hasFinished, currentPage, pageSize);
        return BaseResponse.sendResponse(RetCode.SUCCESS, PageInfo.of(tasks));
    }

    @ApiOperation(value = "任务列表：已办")
    @PostMapping(value = "process-instance-list-only-complete")
    public BaseResponse<?> getHistoricProcessInstancesWithCompleteTask(@RequestBody() TasksReq tasksReq,
                                                                       @RequestParam(defaultValue = "0") Integer currentPage,
                                                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<HistoricProcessInstance> tasks = workflowService.getHistoricProcessInstancesWithCompleteTask(tasksReq, currentPage, pageSize);
        return BaseResponse.sendResponse(RetCode.SUCCESS, PageInfo.of(tasks));
    }


    @ApiOperation("受理，获取自定义表单")
    @GetMapping("form-info")
    public BaseResponse<FormInfo> getTaskFormModel(String taskId) {
        try {
            return BaseResponse.sendResponse(RetCode.SUCCESS, flowService.getTaskFormInfo(taskId));
        } catch (FlowableObjectNotFoundException ex) {
            return BaseResponse.sendResponse(RetCode.SUCCESS, null);
        }
    }

    @ApiOperation(value = "详情：工单处理过程，对应详情页面下")
    @PostMapping("historic-task-list")
    public BaseResponse<PageInfo<HistoricTaskInstanceVo>> getHistoricTasks(@RequestBody() TasksReq tasksReq,
                                                                           @RequestParam(defaultValue = "0") Integer currentPage,
                                                                           @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<HistoricTaskInstanceVo> tasks = workflowService.getHistoricTasks(tasksReq, currentPage, pageSize);
        return BaseResponse.sendResponse(RetCode.SUCCESS, PageInfo.of(tasks));
    }

    @ApiOperation(value = "发起流程：前端使用")
    @PostMapping("processByVO")
    public BaseResponse<?> startProcessByVO(String processKey, @RequestBody DispatchCssVo vo) {
        workflowService.startProcessByVO(processKey, vo);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "发起流程：新.前端使用")
    @PostMapping("processNewByVO")
    public BaseResponse<?> processNewByVO(String processKey, @RequestBody Map<String, Object> variables) {
        workflowService.startProcessNewByVO(processKey, variables);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation("发起流程：获取自定义表单")
    @GetMapping("process-start-form-info")
    public BaseResponse<FormInfo> getProcessStartFormInfo(String processKey) {
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, processKey).getId();
        return BaseResponse.sendResponse(RetCode.SUCCESS, flowService.getProcessStartFormInfo(processDefinitionProcessId));
    }

    @ApiOperation("工单流程配置：根据节点的formkey获取表单")
    @GetMapping("process-get-site-form")
    public BaseResponse<FormInfo> getProcessSiteFormInfo(String formKey) {
        FormInfo formInfo = workflowService.getProcessSiteFormInfo(null, formKey);
        return BaseResponse.sendResponse(RetCode.SUCCESS, formInfo);
    }

    @ApiOperation("工单流程配置：获取流程配置的xml")
    @GetMapping("process-get-flow-xml")
    public BaseResponse<String> getProcessFlowXml(String processKey) {
        String xml = workflowService.getProcessFlowXml(processKey);
        return BaseResponse.sendResponse(RetCode.SUCCESS, xml);
    }

    @ApiOperation("工单流程配置：重新更新流程文件")
    @PostMapping("process-load-flow-xml")
    public BaseResponse<String> deployProcessDefinition(@RequestBody FlowConfigLoadQuery query) {
        String result = workflowService.flowableLoadProcessDefinition(query);
        return BaseResponse.sendResponse(RetCode.SUCCESS, result);
    }


    @ApiOperation("详情：获取工单详情的自定义表单")
    @GetMapping("process-identify-detail-form")
    public BaseResponse<FormInfo> getProcessStartInfoByProcessDefinitionProcessId(String processDefinitionProcessId) {
        return BaseResponse.sendResponse(RetCode.SUCCESS, flowService.getProcessStartFormInfo(processDefinitionProcessId));
    }

    @ApiOperation(value = "受理方式：处理")
    @PostMapping("complete")
    public BaseResponse<?> completeProcess(String taskId,
                                           @RequestBody() Map<String, Object> variables, String filePath, String fileName, String remark) {

        workflowService.dealProcess(taskId, variables, filePath, fileName, remark, null);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "受理方式：驳回")
    @PostMapping("reject")
    public BaseResponse<?> rejectProcess(String taskId,
                                         @RequestBody() Map<String, Object> variables, String filePath, String fileName, String remark) {
        workflowService.dealProcess(taskId, variables, filePath, fileName, remark, Integer.valueOf(SoundConstant.NOT));
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @GetMapping("back-list")
    @ApiOperation(value = "回退列表")
    public List<FlowNodeVo> getBackList(String taskId, String processInstanceId) {
        List<FlowNodeVo> backNodesByProcessInstanceId = workflowService.getBackNodes(taskId, processInstanceId);
        return backNodesByProcessInstanceId;
    }

    @PostMapping("back_to_step_task")
    @ApiOperation(value = "回退")
    public void backToStepTask(BackTaskVo backTaskVo) {
        //todo：判断当前节点是否允许回退

        Map<String, Object> taskVariables = flowService.getTaskVariables(backTaskVo.getTaskId(), Arrays.asList(SoundConstant.FORCE_JUMP));
        // 如果存在强制跳转变量并且设置为false，不允许回退
        if (taskVariables.get(SoundConstant.FORCE_JUMP) != null && !"false".equals(taskVariables.get(SoundConstant.FORCE_JUMP))) {
            throw new RuntimeException("不允许回退");
        }

        backTaskVo.setUserCode(AuthHelper.getUserName());
        backTaskVo.setType("comment");
        backTaskVo.setMessage("回退意见：" + backTaskVo.getMessage());
        flowService.backToStepTask(backTaskVo);
    }


    @Transactional
    @ApiOperation(value = "受理方式：转派")
    @PostMapping("task-assignee")
    public BaseResponse<?> setTaskAssignee(String taskId, String newUserName, String remark, String filePath, String fileName) {
        log.info("工单【taskId: {}】由【{}】转派至【{}】", taskId, AuthHelper.getUserName(), newUserName);
        workflowService.setTaskAssignee(taskId, newUserName, remark, filePath, fileName);
        try {
            //当前节点名字
            TaskForNXOAPushDel t = new TaskForNXOAPushDel();
            //组装新增参数
            t.setAppId(appId);
            t.setAppPwd(apppwd);
            t.setType("1");
            //实际消息id
            Task task = flowService.getTaskById(taskId);
            t.setWorkId(task.getId());
            //与上面一样
            t.setInstanceId(task.getId());
            // 发起转派的用户
            LkgStaff one = staffService.getOne(AuthHelper.getUserName());
            if (one != null) {
                t.setUserId(one.getOaId());
            }
            ObjectMapper objectMapper = new ObjectMapper();
            String paramJson = objectMapper.writeValueAsString(t);
            String pushOanewUrl = oaPushUrl + "/unionmessageservice/view/deleteMessage";
            log.info("【转派】推送删除的oa-id:" + t.getUserId() + ",调用oa接口,推送工单删除信息：" + pushOanewUrl + ",参数: " + paramJson);
            if ("prod".equals(profile)) {
                if ("1".equals(staffService.getMessageSendFlag())) {
                    String oaPushNewResult = RestPostRequest.postRequest(paramJson, pushOanewUrl);
                    log.info("OA--删除返回：" + oaPushNewResult);
                }
            } else {
                log.info("》》》 非prod环境，删除不推送,跳过。");
            }
        } catch (Exception e) {
            log.error("推送删除oa时报错", e);
        }
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "详情：获取评论列表和附件列表，点击详情中列表")
    @GetMapping("task-detail")
    public BaseResponse<?> getCommentsAndAttachments(String taskId) {
        FormInfo formInfo = flowService.getTaskFormInfo(taskId);
        List<Comment> taskComments = flowService.getTaskComments(taskId, USER_COMMENT);
        List<Comment> systemComments = flowService.getTaskComments(taskId, SYSTEM_COMMENT);
        List<OrderTrackRecord> orderTrackRecords = workflowService.queryNodeAttachList(taskId);
        List<Attachment> attachments = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderTrackRecords)) {
            String processInstanceId = orderTrackRecords.get(0).getProcessInstanceId();
            List<ContentItem> contents = flowService.getContents(null, processInstanceId, taskId);
            attachments = workflowService.convertValue(contents);
        } else {
            attachments = flowService.getAttachments(taskId);
        }
        return BaseResponse.sendResponse(RetCode.SUCCESS, new TaskDetailDto(taskComments, systemComments, attachments, formInfo));
    }

    @GetMapping("attachment")
    @ApiOperation(value = "暂未使用：附件内容")
    public void downloadAttachment(HttpServletResponse response, String attachmentId, String fileName) throws IOException {
        InputStream attachmentContent = flowService.getContent(attachmentId);
        if (attachmentContent != null) {
            response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName) + "\"");
            Util.copyStream(attachmentContent, response.getOutputStream());
        } else {
            //该部分为以前的附件下载逻辑，为了兼容之前的文件数据，若不需要则直接返回404
            attachmentContent = flowService.getAttachmentContent(attachmentId);
            if (attachmentContent != null) {
                response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName) + "\"");
                Util.copyStream(attachmentContent, response.getOutputStream());
            } else {
                response.setStatus(404);
            }
        }
    }

    @GetMapping("image")
    @ApiOperation(value = "详情：流程图")
    public void getProcessImage(HttpServletResponse response, String processInstanceId) throws IOException {
        response.setContentType("image/png");
        log.info("defaultCharset={}", Charset.defaultCharset());
        flowService.getImageActivityDiagram(processInstanceId, response.getOutputStream());
    }

    @ApiOperation(value = "暂未使用：创建工单任务")
    @PostMapping("createIdentyTask")
    public BaseResponse<?> createIdentyTask(@RequestBody DispatchCssReq cssReq) {
        workflowService.createIdentyTask(cssReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @GetMapping("history_detail")
    @ApiOperation(value = "历史处理的详情")
    public List<HistoricDetail> getHistoryDetail(String processInstanceId) {
        return flowService.getHistoricDetails(processInstanceId);
    }

    @GetMapping("all-attachments")
    @ApiOperation(value = "获取流程所有附件")
    public List<Attachment> getAllAttachments(String processInstanceId) {
        return flowService.getProcessAttachments(processInstanceId);
    }

    //朱尧-外部系统调用创建工单，后面做了首写字母转小写。
    @ApiOperation(value = "接收外部系统发起流程", notes = "接收外部系统发起流程")
    @PostMapping(value = "/DispatchCSSFromOtherSystem")
    public HeaderResp DispatchCSSFromOtherSystem(@RequestBody OtherSystemHeaderReq cssReq) {
        String content = cssReq.getContent();
        Claims decode = JwtUtils.decode(content);
        log.info("/DispatchCSSFromOtherSystem收到参数，content：" + content);
        String value = (String) decode.get("key");
        log.info("/DispatchCSSFromOtherSystem解密，content：" + value);
        cssReq.setContent(value);
        HeaderResp headerResp = cssService.otherSystemSendDispatchTo(cssReq);
        return headerResp;
    }


    @ApiOperation(value = "外部系统工单查询", notes = "外部系统工单查询")
    @PostMapping(value = "/replyToOtherSystem")
    public OtherSystemHeaderResp replyToOtherSystem(@RequestBody OtherSystemHeaderReq cssReq) {
        String content = cssReq.getContent();
        Claims decode = JwtUtils.decode(content);
        log.info("/replyToOtherSystem收到参数，content：" + content);
        String value = (String) decode.get("key");
        log.info("/replyToOtherSystem解密，content：" + value);
        cssReq.setContent(value);
        OtherSystemHeaderResp headerResp = cssService.replyToOtherSystem(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "通过创建人4A账号查找工单id", notes = "通过创建人4A账号查找工单id")
    @PostMapping(value = "/queryIdentifierBy4AId")
    public BaseResponse<List<QueryIdentifierBy4AResp>> queryIdentifierBy4AId(@RequestBody QueryIdentifierBy4AReq cssReq) {
        String content = cssReq.getContent();
        Claims decode = JwtUtils.decode(content);
        log.info("/queryIdentifierBy4AId收到参数，content：" + content);
        String value = (String) decode.get("key");
        log.info("/queryIdentifierBy4AId解密，content：" + value);
        return BaseResponse.sendResponse(RetCode.SUCCESS, cssService.queryIdentifierBy4AId(value));
    }
}
