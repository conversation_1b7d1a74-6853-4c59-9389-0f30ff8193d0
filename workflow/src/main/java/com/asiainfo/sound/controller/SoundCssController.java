package com.asiainfo.sound.controller;

import com.ai.srd.bd.common.annotation.Log;
import com.ai.srd.bd.common.enums.BusinessType;
import com.asiainfo.sound.domain.req.HeaderReq;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.service.SoundCssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021/10/21 9:42
 * @desc 大音接口调用-集团调用该接口下发工单等操作-之前名字-正式环境需要改回-IdentySync
 */
@Api(tags = {"对接集团：集团——>工作流"})
@RestController
@RequestMapping("IdentySync")
@Slf4j
public class SoundCssController {

    @Autowired
    private SoundCssService cssService;


    @ApiOperation(value = "工单派发",notes = "工单派发")
    @PostMapping(value = "/Dispatch")
    public HeaderResp sendDispatchTo(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/Dispatch");
        HeaderResp headerResp = cssService.sendDispatchTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单回复",notes = "工单回复")
    @PostMapping(value = "/ReplyCSS")
    public HeaderResp replyCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/ReplyCSS");
        HeaderResp headerResp = cssService.sendReplyTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单归档",notes = "工单归档")
    @PostMapping(value = "/StatementCSS")
    public HeaderResp statementTo(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/StatementCSS");
        HeaderResp headerResp = cssService.statementTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单撤单",notes = "工单撤单")
    @PostMapping(value = "/WithdrawCSS")
    public HeaderResp withdrawCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/WithdrawCSS");
        HeaderResp headerResp = cssService.withdrawTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单查询",notes = "工单查询")
    @PostMapping(value = "/QueryCSS")
    public HeaderResp queryCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/QueryCSS");
        HeaderResp headerResp = cssService.queryTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单再处理",notes = "工单再处理")
    @PostMapping(value = "/ReprocessCSS")
    public HeaderResp reprocessCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/ReprocessCSS");
        HeaderResp headerResp = cssService.reprocessTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单催办",notes = "工单催办")
    @PostMapping(value = "/UrgeCSS")
    public HeaderResp urgeCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/UrgeCSS");
        HeaderResp headerResp = cssService.urgeto(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单通用接口",notes = "工单通用接口")
    @PostMapping(value = "/CurrencyCSS")
    public HeaderResp currencyCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/CurrencyCSS");
        HeaderResp headerResp = cssService.currencyTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "测试工单同步删除",notes = "测试工单同步删除")
    @PostMapping(value = "/TestjobCSS")
    public HeaderResp TestjobCSS(@RequestBody HeaderReq cssReq) {
        log.info("调用的:IdentySync/TestjobCSS");
        HeaderResp headerResp = cssService.TestjobTo(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "fortest",notes = "fortest")
    @GetMapping(value = "/fortest")
    public String fortest() {
        log.info("调用的:IdentySync/fortest");
        return "=====";
    }

    /**
     * 当前接口由于网状网申请地址的问题移动到了SoundCssTestController，请在该类中进行修改
     * @param cssReq
     * @return
     */
    @ApiOperation(value = "工单驳回",notes = "工单驳回")
    @PostMapping(value = "/ReturnCSS")
    public HeaderResp returnCSS(@RequestBody HeaderReq cssReq) {
        HeaderResp headerResp = cssService.returnCSS(cssReq);
        return headerResp;
    }

    @ApiOperation(value = "工单信息同步",notes = "工单信息同步")
    @PostMapping(value = "/SyncData")
    public HeaderResp SyncData(@RequestBody HeaderReq cssReq) {
        HeaderResp headerResp = cssService.sync(cssReq);
        return headerResp;
    }
}
