package com.asiainfo.sound.controller;

import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.dto.DispatchCssDto;
import com.asiainfo.sound.domain.dto.SendRequstDto;
import com.asiainfo.sound.domain.enums.RetCode;
import com.asiainfo.sound.domain.po.OperationNodeLog;
import com.asiainfo.sound.domain.query.IdentyPage;
import com.asiainfo.sound.domain.req.DispatchCssReq;
import com.asiainfo.sound.domain.req.QueryReq;
import com.asiainfo.sound.domain.req.TrackinforQueryReq;
import com.asiainfo.sound.domain.resp.TrackinforInfoResp;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.domain.vo.BaseResponse;
import com.asiainfo.sound.service.SoundSendService;
import com.asiainfo.sound.service.WorkOrderTrackinforService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @desc:
 * @author: zhengxy
 * @create: 2022/1/6 14:19
 */
@Api(tags = {"申请单接口"})
@RestController
@RequestMapping("apply")
public class ApplyController {

    @Autowired
    private SoundSendService sendService;

    @Autowired
    private WorkOrderTrackinforService workOrderTrackinforService;

    @ApiOperation(value = "工单创建",notes = "工单创建")
    @PostMapping(value = "/createWork")
    public BaseResponse createWork(@RequestBody DispatchCssReq cssReq) {
        sendService.sendDispatchTo(cssReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "获取我的任务列表",notes = "获取我的任务列表")
    @PostMapping(value = "/getTaskList")
    public BaseResponse<PageInfo<DispatchCssDto>> getTaskList(@RequestBody() IdentyPage identyPage) {
        PageInfo<DispatchCssDto> tasks =sendService.getTaskList(identyPage);
        return BaseResponse.sendResponse(RetCode.SUCCESS, tasks);
    }

    @ApiOperation(value = "获取全部任务列表",notes = "获取全部任务列表")
    @PostMapping(value = "/getTaskListAll")
    public BaseResponse<PageInfo<DispatchCssDto>> getTaskListAll(@RequestBody() IdentyPage identyPage) {
        PageInfo<DispatchCssDto> tasks =sendService.getTaskListAll(identyPage);
        return BaseResponse.sendResponse(RetCode.SUCCESS, tasks);
    }

    @ApiOperation(value = "工单受理",notes = "工单受理")
    @PostMapping(value = "/acceptance")
    public BaseResponse acceptance(@RequestBody() SendRequstDto sendRequstDto) {
        sendService.sendByState(sendRequstDto);
        return BaseResponse.sendResponse(RetCode.SUCCESS);
    }

    @ApiOperation(value = "集团查询",notes = "集团查询")
    @PostMapping(value = "/findGroupWork")
    public BaseResponse findGroupWork(@RequestBody() QueryReq queryReq) {
        HeaderResp headerResp = sendService.sendQueryTo(queryReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS,headerResp.getResult());
    }

    @ApiOperation(value = "节点日志查询",notes = "节点日志查询")
    @PostMapping(value = "/findOperationNodeLog")
    public BaseResponse findOperationNodeLog(@RequestBody() QueryReq queryReq) {
        List<OperationNodeLog> logList = sendService.findOperationNodeLogBy(queryReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS,logList);
    }

    @ApiOperation(value = "查询工单轨迹信息上报信息",notes = "根据工单编号查询工单轨迹信息上报信息")
    @PostMapping(value = "/queryTrackinforInfo")
    public BaseResponse<List<OrderTrackRecord>> queryTrackinforInfo(@RequestBody() TrackinforQueryReq queryReq) {
        List<OrderTrackRecord> trackRecords = workOrderTrackinforService.queryTrackinforInfo(queryReq.getIdentifier());
        return BaseResponse.sendResponse(RetCode.SUCCESS, trackRecords);
    }

    @ApiOperation(value = "根据条件查询工单轨迹信息上报信息",notes = "支持按轨迹类型、时间范围等条件查询工单轨迹信息上报信息")
    @PostMapping(value = "/queryTrackinforInfoByCondition")
    public BaseResponse<List<OrderTrackRecord>> queryTrackinforInfoByCondition(@RequestBody() TrackinforQueryReq queryReq) {
        List<OrderTrackRecord> trackRecords = workOrderTrackinforService.queryTrackinforInfoByCondition(queryReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS, trackRecords);
    }

    @ApiOperation(value = "查询工单轨迹信息上报信息（格式化）",notes = "根据工单编号查询工单轨迹信息上报信息，返回格式化的响应数据")
    @PostMapping(value = "/queryTrackinforInfoFormatted")
    public BaseResponse<List<TrackinforInfoResp>> queryTrackinforInfoFormatted(@RequestBody() TrackinforQueryReq queryReq) {
        List<TrackinforInfoResp> trackRecords = workOrderTrackinforService.queryTrackinforInfoFormatted(queryReq.getIdentifier());
        return BaseResponse.sendResponse(RetCode.SUCCESS, trackRecords);
    }

    @ApiOperation(value = "根据条件查询工单轨迹信息上报信息（格式化）",notes = "支持按轨迹类型、时间范围等条件查询工单轨迹信息上报信息，返回格式化的响应数据")
    @PostMapping(value = "/queryTrackinforInfoByConditionFormatted")
    public BaseResponse<List<TrackinforInfoResp>> queryTrackinforInfoByConditionFormatted(@RequestBody() TrackinforQueryReq queryReq) {
        List<TrackinforInfoResp> trackRecords = workOrderTrackinforService.queryTrackinforInfoByConditionFormatted(queryReq);
        return BaseResponse.sendResponse(RetCode.SUCCESS, trackRecords);
    }

}
