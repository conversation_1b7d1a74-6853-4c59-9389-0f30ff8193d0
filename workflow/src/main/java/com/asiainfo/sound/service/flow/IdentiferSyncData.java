package com.asiainfo.sound.service.flow;

import com.ai.flow.dto.Attachment;
import com.ai.flow.dto.Comment;
import com.ai.flow.dto.FormInfo;
import com.ai.flow.dto.HistoricTaskInstance;
import com.ai.flow.dto.enums.BooleanFilter;
import com.ai.flow.service.IFlowService;
import com.asiainfo.sound.dao.IdentyInfoDao;
import com.asiainfo.sound.dao.OrderTrackRecordMapper;
import com.asiainfo.sound.daoFlow.SoundCssDao;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.enums.IdentySubtype;
import com.asiainfo.sound.domain.enums.SyncDataType;
import com.asiainfo.sound.domain.po.IdentyCommonCodePo;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.ParaList;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.req.SyncDataCssReq;
import com.asiainfo.sound.domain.resp.ExtIdentylogList;
import com.asiainfo.sound.domain.resp.HeaderCodeResp;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.domain.vo.FlowReplyVo;
import com.asiainfo.sound.service.SoundSendService;
import com.asiainfo.sound.service.UserAuthService;
import com.asiainfo.sound.service.WorkflowService;
import com.asiainfo.sound.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.content.api.ContentItem;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.form.model.FormField;
import org.flowable.form.model.SimpleFormModel;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/25 11:08
 * @desc 工单信息同步
 */
@Component
@Slf4j
public class IdentiferSyncData implements JavaDelegate, ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        IdentiferSyncData.applicationContext = applicationContext;
    }



    @SneakyThrows
    @Override
    public void execute(DelegateExecution delegateExecution) {
        IFlowService flowService = applicationContext.getBean(IFlowService.class);
        SoundSendService sendService = applicationContext.getBean(SoundSendService.class);
        SyncDataCssReq syncDataCssReq = getSyncDataCssReq(delegateExecution,flowService,"syncFlag");
        HeaderResp headerResp = sendService.syncDataTo(syncDataCssReq);
        HeaderCodeResp codeResp = headerResp.getResponse();
        if(!SoundConstant.GROUP_REPLY_SUCCESS.equals(codeResp.getRspCode())){
            String excepMsg = "异常码:" + codeResp.getRspCode() + ",异常信息：" + codeResp.getRspDesc();
            throw new Exception(excepMsg);
        }
    }


    //获取处理人信息
    private List<ParaList> getIdentifierHanderInfo(UserAuthService authService){
        List<ParaList> paraLists = new ArrayList<>();
        String userId = AuthHelper.getUserName();
        UserTokenQuery tokenQuery = new UserTokenQuery();
        tokenQuery.setUserId(userId);
        FlowReplyVo flowReplyVo = authService.queryFlowIdentyReplyInfo(tokenQuery);
        //部门查询为空，使用默认部门
        String department = flowReplyVo.getHandingDepartment();
        ParaList handingDepartment = new ParaList("HandingDepartment",StringUtil.isEmpty(department) ? SoundConstant.DEPARTMENT : department);
        paraLists.add(handingDepartment);
        //部门层级为空，为默认部门
        String handleRank = flowReplyVo.getHandlerRank();
        ParaList handlerRank = new ParaList("HandlerRank",StringUtil.isEmpty(handleRank)? SoundConstant.HAND_RANK : handleRank);
        paraLists.add(handlerRank);
        paraLists.add(new ParaList("Handler",flowReplyVo.getHandler()));
        paraLists.add(new ParaList("HandlerInfor",flowReplyVo.getHandlerInfor()));
        paraLists.add(new ParaList("HandingTime", SoundDateUtil.getNowDateTime()));
        return paraLists;
    }

    /**
     * 获取处理意见
     * @param flowService
     * @param processInstanceId
     * @return
     */
    private ParaList getIdentifierHandlingOpinion(IFlowService flowService,String processInstanceId){
        //查询所有任务节点
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(AuthHelper.getUserName(), null,null, processInstanceId,
                null, null, null, 0, 50);
        List<Comment> comments = flowService.getTaskComments(taskList.get(0).getId(),SoundConstant.USER_COMMENT);
        Comment comment = comments.get(0);
        return new ParaList("HandlingOpinion",comment.getMessage());
    }

    /**
     * 获取附件信息
     * @param flowService
     * @param processInstanceId
     * @return
     */
    private ParaList getIdentifierAttachList(IFlowService flowService,String processInstanceId,UserAuthService authService,Object attachSelect,WorkflowService workflowService){
        String userId = AuthHelper.getUserName();
        //查询所有任务节点
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(userId, null,null, processInstanceId,
                null, null, null, 0, 50);
        //获取回复上一步的任务的附件
        List<ContentItem> contents = flowService.getContents(null, null, taskList.get(0).getId());
        List<Attachment> attachments = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(contents)){
            List<Attachment> preAttachments = workflowService.convertValue(contents);
            attachments = mergeIdentifyAttachments(preAttachments,flowService,attachSelect);
        }else {
            attachments = mergeIdentifyAttachments(flowService.getAttachments(taskList.get(0).getId()),flowService,attachSelect);
        }

        UserTokenQuery tokenQuery = new UserTokenQuery();
        tokenQuery.setUserId(userId);
        FlowReplyVo flowReplyVo = authService.queryFlowIdentyReplyInfo(tokenQuery);
        String outputPath = flowReplyVo.getIdentyPath();
        if (CollectionUtil.isNotEmpty(attachments)) {
            String zipName = FileUpload.getMergeAttachmentPackage(attachments, flowService,outputPath);
            if(StringUtil.isEmpty(zipName)){
                return new ParaList("AttachList","");
            }else {
                return new ParaList("AttachList",zipName);
            }
        }
        return new ParaList("AttachList","");
    }

    /**
     * 合并选择的附件与上传的附件
     * @param attachments
     * @return
     */
    private List<Attachment> mergeIdentifyAttachments(List<Attachment> attachments, IFlowService iFlowService,Object attachSelect) {

        if(!(attachSelect instanceof List)){
            return attachments;
        }
        List<String> attachmentIds = (List)attachSelect;
        List<Attachment> selectAttachmentIds = new ArrayList<>(attachments.size());
        for(String attachmentId : attachmentIds){
            InputStream content = iFlowService.getContent(attachmentId);
            if (content == null) {
                selectAttachmentIds.add(iFlowService.getAttachment(attachmentId));
            }else {
                List<ContentItem> contents = iFlowService.getContents(attachmentId, null, null);
                WorkflowService workflowService = applicationContext.getBean(WorkflowService.class);
                List<Attachment> attachments1 = workflowService.convertValue(contents);
                selectAttachmentIds.addAll(attachments1);
            }
        }
        selectAttachmentIds.addAll(attachments);
        return selectAttachmentIds;
    }

    protected SyncDataCssReq getSyncDataCssReq(DelegateExecution delegateExecution,IFlowService flowService,String syncFlagStr){
        UserAuthService authService = applicationContext.getBean(UserAuthService.class);
        WorkflowService workflowService = applicationContext.getBean(WorkflowService.class);
        SoundCssDao soundCssDao = applicationContext.getBean(SoundCssDao.class);
        Map<String,Object> variableMap = delegateExecution.getVariables();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        SyncDataCssReq syncDataCssReq = new SyncDataCssReq();
        //获取公共属性变量
        String identySubtype = String.valueOf(variableMap.get("identySubtype"));
        syncDataCssReq.setIdentySubtype(identySubtype);
        syncDataCssReq.setIdentyType(String.valueOf(variableMap.get("identyType")));
        syncDataCssReq.setIdentifier(String.valueOf(variableMap.get("identifier")));
        syncDataCssReq.setLaunchCompany(String.valueOf(variableMap.get("originUnit")));
        syncDataCssReq.setReceiverUnit(SoundConstant.GROUP_SOUND);
        //获取报文中配置的要同步给集团的内容
        IdentySubtype subtypeEnum = IdentySubtype.getIdentySubtype(identySubtype);
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, subtypeEnum.name()).getId();
        FormInfo formInfo =flowService.getProcessStartFormInfo(processDefinitionProcessId);
        SimpleFormModel formModel = (SimpleFormModel) formInfo.getFormModel();
        List<ParaList> paraLists = new ArrayList<>();
        //表单中定义syncFlag才会生成paraList
        for(FormField formField : formModel.getFields()){
            String id = formField.getId();
            Object value = variableMap.get(id);
            Object syncFlag = formField.getParam(syncFlagStr);
            if(syncFlag == null){
                continue;
            }
            //从变量里面取值
            if(SyncDataType.var.getValue().equals(String.valueOf(syncFlag))){
                ParaList paraList = new ParaList();
                if (subtypeEnum.getValue().equals(IdentySubtype.task_05.getValue())) {
                    if (SyncDataType.dataType.getValue().equals(id)) {
                        id = "type";
                    }
                    if (SyncDataType.handlerDept.getValue().equals(id)) {
                        IdentyCommonCodePo selectBeanByCommonCode = soundCssDao.selectBeanByDataCommonCodeByDataType("taskHandlerDept", String.valueOf(value));
                        if(selectBeanByCommonCode != null) {
                            value = selectBeanByCommonCode.getDataName();
                        }
                    }
                }
                paraList.setParaID(StringUtil.getUpperMethodName(id));
                paraList.setParaVal(value == null ? "" : String.valueOf(value));
                paraLists.add(paraList);

                //获取操作人信息
            }else if(SyncDataType.handler.getValue().equals(String.valueOf(syncFlag))){
                paraLists.addAll(getIdentifierHanderInfo(authService));
                //获取处理意见
            }else if(SyncDataType.opinion.getValue().equals(String.valueOf(syncFlag))){
                paraLists.add(getIdentifierHandlingOpinion(flowService,processInstanceId));
                //处理操作附件
            }else if(SyncDataType.attach.getValue().equals(String.valueOf(syncFlag))){
                //同步工单时,实际没有通过ftp向集团传附件,
                //但是根据2024年10月第二批次需求,集团要通过AttachList及AttachNameList两个字段对附件进行强校验,所以这两个字段只能传空
//                paraLists.add(getIdentifierAttachList(flowService,processInstanceId,authService,variableMap.get(SoundConstant.SELECT_ATTACH),workflowService));
                paraLists.add(new ParaList("AttachList",""));
                paraLists.add(new ParaList("AttachNameList",""));
               //获取操作日志
            }else if(SyncDataType.identylog.getValue().equals(String.valueOf(syncFlag))){
                paraLists.add(getIdentifierExtIdentylogList(flowService,processInstanceId,authService));
            }
        }

        // 处理MeasuresName逻辑
        syncDataCssReq.setParaList(paraLists);
        handleMeasuresNameForSyncData(syncDataCssReq, variableMap);

        return syncDataCssReq;
    }

    private ParaList getIdentifierExtIdentylogList(IFlowService flowService, String processInstanceId,UserAuthService authService) {
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(null, null,null, processInstanceId,
                null, null, null, BooleanFilter.ALL,0, 50);
        List<ExtIdentylogList> identylogLists = new ArrayList<>();
        for(HistoricTaskInstance historicTaskInstance : taskList){
            String deleteReason = historicTaskInstance.getDeleteReason();
            //有用户环节，但未处理，去除掉
            if(StringUtil.isNotEmpty(deleteReason)){
                continue;
            }
            ExtIdentylogList identylogList = new ExtIdentylogList();
            String name = historicTaskInstance.getName();
            String userId = historicTaskInstance.getAssignee();
            ReplyCssReq replyCssReq = authService.queryHandlerInfoReplyInfo(userId);
            identylogList.setHandler(replyCssReq.getHandler());
            identylogList.setHandlerContactInfor(replyCssReq.getHandlerInfor());
            String department = replyCssReq.getHandingDepartment();
            identylogList.setHandingDepartment(StringUtil.isEmpty(department) ? SoundConstant.DEPARTMENT : department);
            identylogList.setHandingTime(SoundDateUtil.getDateTime(historicTaskInstance.getEndTime()));
            String taskId = historicTaskInstance.getId();
            List<Comment> taskComments = flowService.getTaskComments(taskId,"comment");
            if(CollectionUtil.isNotEmpty(taskComments)){
                Comment comment = taskComments.get(0);
                identylogList.setHandingOpinions(comment.getFullMessage());
            }
            identylogLists.add(identylogList);
            if(name.endsWith("审批")){
                identylogList.setPhaseType(String.valueOf(SoundConstant.OK));
            }else {
                identylogList.setPhaseType(String.valueOf(SoundConstant.NOT));
            }
            identylogList.setProcessingName(name);
            //获取处理结果
            Map<String,Object> taskVariables = historicTaskInstance.getTaskLocalVariables();
            if(taskVariables.containsKey(SoundConstant.PASS_FLAG)){
                Object passFlag = taskVariables.get(SoundConstant.PASS_FLAG);
                identylogList.setApprovalResults(String.valueOf(passFlag));
            //默认为通过
            }else {
                identylogList.setApprovalResults(String.valueOf(SoundConstant.OK));
            }
        }
        ParaList paraList = new ParaList();
        paraList.setParaID("ExtIdentylogList");
        paraList.setParaVal(JsonUtil.bean2Json(identylogLists));
        return paraList;
    }

    /**
     * 处理同步数据中的MeasuresName逻辑
     * 当工单子类型为智能研判预警单（0024）时，同步数据请求参数ParaList中需增加MeasuresName字段
     *
     * @param syncDataCssReq 同步数据请求对象
     * @param variableMap 流程变量Map
     */
    private void handleMeasuresNameForSyncData(SyncDataCssReq syncDataCssReq, Map<String, Object> variableMap) {
        if (syncDataCssReq == null) {
            return;
        }

        // 获取工单编号和子类型
        String identifier = syncDataCssReq.getIdentifier();
        String identySubtype = syncDataCssReq.getIdentySubtype();

        if (StringUtils.isBlank(identifier) || StringUtils.isBlank(identySubtype)) {
            return;
        }

        try {
            // 判断是否为智能研判预警单(0024)
            if (IdentySubtype.warning_24.getValue().equals(identySubtype)) {
                log.info("处理智能研判预警单(0024)同步数据，工单号：{}", identifier);

                // 确保ParaList不为空
                if (syncDataCssReq.getParaList() == null) {
                    syncDataCssReq.setParaList(new ArrayList<>());
                }

                // 检查是否已存在MeasuresName参数
                boolean hasMeasuresName = syncDataCssReq.getParaList().stream()
                    .anyMatch(para -> "MeasuresName".equals(para.getParaID()));

                // 如果不存在MeasuresName参数，则添加
                if (!hasMeasuresName) {
                    // 从流程变量和轨迹记录中获取附件名称
                    String measuresName = extractMeasuresNameFromSyncData(identifier, variableMap);

                    ParaList measuresNamePara = new ParaList();
                    measuresNamePara.setParaID("MeasuresName");
                    measuresNamePara.setParaVal(measuresName);
                    syncDataCssReq.getParaList().add(measuresNamePara);

                    log.info("为智能研判预警单(0024)同步数据添加MeasuresName参数，工单号：{}，附件名称：{}", identifier, measuresName);
                }
            }
        } catch (Exception e) {
            log.error("处理智能研判预警单(0024)同步数据特殊参数时发生异常，工单号：{}，异常信息：{}", identifier, e.getMessage(), e);
            // 不抛出异常，避免影响正常的同步数据流程
        }
    }

    /**
     * 从同步数据中提取整改目标附件名称
     *
     * @param identifier 工单编号
     * @param variableMap 流程变量Map
     * @return 整改目标附件名称
     */
    private String extractMeasuresNameFromSyncData(String identifier, Map<String, Object> variableMap) {
        String measuresName = "";

        try {
            // 优先从流程变量中获取附件信息
            Object attachNameListObj = variableMap.get("attachNameList");
            if (attachNameListObj != null && StringUtils.isNotBlank(String.valueOf(attachNameListObj))) {
                measuresName = String.valueOf(attachNameListObj);
                log.info("从流程变量attachNameList获取到附件名称：{}", measuresName);
            }
            // 如果attachNameList为空，则尝试attachList
            else {
                Object attachListObj = variableMap.get("attachList");
                if (attachListObj != null && StringUtils.isNotBlank(String.valueOf(attachListObj))) {
                    measuresName = String.valueOf(attachListObj);
                    log.info("从流程变量attachList获取到附件名称：{}", measuresName);
                }
            }

            // 如果流程变量中没有附件信息，则从轨迹记录中获取
            if (StringUtils.isBlank(measuresName)) {
                OrderTrackRecordMapper orderTrackRecordMapper = applicationContext.getBean(OrderTrackRecordMapper.class);
                List<OrderTrackRecord> trackRecords = orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier);
                if (CollectionUtils.isNotEmpty(trackRecords)) {
                    // 从最新的轨迹记录中获取附件信息
                    for (int i = trackRecords.size() - 1; i >= 0; i--) {
                        OrderTrackRecord record = trackRecords.get(i);
                        if (StringUtils.isNotBlank(record.getAttachNameList())) {
                            measuresName = record.getAttachNameList();
                            log.info("从轨迹记录attachNameList获取到附件名称：{}", measuresName);
                            break;
                        } else if (StringUtils.isNotBlank(record.getAttachList())) {
                            measuresName = record.getAttachList();
                            log.info("从轨迹记录attachList获取到附件名称：{}", measuresName);
                            break;
                        }
                    }
                }
            }

            // 如果附件名称包含多个文件（用|分隔），取第一个作为整改目标附件名称
            if (StringUtils.isNotBlank(measuresName) && measuresName.contains("|")) {
                String[] attachNames = measuresName.split("\\|");
                measuresName = attachNames[0].trim();
                log.info("从多个附件中选择第一个作为整改目标附件名称：{}", measuresName);
            }

            // 如果是文件路径，提取文件名
            if (StringUtils.isNotBlank(measuresName) && (measuresName.contains("/") || measuresName.contains("\\"))) {
                String[] pathParts = measuresName.split("[/\\\\]");
                measuresName = pathParts[pathParts.length - 1];
                log.info("从文件路径中提取文件名：{}", measuresName);
            }

        } catch (Exception e) {
            log.error("从同步数据中提取附件名称时发生异常，工单号：{}，异常信息：{}", identifier, e.getMessage(), e);
            measuresName = ""; // 发生异常时返回空字符串
        }

        return StringUtils.isNotBlank(measuresName) ? measuresName : "";
    }

}
