package com.asiainfo.sound.service.impl;

import cn.hutool.core.io.file.FileNameUtil;
import com.ai.flow.dto.*;
import com.ai.flow.dto.enums.BooleanFilter;
import com.ai.flow.service.IFlowService;
import com.ai.srd.bd.common.exception.CustomException;
import com.asiainfo.sound.dao.*;
import com.asiainfo.sound.daoFlow.SoundCssDao;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.dto.*;
import com.asiainfo.sound.domain.entity.LkgStaff;
import com.asiainfo.sound.domain.enums.FlowUserRole;
import com.asiainfo.sound.domain.enums.IdentyStatus;
import com.asiainfo.sound.domain.enums.IdentySubtype;
import com.asiainfo.sound.domain.enums.TaskPushSource;
import com.asiainfo.sound.domain.query.FlowConfigLoadQuery;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.DispatchCssReq;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.req.TasksReq;
import com.asiainfo.sound.domain.vo.DispatchCssVo;
import com.asiainfo.sound.domain.vo.HistoricTaskInstanceVo;
import com.asiainfo.sound.service.*;
import com.asiainfo.sound.util.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.flowable.content.api.ContentItem;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/1/17 18:04
 * @desc 工作流程服务实习类
 */
@Service
@Slf4j
public class WorkflowServiceImpl implements WorkflowService {
    public static final String ZIP = "zip";
    public static final String USER_COMMENT = "comment";
    public static final String SYSTEM_COMMENT = "system";
    private final IFlowService flowService;
    private final RuntimeService runtimeService;
    private final FlowableMapper flowableMapper;

    private final ILkgStaffService iLkgStaffService;

    private static ExecutorService executorService = CreatThreadUtil.creatThread();

    private static final String[] MACOS_FILE = {".DS_Store", "__MACOSX"};
    @Value("${static.file.dir}")
    private String baseDir;

    //oa推送服务相关内容
    @Value("${oa.id}")
    private String oaId;
    @Value("${oa.name}")
    private String oaName;
    @Value("${oa.url-for-task}")
    private String oaUrl;
    @Autowired
    private OAService oaService;

    @Autowired
    private SoundCssDao cssDao;

    @Autowired
    private IdentyInfoDao identyInfoDao;

    @Resource
    private UserDao userDao;

    @Resource
    private ILkgStaffService staffService;

    @Resource
    private SoundIdentySendCopyDao sendCopyDao;

    @Autowired
    private UserAuthService userAuthService;
    @Autowired
    private IdentyDyInfoService infoService;
    @Resource
    private OrderTrackRecordService orderTrackRecordService;
    @Resource
    private OrderTrackRecordMapper orderTrackRecordMapper;
    @Autowired
    private FileUploadService fileUploadService;

    public WorkflowServiceImpl(IFlowService flowService, RuntimeService runtimeService, FlowableMapper flowableMapper, ILkgStaffService iLkgStaffService) {
        this.flowService = flowService;
        this.runtimeService = runtimeService;
        this.flowableMapper = flowableMapper;
        this.iLkgStaffService = iLkgStaffService;
    }


    @Override
    public List<String> deployDefinitionAndFormBatch(MultipartFile file) {
        List<String> ret = new ArrayList<>();
        String currentFileName = "";
        try {
            MultipartFile multipartFile = file;

            if (null == multipartFile) {
                return null;
            }
            String originalFilename = multipartFile.getOriginalFilename();

            String extName = FileNameUtil.extName(originalFilename);
            if (!ZIP.equalsIgnoreCase(extName)) {
                return null;
            }
            @Cleanup ZipArchiveInputStream zipArchiveInputStream = new ZipArchiveInputStream(file.getInputStream());
            while (true) {
                ZipArchiveEntry zipEntry = zipArchiveInputStream.getNextZipEntry();
                if (zipEntry == null) {
                    break;
                }
                if (!zipEntry.isDirectory() && !zipEntry.isUnixSymlink()) {
                    boolean isGBK = false;
                    for (char c : new String(zipEntry.getRawName(), "utf-8").toCharArray()) {
                        if (c >= 65533) {
                            isGBK = true;
                        }
                    }
                    boolean isMaxOSFile = false;
                    String fileName = new String(zipEntry.getRawName(), isGBK ? "GBK" : "UTF-8");
                    for (String macosx : MACOS_FILE) {
                        if (fileName.startsWith(macosx)) {
                            isMaxOSFile = true;
                        }
                    }
                    if (isMaxOSFile) {
                        continue;
                    }
                    int i = fileName.lastIndexOf(File.pathSeparator);
                    // 只取文件名
                    if (i > 0) {
                        fileName = fileName.substring(i + 1);
                    }
                    // 判断是否是mac的隐藏文件夹
                    currentFileName = fileName;
                    String extName1 = FileNameUtil.extName(fileName);
                    ret.add(fileName);
                    @Cleanup ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    IOUtils.copy(zipArchiveInputStream, byteArrayOutputStream);
                    String s = byteArrayOutputStream.toString("UTF-8");
                    log.info(fileName + "\n" + s);
                    if ("xml".equalsIgnoreCase(extName1)) {
                        flowService.deployProcessDefinition(fileName, s);
                    } else if ("form".equalsIgnoreCase(extName1)) {
                        flowService.deployFormDefinition(fileName, s);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(currentFileName);
            return null;
        }
        return ret;
    }

    @Override
    public Page<Task> getWaitingTasks(TasksReq tasksReq, Integer currentPage, Integer pageSize) {
        String name = AuthHelper.getUserName();
        boolean isAdmin = userAuthService.queryUserIsAdmin(name);
        //如果是管理员同时也勾选了只显示自己的则不传userId
        if (tasksReq.getShowAll() && isAdmin) {
            name = null;
        }
        Long count = flowService.getTaskCount(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), tasksReq.getTaskVariables());
        List<Task> taskList = flowService.getTasks(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), tasksReq.getTaskVariables(),
                tasksReq.getVariableNames(),
                currentPage * pageSize, pageSize);
        Page<Task> tasks = new Page<>(currentPage, pageSize);
        tasks.setTotal(count);
        tasks.addAll(taskList);
        return tasks;
    }

    @Override
    public Page<Task> getOnapushTasks(TasksReq tasksReq,String taskId, Integer currentPage, Integer pageSize) {
        String name = AuthHelper.getUserName();
        boolean isAdmin = AuthHelper.hasRole(FlowUserRole.admin.getValue());
        //如果是管理员同时也勾选了只显示自己的则不传userId
        if (tasksReq.getShowAll() && isAdmin) {
            name = null;
        }
        Long count = flowService.getTaskCount(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), tasksReq.getTaskVariables());
        //无当前数据，取消推送信息
        if(count == 0){
            OAPushDocDto docDto = new OAPushDocDto();
            HistoricProcessInstance taskInstance = getHistoricTaskInstanceByTaskReq(tasksReq);
            if(taskInstance !=null){
                docDto.setDocId(taskInstance.getId());
                docDto.setWorkId(taskId);
                docDto.setUserId(AuthHelper.getUserName());
                sendCancelOapushMessge(docDto,taskInstance.getProcessVariables());
                PortalDealTaskDto dto = new PortalDealTaskDto();
                dto.setTaskId(taskId);
                dto.setDelFlag(String.valueOf(SoundConstant.OK));
                infoService.modifyDyPortalDealTask(dto);
            }
        }
        List<Task> taskList = flowService.getTasks(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), tasksReq.getTaskVariables(),
                tasksReq.getVariableNames(),
                currentPage * pageSize, pageSize);
        Page<Task> tasks = new Page<>(currentPage, pageSize);
        tasks.setTotal(count);
        tasks.addAll(taskList);
        return tasks;
    }

    /**
     * 发送取消OA推送消息
     */
    private void sendCancelOapushMessge(OAPushDocDto docDto,Map<String, Object> varis) {
        OAPushData data = new OAPushData();
        data.setId(oaId);
        data.setName(oaName);
        List<TaskForOAPush> closes = new ArrayList<>();
        closes.add(createFromDelegateTask(docDto,varis,oaUrl));
        data.setClose(closes);
        try {
            oaService.push(data);
        } catch (IOException e) {
            log.error("io exception",e);
        } catch (JAXBException e) {
            log.error("jaxb exception",e);
        }
    }

    private TaskForOAPush createFromDelegateTask(OAPushDocDto dto, Map<String, Object> variables,String url) {
        TaskForOAPush t = new TaskForOAPush();
        t.setTitle(String.format("%s - %s", variables.get("title"), variables.get("identifier")));
        String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
        t.setStartTime(cn.hutool.core.date.DateUtil.format(LocalDateTime.now().minusHours(2), YYYY_MM_DD_HH_MM_SS));
        t.setEndTime(cn.hutool.core.date.DateUtil.format(LocalDateTime.now(), YYYY_MM_DD_HH_MM_SS));
        t.setDocId(dto.getDocId());
        t.setDocType("大音平台");
        t.setPri("1");
        t.setWorkId(dto.getWorkId());
        t.setUserId(dto.getUserId());
        t.setType("1");
        String redirect = String.format("/workflow?id=%s&taskId=%s&source=%s", variables.get("identyType"), variables.get("identifier"),dto.getDocId(), TaskPushSource.fourA.getValue());
        try {
            t.setUrl(String.format("%s?redirect=%s", url, URLEncoder.encode(redirect, "UTF-8")));
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }
        return t;
    }

    @Override
    public Page<HistoricProcessInstance> getHistoricProcessInstances(TasksReq tasksReq, BooleanFilter hasFinished, Integer currentPage, Integer pageSize) {
        String name = AuthHelper.getUserName();
        boolean isAdmin = userAuthService.queryUserIsAdmin(name);
        //如果是管理员同时也勾选了只显示自己的则不传userId
        if (tasksReq.getShowAll() && isAdmin) {
            name = null;
        }
        Long count = flowService.getHistoricProcessInstanceCount(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), hasFinished);
        List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstances(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), hasFinished, currentPage * pageSize, pageSize);

        Page<HistoricProcessInstance> tasks = new Page<>(currentPage, pageSize);

        tasks.setTotal(count);
        tasks.addAll(historicProcessInstances);
        return tasks;
    }

    @Override
    public Page<HistoricTaskInstanceVo> getHistoricTasks(TasksReq tasksReq, Integer currentPage, Integer pageSize) {
        // 如果查一个工单的历史任务则不需要通过人过滤
        String name = StringUtils.isEmpty(tasksReq.getProcessInstanceId()) ? AuthHelper.getUserName() : null;
//        boolean isAdmin = AuthHelper.hasRole("ROLE_ADMIN");
//        //如果是管理员同时也勾选了只显示自己的则不传userId
//        if (tasksReq.getShowAll() && isAdmin) {
//            name = null;
//        }

        Long count = flowService.getHistoricTaskInstanceCount(name, tasksReq.getProcessDefinitionKey(), tasksReq.getProcessInstanceId(),
                tasksReq.getProcessVariables(), tasksReq.getTaskVariables());
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(
                name,
                tasksReq.getProcessDefinitionKey(),
                null,
                tasksReq.getProcessInstanceId(),
                tasksReq.getProcessVariables(),
                tasksReq.getTaskVariables(),
                tasksReq.getVariableNames(),
                BooleanFilter.ALL,
                currentPage * pageSize,
                pageSize);
        List<HistoricTaskInstanceVo> taskInstanceVos = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(taskList)){
            for(HistoricTaskInstance historicTaskInstance : taskList){
                HistoricTaskInstanceVo taskInstanceVo = new HistoricTaskInstanceVo();
                BeanUtils.copyProperties(historicTaskInstance,taskInstanceVo);
                ReplyCssReq replyCssReq = userAuthService.queryHandlerInfoReplyInfo(taskInstanceVo.getAssignee());
                taskInstanceVo.setUserPhone(replyCssReq.getHandlerInfor());
                String taskId = taskInstanceVo.getId();
                List<OrderTrackRecord> orderTrackRecords = queryNodeAttachList(taskId);
                List<Attachment> attachments;
                if (CollectionUtil.isNotEmpty(orderTrackRecords)){
                    String processInstanceId = orderTrackRecords.get(0).getProcessInstanceId();
                    List<ContentItem> contents = flowService.getContents(null, processInstanceId, taskId);
                    attachments = convertValue(contents);
                }else {
                    attachments = flowService.getAttachments(taskId);
                }
                taskInstanceVo.setAttachments(attachments);
                taskInstanceVos.add(taskInstanceVo);
            }
        }
        Page<HistoricTaskInstanceVo> tasks = new Page<>(currentPage, pageSize);

        tasks.setTotal(count);
        tasks.addAll(taskInstanceVos);
        return tasks;
    }

    @Override
    public Page<HistoricProcessInstance> getHistoricProcessInstancesWithCompleteTask(TasksReq tasksReq, Integer currentPage, Integer pageSize) {
        String name = AuthHelper.getUserName();
        boolean isAdmin = AuthHelper.hasRole("ROLE_ADMIN");
        //如果是管理员同时也勾选了只显示自己的则不传userId
        if (tasksReq.getShowAll() && isAdmin) {
            name = null;
        }
        Long count = flowService.getHistoricProcessInstancesWithCompleteTaskCount(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables());
        List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstancesWithCompleteTask(name, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), currentPage * pageSize, pageSize);
        Page<HistoricProcessInstance> tasks = new Page<>(currentPage, pageSize);
        tasks.setTotal(count);
        tasks.addAll(historicProcessInstances);
        return tasks;
    }

    @Override
    public void startProcessByVO(String processKey, DispatchCssVo vo) {
        vo.setIdentifier(SoundParamUtil.getIdentifier());
        vo.setOriginUnit(SoundConstant.PROVINCE_CODE);
        vo.setReceiverUnit(SoundConstant.PROVINCE_CODE);
        vo.setCreatTime(SoundDateUtil.getNowDateTime());
        vo.setType(0);
        ReplyCssReq replyCssReq = userAuthService.queryHandlerInfoReplyInfo(AuthHelper.getUserName());
        vo.setCreator(replyCssReq.getHandler());
        vo.setCreatorContactInfo(replyCssReq.getHandlerInfor());
        Map<String, Object> variables = vo.toMap();
        List<String> userList = new ArrayList<>();
        userList.add(vo.getUserName());
        variables.put(SoundConstant.USER_LIST_STR,userList);
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(vo.getIdentySubtype());
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, identySubtype.name()).getId();
        flowService.startProcess(processDefinitionProcessId, null, variables);
    }

    @Override
    public void startProcessNewByVO(String processKey, Map<String, Object> variables) {
        String userId = AuthHelper.getUserName();
        variables.put("identifier",SoundParamUtil.getIdentifier());
        variables.put("originUnit",SoundConstant.PROVINCE_CODE);
        variables.put("receiverUnit",SoundConstant.PROVINCE_CODE);
        variables.put("creatTime",SoundDateUtil.getNowDateTime());
        variables.put("type",0);
        ReplyCssReq replyCssReq = userAuthService.queryHandlerInfoReplyInfo(userId);
        variables.put("creator",replyCssReq.getHandler());
        variables.put("creatorContactInfo",replyCssReq.getHandlerInfor());
        variables.put("creatorDept",replyCssReq.getHandingDepartment());
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(String.valueOf(variables.get("identySubtype")));
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, identySubtype.name()).getId();
        flowService.startProcess(processDefinitionProcessId, userId, variables);

        DispatchCssDto dispatchDto = new DispatchCssDto();
        dispatchDto.setTitle(String.valueOf(variables.get("title")));
        dispatchDto.setIdentyDetail(String.valueOf(variables.get("identyDetail")));
        dispatchDto.setContent(String.valueOf(variables.get("content")));
        dispatchDto.setAttachList(String.valueOf(variables.get("attachList")));
        dispatchDto.setOriginUnit(SoundConstant.PROVINCE_CODE);
        dispatchDto.setReceiverUnit(SoundConstant.PROVINCE_CODE);
        dispatchDto.setIdentyState(IdentyStatus.dispatch.getValue());
        dispatchDto.setCreator(replyCssReq.getHandler());
        dispatchDto.setProcessTime(String.valueOf(variables.get("processTime")));
        dispatchDto.setIdentifier(String.valueOf(variables.get("identifier")));
        dispatchDto.setCreatTime(String.valueOf(variables.get("creatTime")));
        dispatchDto.setSenderFlag(Integer.valueOf(SoundConstant.NOT));
        dispatchDto.setIdentySubtype(String.valueOf(variables.get("identySubtype")));
        dispatchDto.setIdentyType(String.valueOf(variables.get("identyType")));
        dispatchDto.setCreatorContactInfo(String.valueOf(variables.get("creatorContactInfo")));
        dispatchDto.setCreatDate(SoundDateUtil.getDate());
        dispatchDto.setUserId(userId);
        identyInfoDao.updateTrackInfo(String.valueOf(variables.get("identifier")),String.valueOf(variables.get("attachList")));
        cssDao.insertIdentyBasicInfo(dispatchDto);
//        identyInfoDao.insertIdentyBasicInfo(dispatchDto);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void dealProcess(String taskId, Map<String, Object> variables, String filePath, String fileName, String remark, Integer isPass) {
        String processInstanceId = flowService.getProcessInstanceId(taskId);
        String userName = AuthHelper.getUserName();
        LkgStaff staff = staffService.getOne(userName);
        String mobile = staff.getMobile();
        //保存附件到新表
        Map<String, Object> taskVariables = flowService.getTaskVariables(taskId, null);
        log.info("流程节点参数1：{}",variables);
        log.info("流程节点参数2：{}",taskVariables);
        String comment = variables.get("comment").toString();
        String identifier = taskVariables.get("identifier").toString();
        // 从流程变量中获取trackType参数，如果没有则使用默认值"01"
        String trackType = variables.get("trackType") != null ?
                variables.get("trackType").toString() : "01";
        OrderTrackRecord record = new OrderTrackRecord();
        record.setTaskId(taskId).setIdentifier(identifier).setProcessInstanceId(processInstanceId)
                .setInterfaceLink("02").setTrackType(trackType).setHandingTime(SoundParamUtil.getTimeStamp())
                .setHandler(staff.getName())
                .setHandlerContactInfor(mobile).setHandingOpinions(comment)
                .setAttachNameList(fileName).setAttachFileName(filePath);

        if (StringUtil.isEmpty(staff.getOrgName())) {
            record.setHandingDepartment("中国移动通信集团宁夏有限公司/网络部");
        }else {
            record.setHandingDepartment("中国移动通信集团宁夏有限公司/" + staff.getOrgName());
        }
        /*
        01：公司领导
        02：部门领导
        03：科室经理
        04：员工
        */
        String handlerRank = staff.getPostName();
        if ("公司总经理".equals(handlerRank) || "公司副总经理".equals(handlerRank) || "省公司副总经理".equals(handlerRank) ||"省公司总经理".equals(handlerRank) || "公司领导".equals(handlerRank)) {
            record.setHandlerRank("01");
            record.setHandlerLeadLevel("01");
        }else if ("部门总经理".equals(handlerRank) || "部门副总经理".equals(handlerRank)) {
            record.setHandlerRank("02");
            record.setHandlerLeadLevel("02");
        }else if ("部门经理".equals(handlerRank) || "部门副经理".equals(handlerRank)) {
            record.setHandlerRank("03");
            record.setHandlerLeadLevel("03");
        } else {
            record.setHandlerRank("04");
            record.setHandlerLeadLevel("03");
        }

        String identySubtype = taskVariables.get("identySubtype").toString();
        if ("0211".equals(identySubtype)) {
            //如果是总部重点案例任务单(0211),则必须是02
            record.setHandlerLeadLevel("02");
        }

        //使用多线程解决事务问题
        Future submit = executorService.submit(() -> {
            try {
                if (!StringUtil.isNullOrEmpty(filePath)) {
                    InputStream inputStream = new FileInputStream(new File(baseDir + filePath));
                    ContentItem contentItem = flowService.newContent();
                    contentItem.setName(fileName);
                    contentItem.setTaskId(taskId);
                    contentItem.setProcessInstanceId(processInstanceId);
                    flowService.saveContent(contentItem, inputStream);
                }
            } catch (FileNotFoundException e) {
                log.info("该路径下文件缺失====="+baseDir+filePath);
                throw new CustomException("该路径下文件缺失====="+baseDir+filePath);
            }finally {
                log.info("保存附件成功");
                orderTrackRecordService.save(record);
            }
        });
        if (!StringUtils.isEmpty(remark)) {
            flowService.addComment(taskId, processInstanceId, USER_COMMENT, remark, AuthHelper.getUserName());
        }
        //flowService.setVariable(taskId,SoundConstant.PASS_FLAG,isPass);
        //将当前操作的变量，放入任务变量中
        flowService.setVariablesLocal(taskId,variables);
        flowService.completeUserTask(taskId, variables);
    }

    @Override
    public void setTaskAssignee(String taskId, String newUserName, String remark, String filePath, String fileName) {
        //todo: 检查用户是否存在
        String processInstanceId = flowService.getProcessInstanceId(taskId);
        if (!StringUtils.isEmpty(filePath)) {
            FileInputStream fis = this.getFileInputStream(filePath);
            if (fis != null) {
                ContentItem contentItem = flowService.newContent();
                contentItem.setName(fileName);
                contentItem.setTaskId(taskId);
                contentItem.setProcessInstanceId(processInstanceId);
                flowService.saveContent(contentItem, fis);
            }
        }
        if (!StringUtils.isEmpty(remark)) {
            flowService.addComment(taskId, processInstanceId, USER_COMMENT, remark, AuthHelper.getUserName());
        }
        Task task = flowService.getTaskById(taskId);
        flowService.addComment(taskId, processInstanceId, SYSTEM_COMMENT, String.format("处理人由%s改为%s", task.getAssignee(), newUserName), AuthHelper.getUserName());
        flowService.setTaskAssignee(taskId, newUserName);
    }

    @Override
    public void createIdentyTask(DispatchCssReq cssReq) {
        cssReq.setIdentifier(SoundParamUtil.getIdentifier());
        cssReq.setOriginUnit(SoundConstant.PROVINCE_CODE);
        cssReq.setReceiverUnit(SoundConstant.PROVINCE_CODE);
        cssReq.setCreatTime(SoundDateUtil.getNowDateTime());
        DispatchCssVo dispatchCssVo = new DispatchCssVo();
        BeanUtils.copyProperties(cssReq, dispatchCssVo);
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(dispatchCssVo.getIdentySubtype());
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, identySubtype.name()).getId();
        UserTokenQuery tokenQuery = new UserTokenQuery();
        tokenQuery.setUserId(SoundConstant.PROVINCE_USER);
        Map<String, Object> dispatchMap = dispatchCssVo.toMap();
        dispatchMap.put("userName", tokenQuery.getUserId());
        dispatchMap.put("type", 0);
        flowService.startProcess(processDefinitionProcessId, tokenQuery.getUserId(), dispatchMap);
    }



    @Override
    public FormInfo getProcessSiteFormInfo(String resourceName, String key) {
        return flowService.getFormInfo(key);
    }

    @Override
    public String getProcessFlowXml(String processKey) {
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, processKey).getId();
        InputStream inputStream = flowService.getProcessDefinitionXML(processDefinitionProcessId);
        try {
            return IOUtils.toString(inputStream,"utf8");
        } catch (IOException e) {
            log.error("io exception e",e);
        }
        return null;
    }

    @Override
    public String flowableLoadProcessDefinition(FlowConfigLoadQuery query) {
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(query.getIdentySubtype());
        String resourceName = null;
        if(identySubtype != null){
            resourceName = identySubtype.getLabel() + ".bpmn20.xml";
        }else {
            resourceName = "未知工单" + ".bpmn20.xml";
        }
        return flowService.deployProcessDefinition(resourceName,query.getXmlStr());
    }

    @Override
    public void removeProcessInstance(String identifier,String deleteReason) {
        if (StringUtil.isEmpty(identifier)) {
            throw new RuntimeException("工单号不能为空");
        }
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", identifier);
        List<Task> tasks = flowService.getTasks(null, null, variables, null, null, 0, 100);
        //流程没有结束的
        if (CollectionUtil.isNotEmpty(tasks)) {
            for (Task task : tasks) {
                flowService.removeProcessInstance(task.getProcessInstanceId(), deleteReason);
                flowService.removeHistoricProcessInstance(task.getProcessInstanceId());
            }
            //流程已完结
        } else {
            List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstances(null, null, variables, BooleanFilter.TRUE, 0, 10);
            if (CollectionUtil.isNotEmpty(historicProcessInstances)) {
               for(HistoricProcessInstance historicProcessInstance : historicProcessInstances){
                   flowService.removeHistoricProcessInstance(historicProcessInstance.getId());
               }
            }
        }
        IdentyHeader identyHeader = new IdentyHeader();
        identyHeader.setIdentifier(identifier);
        cssDao.deleteIdentyBasicInfo(identyHeader);
        cssDao.deleteIdentyHandlerHis(identyHeader);
        //单独起一个线程删除抄送表中的任务
        executorService.submit(()->{
            QueryWrapper<SoundIdentySendCopyInfo> qw = new QueryWrapper<>();
            qw.lambda().eq(SoundIdentySendCopyInfo::getIdentifier,identifier);
            sendCopyDao.delete(qw);
        });
        identyInfoDao.deleteIdentyBasicInfo(identyHeader);
    }


    @Override
    public void removeRunningProcessInstance(String identifier,String deleteReason) {
        if (StringUtil.isEmpty(identifier)) {
            throw new RuntimeException("工单号不能为空");
        }
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", identifier);
        List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstances(null, null, variables, BooleanFilter.ALL, 0, 10);
        if (CollectionUtil.isNotEmpty(historicProcessInstances)) {
            for(HistoricProcessInstance historicProcessInstance : historicProcessInstances){
                runtimeService.deleteProcessInstance(historicProcessInstance.getId(),deleteReason);
            }
        }
        IdentyHeader identyHeader = new IdentyHeader();
        identyHeader.setIdentifier(identifier);
        cssDao.deleteIdentyBasicInfo(identyHeader);
        cssDao.deleteIdentyHandlerHis(identyHeader);
        //单独起一个线程删除抄送表中的任务
        executorService.submit(()->{
            QueryWrapper<SoundIdentySendCopyInfo> qw = new QueryWrapper<>();
            qw.lambda().eq(SoundIdentySendCopyInfo::getIdentifier,identifier);
            sendCopyDao.delete(qw);
        });
        identyInfoDao.deleteIdentyBasicInfo(identyHeader);
    }

    @Override
    public void removeProcessByIncId(String proInstId, String deleteReason) {
        flowService.removeProcessInstance(proInstId, deleteReason);
        flowService.removeHistoricProcessInstance(proInstId);
    }

    /**
     * 获取文件流
     *
     * @param filePath
     * @return
     */
    private FileInputStream getFileInputStream(String filePath) {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(baseDir + filePath);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return fis;
    }

    private HistoricProcessInstance getHistoricTaskInstanceByTaskReq(TasksReq tasksReq){
        List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstances(null, tasksReq.getProcessDefinitionKey(),
                tasksReq.getProcessVariables(), BooleanFilter.ALL, 0, 1);
        if(CollectionUtil.isEmpty(historicProcessInstances)){
            return null;
        }else {
            return historicProcessInstances.get(0);
        }
    }

    /**
     *修复回退bug
     */
    @Override
    public List<FlowNodeVo> getBackNodes(String taskId, String processInstanceId) {
        Map<String, LkgStaff> allLkgStaffInMap = iLkgStaffService.getAllInMap();
        List<FlowNodeVo> backNodesByProcessInstanceId = flowService.getBackNodesByProcessInstanceId(taskId, processInstanceId);
        for (FlowNodeVo flowNodeVo : backNodesByProcessInstanceId) {
            String realCode = flowNodeVo.getUserName();
            LkgStaff lkgStaff = allLkgStaffInMap.get(realCode);
            flowNodeVo.setUserCode(realCode);
            flowNodeVo.setUserName(lkgStaff.getName());
        }
        return backNodesByProcessInstanceId;
    }

    /**
     * 根据taskID查询该节点下的附件
     * @param taskId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<OrderTrackRecord> queryNodeAttachList(String taskId) {
        return orderTrackRecordMapper.queryByTaskId(taskId);
    }

    @Override
    public InputStream getAttachmentContent(String attachId) {
        List<OrderTrackRecord> orderTrackRecords = queryNodeAttachList(attachId);
        String attachFileName = orderTrackRecords.get(0).getAttachFileName();
        try {
            File f = new File(baseDir+attachFileName);
            InputStream inputStream = new FileInputStream(f);
            return inputStream;
        } catch (FileNotFoundException e) {
            throw new CustomException("当前路径文件缺失"+baseDir+attachFileName);
        }
    }

    @Override
    public List<Attachment> convertValue(List<ContentItem> contents) {
        List<Attachment> attachments = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(contents)){
            for (ContentItem content : contents) {
                Attachment attachment = new Attachment();
                attachment.setName(content.getName());
                attachment.setProcessInstanceId(content.getProcessInstanceId());
                attachment.setTaskId(content.getTaskId());
                attachment.setId(content.getId());
                attachment.setType("file");
                attachment.setTime(content.getCreated());
                attachments.add(attachment);
            }
            return attachments;
        }
        return attachments;
    }

    @Override
    public void saveMessageLog(String mobile, String message, String sysTime, String identifier, String loginName, int type) {
        orderTrackRecordMapper.saveMessageLog(mobile, message, sysTime, identifier, loginName, type);
    }

    @Override
    public boolean doHandledbSecretkey(HistoricProcessInstance processInstance) {
        boolean isSuccess = true;
        try {
            Map<String, Object> processVariables = processInstance.getProcessVariables();
            String secretKey = String.valueOf(processVariables.get("secretKey"));
            String attachList = String.valueOf(processVariables.get("attachList"));
            log.info("通用督办单任务处理,原文件{}",attachList);
            List<String> fileNames;
            // 1、检查文件是否存在,获取文件路径
            String zipPath = fileUploadService.getFilePath(attachList);
            if(StringUtil.isNotEmpty(zipPath)){
                // 2、将文件解压到目录并返回解压得到的文件名列表
                fileNames = ZipReleaseUtil.releaseZipToFiles(zipPath, zipPath + attachList, secretKey);
                if(fileNames.isEmpty()){
                    log.info("通用督办单任务处理,解压的文件[{}]为空",zipPath);
                    isSuccess = false;
                }else{
                    log.info("通用督办单任务处理,更新原文件变量为解压得到文件列表{}",fileNames);
                    String filePrefixName= FilenameUtils.getBaseName(attachList);
                    List<String> newfileNames = new ArrayList<>();
                    for (int i = 0; i < fileNames.size(); i++) {
                        String oldFileName = fileNames.get(i);
                        String fileEnd = FilenameUtils.getExtension(oldFileName);
                        String newFileName = filePrefixName + i + "." + fileEnd;
                        log.info("将文件{}重命名为{}",oldFileName,newFileName);
                        boolean b = ZipReleaseUtil.renameFile(zipPath + oldFileName, newFileName);
                        if(!b){
                            throw new CustomException("重命名文件失败.");
                        }
                        newfileNames.add(newFileName);
                    }
                    log.info("通用督办单任务处理,原文件列表重命名为{}",newfileNames);
                    Map<String,String> attachMap = new HashMap<>();
                    attachMap.put("attachList",String.join("|",newfileNames));
                    attachMap.put("attachNameList",String.join("|",fileNames));
                    updateVariablesByIdentifier(processInstance.getProcessInstanceId(),attachMap);
                }
            }else{
                log.info("通用督办单任务处理,待解压的文件[{}]不存在",attachList);
                isSuccess = false;
            }
        }catch (Exception e){
            e.printStackTrace();
            isSuccess = false;
        }
        return isSuccess;
    }

    private void updateVariablesByIdentifier(String processInstanceId,Map<String,String> attachMap) {
        runtimeService.setVariables(processInstanceId,attachMap);
    }

    @Override
    public void setVariable(String identifier, String varName, String varValue) {
        flowableMapper.setRunVariable(identifier,varName,varValue);
        flowableMapper.setHisVariable(identifier,varName,varValue);
    }
}
