package com.asiainfo.sound.service.impl;

import com.alibaba.fastjson.JSON;
import com.asiainfo.sound.dao.IdentyInfoDao;
import com.asiainfo.sound.dao.OrderTrackRecordMapper;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.OrderTrackTaskLog;
import com.asiainfo.sound.domain.enums.UnitCode;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.req.TrackinforCssReq;
import com.asiainfo.sound.domain.req.TrackinforIdentyLog;
import com.asiainfo.sound.domain.req.TrackinforQueryReq;
import com.asiainfo.sound.domain.resp.TrackinforInfoResp;
import com.asiainfo.sound.domain.resp.HeaderCodeResp;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.service.*;
import com.asiainfo.sound.util.SoundConstant;
import com.asiainfo.sound.util.StringUtil;
import com.asiainfo.sound.util.TrackinforInfoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/3
 **/
@Slf4j
@Service
public class WorkOrderTrackinforServiceImpl implements WorkOrderTrackinforService {
    @Autowired
    private SoundSendService soundSendService;

    @Autowired
    private OrderTrackRecordMapper orderTrackRecordMapper;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private OrderAttachSequenceService orderAttachSequenceService;

    @Autowired
    private IdentyInfoDao identyInfoDao;

    @Autowired
    private OperatingEnvironmentService operatingEnvironmentService;

    private DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public void handleAttachUpload(String identifier,String groupRealAttachName,String identySubtypeName) {
        // 根据工单编号获取所有轨迹列表
        List<OrderTrackRecord> orderTrackRecords = orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier);
        if (CollectionUtils.isEmpty(orderTrackRecords)) {
            throw new RuntimeException("工单轨迹为空" + identifier);
        }
        for (OrderTrackRecord orderTrackRecord : orderTrackRecords) {
            // 轨迹附件实际名称
            String attachNameList = orderTrackRecord.getAttachNameList();
            // 获取轨迹附件路径
            String attachFileName = orderTrackRecord.getAttachFileName();
            // 轨迹附件根据规则生成的名称
            String recordAttachList = orderTrackRecord.getAttachList();

            // 如果存在，则上传到集团SFTP
            if (StringUtils.isNotBlank(attachNameList) && StringUtils.isNotBlank(attachFileName) && StringUtils.isBlank(recordAttachList)) {
                String[] attachNameArray = attachNameList.split("\\|");
                String[] attachFilePathArray = attachFileName.split("\\|");
                List<String> groupRealAttachNameList = null;
                if (StringUtils.isNotEmpty(groupRealAttachName)) {
                    String[] groupRealAttachNameArray = groupRealAttachName.split("\\|");
                    groupRealAttachNameList = Arrays.asList(groupRealAttachNameArray);
                }

                String attachList = "";
                String groupRealAttachNameStr = "";
                for (int i = 0; i < attachFilePathArray.length; i++) {
                    String attachName = attachNameArray[i];
                    String fileSuffix = FilenameUtils.getExtension(attachName);
                    // 获取集团唯一附件名称
                    String attachSequenceFileName = orderAttachSequenceService.getAttachSequenceFileName();
                    attachSequenceFileName = attachSequenceFileName + "." + fileSuffix;
                    attachList += attachSequenceFileName + "|";
                    // 附件路径
                    String attachFilePath = attachFilePathArray[i];
                    // 上传到集团
                    if (operatingEnvironmentService.isProduct()) {
                        fileUploadService.loadAttachListToSftpNew(attachSequenceFileName, attachFilePath);
                    }
                    if (groupRealAttachNameList != null && groupRealAttachNameList.size() > 0) {
                        List<String> realNameList = groupRealAttachNameList.stream().filter(x -> x.toLowerCase().endsWith(fileSuffix.toLowerCase())).collect(Collectors.toList());
                        if (realNameList.size() > 0) {
                            groupRealAttachNameStr += realNameList.get(0) + "|";
                        }else {
                            //集团下发工单未携带附件，省专公司如需上传本地附件，则按“附件实际名称命名要求”上传即可
                            //附件名称命名规则：省份名称_业务名称_日期；（如：北京_“集团大音不知情定制预警单反馈表”_2023-3-29
                            groupRealAttachNameStr += getEmptyFileName(identySubtypeName,fileSuffix) + "|";
                        }
                    }else {
                        groupRealAttachNameStr += getEmptyFileName(identySubtypeName,fileSuffix) + "|";
                    }
                }
                attachList = attachList.substring(0, attachList.lastIndexOf("|"));

                if (!StringUtil.isEmpty(groupRealAttachNameStr)) {
                    groupRealAttachNameStr = groupRealAttachNameStr.substring(0, groupRealAttachNameStr.lastIndexOf("|"));
                }
                log.info("attachList:" + attachList);
                log.info("groupRealAttachNameStr:" + groupRealAttachNameStr);
                // 更新attach_list
                Long orderTrackRecordId = orderTrackRecord.getId();
                int updateResult = orderTrackRecordMapper.updateAttachListById(orderTrackRecordId, attachList,groupRealAttachNameStr);
                if (updateResult != 1) {
                    throw new RuntimeException("修改附件名称异常");
                }
            }
        }
    }

    private String getEmptyFileName(String identySubtypeName,String fileSuffix){
        //时间转字符串
        LocalDate now = LocalDate.now();
        String nowStr = dayFormatter.format(now);
        String realName = UnitCode.ningxia.getLabel() + "_" + identySubtypeName  + "_" + nowStr  + "." + fileSuffix;
        return realName;
    }

    /**
     * 判断是否为不知情定制投诉任务单
     * @param identifier 工单编号
     * @return true-是不知情定制投诉任务单，false-不是
     */
    private boolean isUnknownCustomComplaintTask(String identifier) {
        try {
            String identyDetail = identyInfoDao.getIdentyDetail(identifier);
            if (StringUtils.isNotBlank(identyDetail)) {
                // 不知情定制投诉任务单的细类：02020901(新增不知情定制投诉)、02020902(业务订购争议投诉量监控)
                // || "01010904".startsWith(identyDetail)
                return "02020901".equals(identyDetail) || "02020902".equals(identyDetail);
            }
        } catch (Exception e) {
            log.warn("获取工单细类失败，工单号：{}", identifier, e);
        }
        return false;
    }


    @Override
    public boolean isExistsAttach(String identifier) {
        int count = orderTrackRecordMapper.getExistsAttachTrackCountByIdentifier(identifier);
        return count > 0;
    }

    @Override
    public void handleTrackinfor(ReplyCssReq cssReq, OrderTrackTaskLog trackTaskLog) {
        log.info("工单轨迹信息上报处理开始");
        // 工单轨迹信息上报（TrackinforCSS）
        // 上报成功，再进行回复
        String identifier = null;
        try {
            if (cssReq == null) {
                throw new RuntimeException("cssReq为空");
            }
            identifier = cssReq.getIdentifier();

            String launchCompany = cssReq.getLaunchCompany();
            String forwardCompany = cssReq.getForwardCompany();
            log.info("工单轨迹信息上报开始");
            TrackinforCssReq trackinforCssReq = new TrackinforCssReq();
            trackinforCssReq.setIdentifier(identifier);
            trackinforCssReq.setLaunchCompany(launchCompany);
            trackinforCssReq.setForwardCompany(forwardCompany);

            // 根据工单号获取流程ID
            List<OrderTrackRecord> orderTrackRecordList = orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier);
            if (CollectionUtils.isEmpty(orderTrackRecordList)) {
                throw new RuntimeException("工单轨迹信息轨迹为空");
            }
            List<TrackinforIdentyLog> trackinforIdentyLogs = new ArrayList<>();
            for (OrderTrackRecord orderTrackRecord : orderTrackRecordList) {
                TrackinforIdentyLog trackinforIdentyLog = new TrackinforIdentyLog();
                BeanUtils.copyProperties(orderTrackRecord, trackinforIdentyLog);
                
                // 调整handingTime字段，增加10分钟来补偿服务器时间差
                String originalHandingTime = trackinforIdentyLog.getHandingTime();
                if (StringUtils.isNotBlank(originalHandingTime)) {
                    String adjustedHandingTime = adjustHandingTime(originalHandingTime);
                    trackinforIdentyLog.setHandingTime(adjustedHandingTime);
                    log.debug("调整处理时间，工单号：{}, 原时间：{}, 调整后：{}", identifier, originalHandingTime, adjustedHandingTime);
                }
                
                trackinforIdentyLogs.add(trackinforIdentyLog);
            }

            // HandlerLeadLevel  03 未对应 轨迹类型TrackType  02     轨迹至少有一条对应 重派单
//            boolean isOk = false;
//            for (TrackinforIdentyLog trackinforIdentyLog : trackinforIdentyLogs) {
//                String trackType = trackinforIdentyLog.getTrackType();
//                String handlerLeadLevel = trackinforIdentyLog.getHandlerLeadLevel();
//                if (StringUtils.isNotBlank(trackType) && StringUtils.isNotBlank(handlerLeadLevel)) {
//                    if ("02".equals(trackType) && "03".equals(handlerLeadLevel)) {
//                        isOk = true;
//                        break;
//                    }
//                }
//            }

//            if (!isOk) {
//                // 设置最后一行数据的 HandlerLeadLevel：03，轨迹类型TrackType：02
//                int lastIndex = trackinforIdentyLogs.size() - 1;
//                TrackinforIdentyLog trackinforIdentyLog = trackinforIdentyLogs.get(lastIndex);
//                trackinforIdentyLog.setHandlerLeadLevel("03");
//                trackinforIdentyLog.setTrackType("02");
//                trackinforIdentyLogs.set(lastIndex, trackinforIdentyLog);
//            }

            // 如果是不知情定制投诉任务单，则跳过设置最后一行数据的逻辑
            if (!isUnknownCustomComplaintTask(identifier)) {
                // 设置最后一行数据的 HandlerRank：03 HandlerLeadLevel：03，轨迹类型TrackType：02
                int lastIndex = trackinforIdentyLogs.size() - 1;
                TrackinforIdentyLog trackinforIdentyLog = trackinforIdentyLogs.get(lastIndex);
                trackinforIdentyLog.setHandlerLeadLevel("03");
                trackinforIdentyLog.setHandlerRank("03");
                trackinforIdentyLog.setTrackType("02");
                trackinforIdentyLogs.set(lastIndex, trackinforIdentyLog);
            } else {
                int lastIndex = trackinforIdentyLogs.size() - 1;
                TrackinforIdentyLog trackinforIdentyLog = trackinforIdentyLogs.get(lastIndex);
                trackinforIdentyLog.setTrackType("02");
                trackinforIdentyLogs.set(lastIndex, trackinforIdentyLog);
                log.info("不知情定制投诉任务单，跳过设置最后一行轨迹数据的逻辑，工单号：{}", identifier);
            }

            trackinforCssReq.setExtIdentylogList(trackinforIdentyLogs);
            // 上报集团
            String paramContent = JSON.toJSONString(trackinforCssReq);
            log.info("工单轨迹信息上报参数:" + paramContent);
            if (trackTaskLog != null) {
                trackTaskLog.setParamContent(paramContent);
            }
            HeaderResp headerResp = soundSendService.trackinforCssReq(trackinforCssReq);
            if (headerResp != null) {
                HeaderCodeResp response = headerResp.getResponse();
                String rspCode = response.getRspCode();
                String rspDesc = response.getRspDesc();
                if (trackTaskLog != null) {
                    trackTaskLog.setResponseCode(rspCode);
                    trackTaskLog.setResponseResult(rspDesc);
                }
                if (SoundConstant.GROUP_REPLY_SUCCESS.equals(rspCode)) {
                    log.info("工单轨迹信息上报成功" + identifier);
                } else {
                    throw new RuntimeException("工单轨迹信息上传失败");
                }
            }
        } catch (Exception e) {
            log.error("工单轨迹信息上报异常" + identifier);
            throw e;
        }
    }

    @Override
    public void handleCurrentNodeTrackinfor(ReplyCssReq cssReq, OrderTrackTaskLog trackTaskLog, String taskName, String taskId) {
        log.info("当前节点轨迹信息上报处理开始");
        String identifier = null;
        try {
            if (cssReq == null) {
                throw new RuntimeException("cssReq为空");
            }
            identifier = cssReq.getIdentifier();

            String launchCompany = cssReq.getLaunchCompany();
            String forwardCompany = cssReq.getForwardCompany();
            log.info("当前节点轨迹信息上报开始，工单号：{}, 任务名称：{}", identifier, taskName);
            
            TrackinforCssReq trackinforCssReq = new TrackinforCssReq();
            trackinforCssReq.setIdentifier(identifier);
            trackinforCssReq.setLaunchCompany(launchCompany);
            trackinforCssReq.setForwardCompany(forwardCompany);

            // 根据工单号和任务信息获取当前节点的轨迹记录
            List<OrderTrackRecord> currentNodeTrackRecords = orderTrackRecordMapper.getCurrentNodeTrackRecords(identifier, taskName, taskId);
            if (CollectionUtils.isEmpty(currentNodeTrackRecords)) {
                // 如果没有找到当前节点的轨迹，则获取最新的一条轨迹记录
                List<OrderTrackRecord> allTrackRecords = orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier);
                if (CollectionUtils.isNotEmpty(allTrackRecords)) {
                    // 获取最后一条轨迹记录作为当前节点轨迹
                    OrderTrackRecord lastRecord = allTrackRecords.get(allTrackRecords.size() - 1);
                    currentNodeTrackRecords = Arrays.asList(lastRecord);
                    log.info("未找到当前节点轨迹，使用最新轨迹记录，工单号：{}, 任务名称：{}", identifier, taskName);
                } else {
                    throw new RuntimeException("工单轨迹信息为空");
                }
            }

            List<TrackinforIdentyLog> trackinforIdentyLogs = new ArrayList<>();
            for (OrderTrackRecord orderTrackRecord : currentNodeTrackRecords) {
                TrackinforIdentyLog trackinforIdentyLog = new TrackinforIdentyLog();
                BeanUtils.copyProperties(orderTrackRecord, trackinforIdentyLog);
                
                // 调整handingTime字段，增加10分钟来补偿服务器时间差
                String originalHandingTime = trackinforIdentyLog.getHandingTime();
                if (StringUtils.isNotBlank(originalHandingTime)) {
                    String adjustedHandingTime = adjustHandingTime(originalHandingTime);
                    trackinforIdentyLog.setHandingTime(adjustedHandingTime);
                    log.debug("调整处理时间，工单号：{}, 原时间：{}, 调整后：{}", identifier, originalHandingTime, adjustedHandingTime);
                }
                
                trackinforIdentyLogs.add(trackinforIdentyLog);
            }

    /*        // 设置当前节点轨迹的处理级别和类型
            if (CollectionUtils.isNotEmpty(trackinforIdentyLogs)) {
                TrackinforIdentyLog currentLog = trackinforIdentyLogs.get(trackinforIdentyLogs.size() - 1);

                // 如果是不知情定制投诉任务单，则跳过设置处理级别的逻辑
                if (!isUnknownCustomComplaintTask(identifier)) {
                    currentLog.setHandlerLeadLevel("03");
                    currentLog.setHandlerRank("03");
                }
                currentLog.setTrackType("02"); // 设置轨迹类型为重派单

                log.info("设置当前节点轨迹处理级别，工单号：{}, 任务名称：{}", identifier, taskName);
            }*/

            trackinforCssReq.setExtIdentylogList(trackinforIdentyLogs);
            
            // 上报集团
            String paramContent = JSON.toJSONString(trackinforCssReq);
            log.info("当前节点轨迹信息上报参数:" + paramContent);
            if (trackTaskLog != null) {
                trackTaskLog.setParamContent(paramContent);
            }
            
            HeaderResp headerResp = soundSendService.trackinforCssReq(trackinforCssReq);
            if (headerResp != null) {
                HeaderCodeResp response = headerResp.getResponse();
                String rspCode = response.getRspCode();
                String rspDesc = response.getRspDesc();
                if (trackTaskLog != null) {
                    trackTaskLog.setResponseCode(rspCode);
                    trackTaskLog.setResponseResult(rspDesc);
                }
                if (SoundConstant.GROUP_REPLY_SUCCESS.equals(rspCode)) {
                    log.info("当前节点轨迹信息上报成功，工单号：{}, 任务名称：{}", identifier, taskName);
                } else {
                    throw new RuntimeException("当前节点轨迹信息上传失败");
                }
            }
        } catch (Exception e) {
            log.error("当前节点轨迹信息上报异常，工单号：{}, 任务名称：{}", identifier, taskName, e);
            throw e;
        }
    }

    /**
     * 调整处理时间，增加10分钟来补偿服务器时间差
     * 
     * @param originalHandingTime 原始处理时间，格式：YYYYMMDDHHMMSS
     * @return 调整后的处理时间
     */
    private String adjustHandingTime(String originalHandingTime) {
        try {
            if (StringUtils.isBlank(originalHandingTime) || originalHandingTime.length() != 14) {
                log.warn("处理时间格式不正确：{}", originalHandingTime);
                return originalHandingTime;
            }
            
            // 解析时间字符串 YYYYMMDDHHMMSS
            String year = originalHandingTime.substring(0, 4);
            String month = originalHandingTime.substring(4, 6);
            String day = originalHandingTime.substring(6, 8);
            String hour = originalHandingTime.substring(8, 10);
            String minute = originalHandingTime.substring(10, 12);
            String second = originalHandingTime.substring(12, 14);
            
            // 构建LocalDateTime对象
            LocalDateTime originalDateTime = LocalDateTime.of(
                Integer.parseInt(year),
                Integer.parseInt(month),
                Integer.parseInt(day),
                Integer.parseInt(hour),
                Integer.parseInt(minute),
                Integer.parseInt(second)
            );
            
            // 增加10分钟
            LocalDateTime adjustedDateTime = originalDateTime.plusMinutes(10);
            
            // 格式化回字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            String adjustedHandingTime = adjustedDateTime.format(formatter);
            
            return adjustedHandingTime;
            
        } catch (Exception e) {
            log.error("调整处理时间失败，原时间：{}", originalHandingTime, e);
            return originalHandingTime;
        }
    }

    @Override
    public List<OrderTrackRecord> queryTrackinforInfo(String identifier) {
        log.info("查询工单轨迹信息上报信息开始，工单号：{}", identifier);
        try {
            if (StringUtils.isBlank(identifier)) {
                throw new RuntimeException("工单编号不能为空");
            }

            // 根据工单编号获取所有轨迹列表
            List<OrderTrackRecord> orderTrackRecords = orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(identifier);
            if (CollectionUtils.isEmpty(orderTrackRecords)) {
                log.warn("未找到工单轨迹信息，工单号：{}", identifier);
                return new ArrayList<>();
            }

            log.info("查询工单轨迹信息上报信息成功，工单号：{}，轨迹数量：{}", identifier, orderTrackRecords.size());
            return orderTrackRecords;

        } catch (Exception e) {
            log.error("查询工单轨迹信息上报信息异常，工单号：{}", identifier, e);
            throw new RuntimeException("查询工单轨迹信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<OrderTrackRecord> queryTrackinforInfoByCondition(TrackinforQueryReq queryReq) {
        log.info("根据条件查询工单轨迹信息上报信息开始，工单号：{}，查询条件：{}", queryReq.getIdentifier(), JSON.toJSONString(queryReq));
        try {
            if (StringUtils.isBlank(queryReq.getIdentifier())) {
                throw new RuntimeException("工单编号不能为空");
            }

            // 根据条件查询轨迹列表
            List<OrderTrackRecord> orderTrackRecords = orderTrackRecordMapper.getOrderTrackRecordsByCondition(
                queryReq.getIdentifier(),
                queryReq.getTrackType(),
                queryReq.getStartTime(),
                queryReq.getEndTime()
            );

            if (CollectionUtils.isEmpty(orderTrackRecords)) {
                log.warn("未找到符合条件的工单轨迹信息，工单号：{}，查询条件：{}", queryReq.getIdentifier(), JSON.toJSONString(queryReq));
                return new ArrayList<>();
            }

            log.info("根据条件查询工单轨迹信息上报信息成功，工单号：{}，轨迹数量：{}", queryReq.getIdentifier(), orderTrackRecords.size());
            return orderTrackRecords;

        } catch (Exception e) {
            log.error("根据条件查询工单轨迹信息上报信息异常，工单号：{}，查询条件：{}", queryReq.getIdentifier(), JSON.toJSONString(queryReq), e);
            throw new RuntimeException("查询工单轨迹信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<TrackinforInfoResp> queryTrackinforInfoFormatted(String identifier) {
        log.info("查询工单轨迹信息上报信息（格式化）开始，工单号：{}", identifier);
        try {
            List<OrderTrackRecord> orderTrackRecords = queryTrackinforInfo(identifier);
            List<TrackinforInfoResp> respList = TrackinforInfoConverter.convertList(orderTrackRecords);
            log.info("查询工单轨迹信息上报信息（格式化）成功，工单号：{}，轨迹数量：{}", identifier, respList.size());
            return respList;
        } catch (Exception e) {
            log.error("查询工单轨迹信息上报信息（格式化）异常，工单号：{}", identifier, e);
            throw new RuntimeException("查询工单轨迹信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<TrackinforInfoResp> queryTrackinforInfoByConditionFormatted(TrackinforQueryReq queryReq) {
        log.info("根据条件查询工单轨迹信息上报信息（格式化）开始，工单号：{}，查询条件：{}", queryReq.getIdentifier(), JSON.toJSONString(queryReq));
        try {
            List<OrderTrackRecord> orderTrackRecords = queryTrackinforInfoByCondition(queryReq);
            List<TrackinforInfoResp> respList = TrackinforInfoConverter.convertList(orderTrackRecords);
            log.info("根据条件查询工单轨迹信息上报信息（格式化）成功，工单号：{}，轨迹数量：{}", queryReq.getIdentifier(), respList.size());
            return respList;
        } catch (Exception e) {
            log.error("根据条件查询工单轨迹信息上报信息（格式化）异常，工单号：{}，查询条件：{}", queryReq.getIdentifier(), JSON.toJSONString(queryReq), e);
            throw new RuntimeException("查询工单轨迹信息失败：" + e.getMessage());
        }
    }

}
