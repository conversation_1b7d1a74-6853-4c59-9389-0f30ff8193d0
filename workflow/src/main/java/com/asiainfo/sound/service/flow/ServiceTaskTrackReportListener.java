package com.asiainfo.sound.service.flow;

import com.asiainfo.sound.domain.OrderTrackTaskLog;
import com.asiainfo.sound.domain.enums.IdentyStatus;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.vo.FlowReplyVo;
import com.asiainfo.sound.service.OrderTrackTaskLogService;
import com.asiainfo.sound.service.UserAuthService;
import com.asiainfo.sound.service.WorkOrderTrackinforService;
import com.asiainfo.sound.util.AuthHelper;
import com.asiainfo.sound.util.SoundConstant;
import com.asiainfo.sound.util.SoundParamUtil;
import com.asiainfo.sound.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.Map;

/**
 * 服务任务轨迹上报监听器
 * 在服务任务执行完成后自动上报当前轨迹到集团
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ServiceTaskTrackReportListener implements ExecutionListener, ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void notify(DelegateExecution execution) {
        WorkOrderTrackinforService workOrderTrackinforService = applicationContext.getBean(WorkOrderTrackinforService.class);
        UserAuthService authService = applicationContext.getBean(UserAuthService.class);
        OrderTrackTaskLogService orderTrackTaskLogService = applicationContext.getBean(OrderTrackTaskLogService.class);
        
        String userId = AuthHelper.getUserName();
        Map<String, Object> variableMap = execution.getVariables();
        String identifier = String.valueOf(variableMap.get("identifier"));
        String activityName = execution.getEventName();
        
        log.info("督办单服务任务轨迹上报开始，工单号：{}, 服务任务名称：{}, 处理人：{}", identifier, activityName, userId);
        
        try {
            // 构建轨迹上报请求
            UserTokenQuery tokenQuery = new UserTokenQuery();
            tokenQuery.setUserId(userId);
            FlowReplyVo flowReplyVo = authService.queryFlowIdentyReplyInfo(tokenQuery);
            
            ReplyCssReq replyCssReq = new ReplyCssReq();
            BeanUtils.copyProperties(flowReplyVo, replyCssReq);
            replyCssReq.setIdentifier(identifier);
            
            String department = replyCssReq.getHandingDepartment();
            replyCssReq.setHandingDepartment(StringUtil.isEmpty(department) ? SoundConstant.DEPARTMENT : department);
            
            String handleRank = replyCssReq.getHandlerRank();
            replyCssReq.setHandlerRank(StringUtil.isEmpty(handleRank) ? SoundConstant.HAND_RANK : handleRank);
            
            replyCssReq.setForwardCompany(String.valueOf(variableMap.get("receiverUnit")));
            replyCssReq.setLaunchCompany("951");
            replyCssReq.setParaList(SoundParamUtil.getParaListByMap(variableMap, IdentyStatus.reply.name()));
            
            // 创建任务日志记录
            OrderTrackTaskLog trackTaskLog = new OrderTrackTaskLog();
            trackTaskLog.setIdentifier(identifier);
            trackTaskLog.setIdentyType(String.valueOf(variableMap.get("identyType")));
            trackTaskLog.setIdentySubtype(String.valueOf(variableMap.get("identySubtype")));
            trackTaskLog.setTrackStatus(190); // 190-待处理
            trackTaskLog.setCreateTime(new Date());
            
            // 使用事务同步机制确保在事务提交后执行轨迹上报
            TransactionSynchronizationManager.registerSynchronization(new TransactionSync<ReplyCssReq>(replyCssReq) {
                @Override
                public void afterCommit() {
                    try {
                        ReplyCssReq cssReq = this.getData();
                        // 先保存任务日志
                        orderTrackTaskLogService.save(trackTaskLog);
                        
                        // 执行当前节点轨迹上报
                        workOrderTrackinforService.handleCurrentNodeTrackinfor(cssReq, trackTaskLog, activityName, execution.getCurrentActivityId());
                        
                        // 更新任务日志状态为成功
                        trackTaskLog.setTrackStatus(200); // 200-可回复
                        trackTaskLog.setTrackResult("服务任务轨迹上报成功");
                        orderTrackTaskLogService.updateById(trackTaskLog);
                        
                        log.info("督办单服务任务轨迹上报成功，工单号：{}, 服务任务名称：{}", identifier, activityName);
                    } catch (Exception e) {
                        log.error("督办单服务任务轨迹上报失败，工单号：{}, 服务任务名称：{}", identifier, activityName, e);
                        
                        // 更新任务日志状态为失败
                        trackTaskLog.setTrackStatus(191); // 191-附件处理中（表示处理失败）
                        trackTaskLog.setTrackResult("服务任务轨迹上报失败：" + e.getMessage());
                        try {
                            orderTrackTaskLogService.updateById(trackTaskLog);
                        } catch (Exception ex) {
                            log.error("更新任务日志失败", ex);
                        }
                    }
                }
            });
            
        } catch (Exception e) {
            log.error("督办单服务任务轨迹上报处理异常，工单号：{}, 服务任务名称：{}", identifier, activityName, e);
        }
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ServiceTaskTrackReportListener.applicationContext = applicationContext;
    }
}