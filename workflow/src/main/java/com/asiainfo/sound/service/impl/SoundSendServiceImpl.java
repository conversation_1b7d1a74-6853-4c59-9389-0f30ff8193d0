package com.asiainfo.sound.service.impl;


import com.ai.srd.bd.common.utils.StringUtils;
import com.asiainfo.sound.dao.IdentyInfoDao;
import com.asiainfo.sound.daoFlow.SoundCssDao;
import com.asiainfo.sound.domain.dto.*;
import com.asiainfo.sound.domain.enums.FlowUserRole;
import com.asiainfo.sound.domain.enums.IdentyStatus;
import com.asiainfo.sound.domain.enums.OpinionCode;
import com.asiainfo.sound.domain.po.OperationNodeLog;
import com.asiainfo.sound.domain.query.IdentyPage;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.*;
import com.asiainfo.sound.domain.resp.AuthResp;
import com.asiainfo.sound.domain.resp.HeaderResp;
import com.asiainfo.sound.domain.vo.SelectBean;
import com.asiainfo.sound.domain.vo.UserVo;
import com.asiainfo.sound.service.*;
import com.asiainfo.sound.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:12
 * @desc 工单发送接口
 */
@Service
@Slf4j
public class SoundSendServiceImpl implements SoundSendService {

    @Autowired
    private ExternalService externalService;

    @Autowired
    private SoundCssDao cssDao;

    @Autowired
    private IdentyInfoDao identyInfoDao;

    @Autowired
    UserAuthService userAuthService;

    @Autowired
    private IdentyCommonService commonService;

    @Value("${sound.env.flag}")
    public String envFlag;

    //工单请求尝试次数,暂时这么多，先保证能够调用，后期数字小点。
    private static int tryTimes = 10;

    @Autowired
    private WorkOrderTrackinforService workOrderTrackinforService;

    @Override
    public HeaderResp sendDispatchTo(DispatchCssReq cssReq) {
        //去除多余元素
        cssReq.setIdentyState(null);
        cssReq.setIdentyStatus(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单下发第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单下发入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendDispatchTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("工单下发-集团对派发回复信息如下：{}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        //将一线声音回传集团操作类型存入表
        if ("03030201".equals(cssReq.getIdentyDetail())) {
            List<ParaList> paraList = cssReq.getParaList();
            log.info("paraList====>{}", paraList);
            String operationType = null;
            for (ParaList i : paraList) {
                if ("OperationType".equals(i.getParaID())) {
                    operationType = i.getParaVal();
                }
            }
            log.info("获取到的OperationType:{}", operationType);
            identyInfoDao.insertGroupHandleType(cssReq.getIdentifier(), operationType);
        }
        return headerResp;
    }

    @Override
    public HeaderResp sendReplyTo(ReplyCssReq cssReq) {
        if (cssReq != null) {
            cssReq.setIdentyState(null);
            if (StringUtil.isEmpty(cssReq.getHandingTime())) {
                cssReq.setHandingTime(SoundDateUtil.getNowDateTime());
            }
        }
        String content = JsonUtil.bean2Json(cssReq);
        log.info("工单回复内容 ：{}", content);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单回复第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单回复入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendReplyTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("工单回复-集团回复状态信息如下：{}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        ReplyCssDto dto = new ReplyCssDto();
        BeanUtils.copyProperties(cssReq, dto);
        //成功以后更新状态
        dto.setReplyMsg(headerResp.getResponse().getRspDesc());
        dto.setParaListStr(JsonUtil.bean2Json(cssReq.getParaList()));
        //获取额外信息里面的状态
        setReplyParamStatus(dto);
        String handlingOpinion = dto.getHandlingOpinion();
        if (StringUtils.isNotEmpty(handlingOpinion) && handlingOpinion.length() > 100) {
            handlingOpinion = handlingOpinion.substring(0,100);
            dto.setHandlingOpinion(handlingOpinion);
        }
        cssDao.insertIdentyHandlerHis(dto);
        dto.setIdentyState(IdentyStatus.reply.getValue());
        cssDao.modifyIdentyBasicState(dto);
        identyInfoDao.modifyIdentyBasicState(dto);
        return headerResp;
    }

    /**
     * 设置额外信息里面的状态
     *
     * @param dto
     */
    private void setReplyParamStatus(ReplyCssDto dto) {
        List<ParaList> paraLists = dto.getParaList();
        if (CollectionUtil.isEmpty(paraLists)) {
            return;
        }
        for (ParaList paraList : paraLists) {
            if ("Status".equals(paraList.getParaID())) {
                dto.setParamStatus(paraList.getParaVal());
                break;
            }
        }
    }

    @Override
    public HeaderResp sendStatementTo(StatementCssReq cssReq) {
        //判断一线声音回传 & 集团操作类型 0 1 设置转发省专为回复的省专
        String identifier = cssReq.getIdentifier();
        String forwardCompany03030201 = identyInfoDao.getFowardCompany(identifier);
        log.info("forwardCompany03030201:{}=========", forwardCompany03030201);
        if (StringUtils.isNotEmpty(forwardCompany03030201)) {
            //做归档处理，将ForwardCompany修改为回复的省专
            log.info("进行重新设置");
            cssReq.setForwardCompany(forwardCompany03030201);
        }
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单归档,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单归档入参 {}", JsonUtil.bean2Json(headerReq));
            //
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendStatementTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("工单归档-集团回复状态信息如下：{}", resultJson);
            log.info(headerResp.getResponse().getRspCode());
            if ("00000".equals(headerResp.getResponse().getRspCode())) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        //归档修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();

        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.statement.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
        return headerResp;
    }

    @Override
    public HeaderResp sendWithdrawTo(WithdrawCssReq cssReq) {
        cssReq.setIdentyState(null);
        cssReq.setWithdrawTime(SoundDateUtil.getNowDateTime());
        String content = JsonUtil.bean2Json(cssReq);
        HeaderResp headerResp = null;
        int times = tryTimes;
        for (int i = 1; i <= times; i++) {
            log.info("工单撤单,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单撤单入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendWithdrawTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("工单撤单-集团回复状态信息如下 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        //数据入库记录
        cssReq.setIdentyState(IdentyStatus.withdraw.getValue());
        cssDao.modifyIdentyBasicState(cssReq);
        identyInfoDao.modifyIdentyBasicState(cssReq);
        return headerResp;
    }

    @Override
    public HeaderResp sendQueryTo(QueryReq cssReq) {
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单查询,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单查询入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendQueryTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("集团回复状态信息如下 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }

        return headerResp;
    }

    @Override
    public HeaderResp sendReprocessTo(ReprocessCssReq cssReq) {
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        String identifier = cssReq.getIdentifier();
        String forwardCompany03030201 = identyInfoDao.getFowardCompany(identifier);
        if (StringUtils.isNotEmpty(forwardCompany03030201)) {
            //做归档处理，将ForwardCompany修改为回复的省专
            log.info("forwardCompany03030201:{}=========", forwardCompany03030201);
            cssReq.setForwardCompany(forwardCompany03030201);
        }
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单再处理,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单再处理入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendReprocessTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("集团回复状态信息如下 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        //入库数据操作
        cssReq.setIdentyState(IdentyStatus.reprocess.getValue());
        cssDao.modifyIdentyBasicState(cssReq);
        identyInfoDao.modifyIdentyBasicState(cssReq);

        return headerResp;
    }

    @Override
    public HeaderResp sendUrgeTo(UrgeCssReq cssReq) {
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单催办,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单催办入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.sendUrgeTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("集团回复状态信息如下 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }
        cssReq.setIdentyState(IdentyStatus.urge.getValue());
        cssDao.modifyIdentyBasicState(cssReq);
        identyInfoDao.modifyIdentyBasicState(cssReq);
        return headerResp;
    }

    @Override
    public HeaderResp currencyTo(CurrencyCssReq cssReq) {
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单通用接口,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单通用接口入参 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.currencyTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("集团回复状态信息如下 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }

        return headerResp;
    }

    @Override
    public HeaderResp syncDataTo(SyncDataCssReq cssReq) {
        cssReq.setIdentyState(null);
        log.info("业务参数为{}", JsonUtil.bean2Json(cssReq));
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单信息同步接口,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单信息同步参数 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.syncDataTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("集团返回参数 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }

        return headerResp;
    }

    @Override
    public HeaderResp TestjobTo(IdentyHeader cssReq) {
        cssReq.setIdentyState(null);
        String content = JsonUtil.bean2Json(cssReq);
        int times = tryTimes;
        HeaderResp headerResp = null;
        for (int i = 1; i <= times; i++) {
            log.info("工单测试删除同步接口,第(" + i + ")尝试。总共尝试(" + times + ")次。");
            HeaderReq headerReq = getHeaderReq(content);
            headerReq.setContent(content);
            headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
            log.info("工单测试删除参数 {}", JsonUtil.bean2Json(headerReq));
            headerResp = HttpUtils.stringToHeaderResp(externalService.testjobTo(headerReq));
            String resultJson = JsonUtil.bean2Json(headerResp);
            log.info("工单测试删除参数 {}", resultJson);
            if (!resultJson.contains("Request invalid or expired parameter token.")) {
                //如果成功，跳过循环，不成功再次尝试，一共10次
                break;
            }
        }

        return headerResp;
    }

    @Override
    public PageInfo<DispatchCssDto> getTaskList(IdentyPage identyPage) {
        PageHelper.startPage(identyPage.getCurrentPage(), identyPage.getPageSize());
        List<DispatchCssDto> dispatchCssDtos = cssDao.selectDispatchCssByCondition(identyPage, AuthHelper.getUserName());
        PageInfo<DispatchCssDto> pageInfo = new PageInfo(dispatchCssDtos);
        return pageInfo;
    }

    @Override
    public PageInfo<DispatchCssDto> getTaskListAll(IdentyPage identyPage) {
        String creator = "";
        UserTokenQuery userTokenQuery = new UserTokenQuery();
        userTokenQuery.setUserId(AuthHelper.getUserName());
        List<UserRoleDto> userRoleDtos = userAuthService.queryUserRoleInfos(userTokenQuery);
        List<String> userId = userRoleDtos.stream().filter(u -> FlowUserRole.admin.getValue().equals(u.getRoleId())).map(u -> u.getUserId()).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(userId)) {
            UserVo user = userAuthService.getUserById(AuthHelper.getUserName());
            creator = user.getUsername();
        }
        PageHelper.startPage(identyPage.getCurrentPage(), identyPage.getPageSize());
        List<DispatchCssDto> dispatchCssDtos = cssDao.selectDispatchCssByCondition(identyPage, creator);
        PageInfo<DispatchCssDto> pageInfo = new PageInfo(dispatchCssDtos);
        return pageInfo;
    }

    @Override
    public void sendByState(SendRequstDto sendRequstDto) {
        String state = sendRequstDto.getIdentyState();
        UserVo user = userAuthService.getUserById(AuthHelper.getUserName());
        if (IdentyStatus.statement.getValue().equals(state)) {//归档
            StatementCssReq cssReq = new StatementCssReq();
            cssReq.setIdentifier(sendRequstDto.getIdentifier());
            cssReq.setFilingOpinion(sendRequstDto.getOpinion());//意见传数值    00：满意   01：一般   02：不满意
            sendStatementTo(cssReq);
        } else if (IdentyStatus.withdraw.getValue().equals(state)) {//撤单
            WithdrawCssReq cssReq = new WithdrawCssReq();
            cssReq.setIdentifier(sendRequstDto.getIdentifier());
            cssReq.setWithdrawTime(SoundDateUtil.getNowDateTime());
            cssReq.setWithdrawReason(sendRequstDto.getOpinion());
            sendWithdrawTo(cssReq);
        } else if (IdentyStatus.reprocess.getValue().equals(state)) {//在处理
            ReprocessCssReq cssReq = new ReprocessCssReq();
            cssReq.setIdentifier(sendRequstDto.getIdentifier());
            cssReq.setCreator(user.getUsername());
            cssReq.setCreatorTel(user.getMobilePhone());
            cssReq.setReprocessingOpinion(sendRequstDto.getOpinion());
            sendReprocessTo(cssReq);
        } else if (IdentyStatus.urge.getValue().equals(state)) {//催办
            UrgeCssReq cssReq = new UrgeCssReq();
            cssReq.setIdentifier(sendRequstDto.getIdentifier());
            cssReq.setUrgeTime(SoundDateUtil.getNowDateTime());
            cssReq.setUrgeReason(sendRequstDto.getOpinion());
            sendUrgeTo(cssReq);
        }
        OperationNodeLog operationNodeLog = new OperationNodeLog();
        operationNodeLog.setIdentifier(sendRequstDto.getIdentifier());
        operationNodeLog.setHandingTime(LocalDateTime.now().toString());
        operationNodeLog.setHandler(user.getUsername());
        operationNodeLog.setIdentyState(state);
        operationNodeLog.setHandlingOpinion(sendRequstDto.getOpinion());
        if (IdentyStatus.statement.getValue().equals(state)) {
            operationNodeLog.setHandlingOpinion(OpinionCode.getOpinionCode(sendRequstDto.getOpinion()).getLabel());
        }
        operationNodeLog.setStateCode(IdentyStatus.getIdentyStatus(state).getLabel());
        log.info("节点日志记录的是: {}", IdentyStatus.getIdentyStatus(state).getLabel());
        cssDao.insertOperationNodeLog(operationNodeLog);
    }

    @Override
    public List<OperationNodeLog> findOperationNodeLogBy(QueryReq queryReq) {
        List<OperationNodeLog> operationNodeLogs = cssDao.findOperationNodeLogBy(queryReq.getIdentifier());
        if (CollectionUtil.isEmpty(operationNodeLogs)) {
            return operationNodeLogs;
        }
        //转换地市与省份的编码为名称
        Map<String, List<SelectBean>> stringListMap = commonService.queryIdentyAllCodes();
        List<SelectBean> unitCodes = stringListMap.get("unitCode");
        for (OperationNodeLog operationNodeLog : operationNodeLogs) {
            for (SelectBean unitCode : unitCodes) {
                if (unitCode.getValue().equals(operationNodeLog.getLaunchCompany())) {
                    operationNodeLog.setLaunchCompany(unitCode.getLabel());
                }
                if (unitCode.getValue().equals(operationNodeLog.getForwardCompany())) {
                    operationNodeLog.setForwardCompany(unitCode.getLabel());
                }
            }
        }
        return operationNodeLogs;
    }

    /**
     * 获取公共报文参数
     *
     * @return
     */
    private HeaderReq getHeaderReq(String content) {
/*        AuthReq req = new AuthReq();
        req.setAppId(SoundConstant.USER_PARTY_ID);
        req.setAppSecret(SoundConstant.APP_SECRET);
        TokenResp authResp = externalService.authinternal(req);*/
        String token = getToken();
        HeaderReq headerReq = new HeaderReq();
        headerReq.setTimeStamp(SoundParamUtil.getTimeStamp());
        headerReq.setTransIDO(SoundParamUtil.getTransIDO());
        headerReq.setSessionID(headerReq.getTransIDO());
        //缺少 sessionId
        headerReq.setEnvFlag(envFlag);
        headerReq.setAccessToken(token);
        headerReq.setSignMethod(SoundConstant.SIGN_METHOD);
        headerReq.setCutOffDay(SoundDateUtil.getNowDate());
        headerReq.setUserPartyID(SoundConstant.USER_PARTY_ID);
        headerReq.setDomain(SoundConstant.DOMAIN);
        headerReq.setVersion(SoundConstant.VERSION);
        headerReq.setRouteType("00");
        headerReq.setRouteValue(SoundConstant.JITUAN_CODE);
        headerReq.setBusType(SoundConstant.SOUND_CODE);
        return headerReq;
    }

    private String getToken() {
        AuthReq req = new AuthReq();
        req.setAppId(SoundConstant.USER_PARTY_ID);
        req.setAppSecret(SoundConstant.APP_SECRET);
        //TokenResp authResp = externalService.queryAccessToken();
        String s = externalService.authinternal(req);
        AuthResp resp = JsonUtil.json2Bean(s, AuthResp.class);
        String token = resp.getAccess_token();
        log.info("网状网获取token=========>" + resp.getAccess_token());
        //redisTemplate.opsForValue().set(TOKEN_KEY, token, TOKEN_TIME, TimeUnit.DAYS);
        //todo 生产没有创建该表sound_token_info
        //cssMapper.updateSoundToken(token, SoundDateUtil.getNowDateTime());
        return token;
    }

    //给定时任务用的， 每天凌晨刷新一次。
    @Override
    public String getExternalServiceToken() {
        String token = getToken();
        //log.info("网状网获得的token是: {}",token);
        return token;
    }

    @Override
    public HeaderResp trackinforCssReq(TrackinforCssReq cssReq) {
        if (cssReq != null) {
            cssReq.setIdentyState(null);
        }
        String content = JsonUtil.bean2Json(cssReq);
        log.info("工单轨迹上报信息 ：{}", content);
        HeaderReq headerReq = getHeaderReq(content);
        headerReq.setContent(content);
        headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET, JsonUtil.bean2Json(headerReq)));
        log.info("工单轨迹上报给集团的信息 {}", JsonUtil.bean2Json(headerReq));
        HeaderResp headerResp = HttpUtils.stringToHeaderResp(externalService.sendTrackinforTo(headerReq));
        log.info("集团返回状态信息如下：{}", JsonUtil.bean2Json(headerResp));
        return headerResp;
    }

    @Override
    public HeaderResp sendReturnTo(RejectCssReq cssReq) {
        String content = JsonUtil.bean2Json(cssReq);
        log.info("工单驳回内容 ：{}",content);
        HeaderReq headerReq = getHeaderReq(content);
        headerReq.setContent(content);
        headerReq.setSign(SignUtil.getSign(SoundConstant.APP_SECRET,JsonUtil.bean2Json(headerReq)));
        log.info("工单驳回入参 {}",JsonUtil.bean2Json(headerReq));
        HeaderResp headerResp = externalService.sendReturnTo(headerReq);
        log.info("集团返回驳回信息 {}",JsonUtil.bean2Json(headerResp));
        return headerResp;
    }

}
