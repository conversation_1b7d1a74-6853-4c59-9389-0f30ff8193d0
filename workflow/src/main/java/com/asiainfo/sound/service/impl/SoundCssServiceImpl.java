package com.asiainfo.sound.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.flow.dto.*;
import com.ai.flow.dto.enums.BooleanFilter;
import com.ai.flow.service.IFlowService;
import com.ai.srd.bd.common.exception.CustomException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.sound.dao.*;
import com.asiainfo.sound.daoFlow.IdentyLogDao;
import com.asiainfo.sound.daoFlow.SoundAssigneeCreatorDao;
import com.asiainfo.sound.daoFlow.SoundCssDao;
import com.asiainfo.sound.domain.HomeWideWarnStaff;
import com.asiainfo.sound.domain.UserGridInfo;
import com.asiainfo.sound.domain.dto.DispatchCssDto;
import com.asiainfo.sound.domain.dto.FlowSiteUserDto;
import com.asiainfo.sound.domain.dto.IdentyHeader;
import com.asiainfo.sound.domain.dto.ReplyCssDto;
import com.asiainfo.sound.domain.entity.SoundAssigneeCreator;
import com.asiainfo.sound.domain.entity.ZbCusRepairTaskDay;
import com.asiainfo.sound.domain.enums.*;
import com.asiainfo.sound.domain.po.IdentyLogPo;
import com.asiainfo.sound.domain.po.OperationNodeLog;
import com.asiainfo.sound.domain.query.UserTokenQuery;
import com.asiainfo.sound.domain.req.*;
import com.asiainfo.sound.domain.resp.*;
import com.asiainfo.sound.domain.vo.DispatchCssVo;
import com.asiainfo.sound.domain.vo.OtherSystemDispatchCssVo;
import com.asiainfo.sound.service.*;
import com.asiainfo.sound.service.CityUserConfigService;
import com.asiainfo.sound.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.asiainfo.sound.domain.enums.IdentyStatus.dispatch;

/**
 * <AUTHOR>
 * @date 2021/10/21 10:18
 * @desc 对接集团服务具体实现类
 */
@Service
@Slf4j
public class SoundCssServiceImpl implements SoundCssService {

    @Autowired
    private SoundCssDao cssDao;

    @Autowired
    private IdentyInfoDao identyInfoDao;

    @Autowired
    private IFlowService flowService;

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserAuthService authService;

    @Autowired
    IdentyLogDao identyLogDao;

    @Autowired
    private SoundSendService sendService;

    @Autowired
    private WorkflowService workflowService;

    @Resource
    private SoundAssigneeCreatorDao assigneeCreatorDao;

    @Resource
    private UserGridInfoService userGridInfoService;

    @Autowired
    private ZbCusRepairTaskDayMapper repairTaskDayMapper;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserGridInfoMapper userGridInfoMapper;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private OrderTrackRecordMapper orderTrackRecordMapper;

    @Autowired
    private LayoutService layoutService;

    @Autowired
    private CityUserConfigService cityUserConfigService;

    public static final String ALLOW_FORCE_ = "0487F81F-D4CE-4B33-9E8B-2D0995448F77-ALLOW-FORCE-JUMP";

    public static final String IDENTIFIER = "identifier";
    public static final String OTHER_SYSTEM_REPLY_URL = "othersystemReplyUrl";

    public static final String CREAT_TIME = "creatTime";

    public static final String FIRST_NODE_PERSON = "品质部项目经理";

    public static final String TITLE = "title";

    public static final String USER_ID = "userId";

    public static final String IDENTY_SUB_TYPE = "identySubtype";

    private static String key = "key";

    /**
     * 处理智能研判预警单(0024)工单回复的特殊参数需求
     * 当工单子类型为智能研判预警单（0024）时，工单回复请求参数ParaList中需增加MeasuresName字段
     * 
     * @param cssReq 工单回复请求对象
     */
    private void handleIntelligentJudgmentWarningReply(ReplyCssReq cssReq) {
        if (cssReq == null) {
            return;
        }
        
        // 获取工单子类型
        String identifier = cssReq.getIdentifier();
        if (StringUtils.isBlank(identifier)) {
            return;
        }
        
        try {
            // 查询工单的子类型
            String identySubtype = identyInfoDao.getIdentySubType(identifier);
            
            // 判断是否为智能研判预警单(0024)
            if (IdentySubtype.warning_24.getValue().equals(identySubtype)) {
                log.info("处理智能研判预警单(0024)工单回复，工单号：{}", identifier);
                
                // 确保ParaList不为空
                if (cssReq.getParaList() == null) {
                    cssReq.setParaList(new ArrayList<>());
                }
                
                // 检查是否已存在MeasuresName参数
                boolean hasMeasuresName = cssReq.getParaList().stream()
                    .anyMatch(para -> "MeasuresName".equals(para.getParaID()));
                
                // 如果不存在MeasuresName参数，则添加
                if (!hasMeasuresName) {
                    // 根据上传集团文件附件逻辑获取附件名称
                    String measuresName = extractMeasuresNameFromAttachments(cssReq);
                    
                    ParaList measuresNamePara = new ParaList();
                    measuresNamePara.setParaID("MeasuresName");
                    measuresNamePara.setParaVal(measuresName);
                    cssReq.getParaList().add(measuresNamePara);
                    
                    log.info("为智能研判预警单(0024)添加MeasuresName参数，工单号：{}，附件名称：{}", identifier, measuresName);
                }
            }
        } catch (Exception e) {
            log.error("处理智能研判预警单(0024)特殊参数时发生异常，工单号：{}，异常信息：{}", identifier, e.getMessage(), e);
            // 不抛出异常，避免影响正常的工单回复流程
        }
    }
    
    /**
     * 从附件信息中提取整改目标附件名称
     * 
     * @param cssReq 工单回复请求对象
     * @return 整改目标附件名称
     */
    private String extractMeasuresNameFromAttachments(ReplyCssReq cssReq) {
        String measuresName = "";
        
        try {
            // 优先使用attachNameList（附件实际名称）
            if (StringUtils.isNotBlank(cssReq.getAttachNameList())) {
                measuresName = cssReq.getAttachNameList();
                log.info("从attachNameList获取到附件名称：{}", measuresName);
            }
            // 如果attachNameList为空，则使用attachList（附件文件名）
            else if (StringUtils.isNotBlank(cssReq.getAttachList())) {
                measuresName = cssReq.getAttachList();
                log.info("从attachList获取到附件名称：{}", measuresName);
            }
            // 如果都为空，尝试从ParaList中查找附件相关参数
            else if (cssReq.getParaList() != null) {
                for (ParaList para : cssReq.getParaList()) {
                    // 查找可能的附件名称参数
                    if ("AttachmentName".equals(para.getParaID()) || 
                        "FileName".equals(para.getParaID()) ||
                        "DocumentName".equals(para.getParaID())) {
                        measuresName = para.getParaVal();
                        log.info("从ParaList参数{}获取到附件名称：{}", para.getParaID(), measuresName);
                        break;
                    }
                }
            }
            
            // 如果附件名称包含多个文件（用|分隔），取第一个作为整改目标附件名称
            if (StringUtils.isNotBlank(measuresName) && measuresName.contains("|")) {
                String[] attachNames = measuresName.split("\\|");
                measuresName = attachNames[0].trim();
                log.info("从多个附件中选择第一个作为整改目标附件名称：{}", measuresName);
            }
            
            // 如果是文件路径，提取文件名
            if (StringUtils.isNotBlank(measuresName) && (measuresName.contains("/") || measuresName.contains("\\"))) {
                String[] pathParts = measuresName.split("[/\\\\]");
                measuresName = pathParts[pathParts.length - 1];
                log.info("从文件路径中提取文件名：{}", measuresName);
            }
            
        } catch (Exception e) {
            log.error("提取附件名称时发生异常：{}", e.getMessage(), e);
            measuresName = ""; // 发生异常时返回空字符串
        }
        
        return StringUtils.isNotBlank(measuresName) ? measuresName : "";
    }

    /**
     * 对接工单派发的接口2
     *
     * @param req
     * @return
     */
    @Override
    public HeaderResp otherSystemSendDispatchTo(OtherSystemHeaderReq req) {

        log.info("外部系统工单派发的入参参数 {}", JsonUtil.bean2Json(req));

        HeaderResp headerResp = otherSystemGetHeaderResp(req);
        String content = req.getContent();
        if (StringUtil.isEmpty(content)) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        OtherSystemDispatchCssReq dispatchCssReq = JsonUtil.json2Bean(content, OtherSystemDispatchCssReq.class);
        if (req.getWorkOrderId() != null && req.getOtherSystemId() != null) {
            dispatchCssReq.setWorkOrderId(req.getWorkOrderId());
            dispatchCssReq.setOtherSystemId(req.getOtherSystemId());
        }
        if (dispatchCssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        try {
            //开始流程
            otherSystemStartDispatchProcess(content, dispatchCssReq);
        } catch (CustomException e) {
            headerResp.setResponse(RespUtil.getCustomError(e.getMessage()));
            return headerResp;
        }
        //记录工单
        otherSystemInsertDispatchIdentyBasic(dispatchCssReq);
        headerResp.setResponse(RespUtil.getSuccess());
        return headerResp;
    }

    private void otherSystemStartDispatchProcess(String content, OtherSystemDispatchCssReq dispatchCssReq) {
        //存入外部系统处理信息
        insertGroupIdentyLogByString(content, dispatch.getValue());
        OtherSystemDispatchCssVo dispatchCssVo = new OtherSystemDispatchCssVo();
        BeanUtils.copyProperties(dispatchCssReq, dispatchCssVo);
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(dispatchCssReq.getIdentySubtype());
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, identySubtype.name()).getId();
        UserTokenQuery tokenQuery = new UserTokenQuery();
        tokenQuery.setUserId(SoundConstant.PROVINCE_USER);
        dispatchCssVo.setType(0);
        dispatchCssVo.setNoticeSend("11");
        if (StrUtil.isEmpty(dispatchCssVo.getTitle())) {
            dispatchCssVo.setTitle(identySubtype.getLabel());
        }
        Map<String, Object> dispatchMap = dispatchCssVo.toMap();

        JSONObject jo = JSON.parseObject(content);
        //两类差错及反悔办理焦点投诉预警单
        if(IdentySubtype.warning_99.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleGridEvent(dispatchMap, jo);
        }
        //整体申告预警单
        if(IdentySubtype.warning_93.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleWarning93(dispatchMap, jo);
        }
        //重点产品投诉预警单
        if(IdentySubtype.warning_94.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleWarning94(dispatchMap, jo);
        }
        //重点政策投诉预警单
        if(IdentySubtype.warning_95.getValue().equals(dispatchCssReq.getIdentySubtype())){
            // 与整体申告预警单的处理人一致
            handleWarning95(dispatchMap, jo);
        }
        //升级投诉预警单
        if(IdentySubtype.warning_96.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleWarning96(dispatchMap, jo);
        }
        //不满客户修复单 8660 及 不满客户修复单(领导)8670
        if (IdentySubtype.repair_01.getValue().equals(dispatchCssReq.getIdentySubtype())
                || IdentySubtype.repair_02.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            dispatchMap.put(ALLOW_FORCE_, "false");
        }
        //重保客户修复单 根据地市选择不同的人员进行派发
        if (IdentySubtype.repair_99.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleZbRepair(dispatchMap,jo);
        }
        //不知情定制通报预警单 0053 添加抄送所需 用户名
        if (IdentySubtype.warning_53.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            handlePerson(dispatchMap, jo);
            switch (String.valueOf(jo.get("level"))) {
                case "1":
                    handleCase1(dispatchMap,jo);
                    break;
                case "2":
                    handleCase2(dispatchMap,jo);
                    break;
                case "3":
                    handleCase3(dispatchMap,jo);
                    break;
                default:
            }
        }
        //主动关怀发短信申请单 7560
        if (dispatchCssReq.getIdentySubtype().equals("8680")
                || IdentySubtype.apply_70.getValue().equals(dispatchCssReq.getIdentySubtype()) ||
                IdentySubtype.apply_71.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = new Date();
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(date);
            rightNow.add(Calendar.HOUR, 120);
            Date time = rightNow.getTime();
            String format = sdf.format(time);
            dispatchMap.put("processTime", format);
            //log.info("主动关怀发短信申请单 format {}", format);
        }
        dispatchMap.put(IDENTIFIER, dispatchCssReq.getWorkOrderId());
        dispatchMap.put(CREAT_TIME, SoundDateUtil.getNowDateTime());
        for (Map.Entry<String, Object> entry : jo.entrySet()) {
            if (entry.getKey().equals("firstNodePerson")) {
                dispatchMap.put(FIRST_NODE_PERSON, entry.getValue());
                continue;
            }
            //不满客户修复单 8660 及 不满客户修复单(领导)8670
            if ("name".equals(entry.getKey())
                    && (IdentySubtype.repair_01.getValue().equals(dispatchCssReq.getIdentySubtype())
                    || IdentySubtype.repair_02.getValue().equals(dispatchCssReq.getIdentySubtype()))) {
                dispatchMap.put(TITLE, entry.getValue());
                dispatchMap.put("name", entry.getValue());
                continue;
            }
            //zhuyao-这里做个兼容，由于from表单部分id为驼峰命名法，但是集团或者外部过来的是手写大写，这里统一将首写字母转换为小写。
            dispatchMap.put(StrUtil.lowerFirst(entry.getKey()), entry.getValue());
        }
        dispatchMap.putAll(SoundParamUtil.getVariablesMap(dispatchCssReq.getParaList()));
        String roleName = identySubtype.name() + SoundConstant.ATTACH_SUB + FlowTaskSite.accept.name();
        if (IdentySubtype.apply_70.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            //主动关怀发短信申请单 7560
            apply70Event(dispatchMap,jo.getString("userId"));
        } else if(IdentySubtype.apply_71.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            //互动调研发短信申请单 7561
            apply71Event(dispatchMap,jo.getString("userId"));
        } else if(IdentySubtype.warning_98.getValue().equals(dispatchCssReq.getIdentySubtype())
            ||IdentySubtype.handle_99.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            //通用版预警单(省内) 0098   通用版督办单(省内) 0199
            warning98Handle99Event(dispatchMap,jo.getString("targetType"));
        } else if(IdentySubtype.warning_97.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            //家宽省内预警单
            warning97Event(dispatchMap,jo.getString("cityName"),jo.getString("targetName"));
        } /*else if(IdentySubtype.warning_25.getValue().equals(dispatchCssReq.getIdentySubtype())) {
            //服务质量标准数据质量预警单
            handleWarning25(dispatchMap, jo);
        } */else {
            List<FlowSiteUserDto> userList = authService.queryUserInfoByRoleName(FlowUserRole.accept.getValue(), roleName);
            dispatchMap.put(SoundConstant.USER_LIST_STR, userList.stream().map(FlowSiteUserDto::getUserName).collect(Collectors.toList()));
        }
        flowService.startProcess(processDefinitionProcessId, null, dispatchMap);
    }
    /**
     * 根据网格查询对应的处理人并存入变量
     * @param dispatchMap
     * @param jo
     */
    private void handleGridEvent(Map<String, Object> dispatchMap, JSONObject jo) {
        String gridId = String.valueOf(jo.get("gridId"));
        UserGridInfo userGridInfo = userGridInfoService.queryInfoById(gridId);
        if (ObjectUtil.isNotEmpty(userGridInfo)){
            log.info("当前网格为:{}",userGridInfo.getGridName());
            String gridManager = userGridInfo.getGridManager();
            String districtManager = userGridInfo.getGridAuditor();
            String secondDistrictManager = userGridInfo.getSecondGridAuditor();
            String gridReaders = userGridInfo.getGridReaders();
            if (StringUtil.isNullOrEmpty(gridManager)
                    || StringUtil.isNullOrEmpty(districtManager)
                    || StringUtil.isNullOrEmpty(secondDistrictManager)
                    || StringUtil.isNullOrEmpty(gridReaders)){
                log.info("当前网格{}查询到的网格员:{},审核人:{},第二审核人:{},审阅人:{}",userGridInfo.getGridName(),gridManager,districtManager,secondDistrictManager,gridReaders);
                throw new CustomException("当前网格人员缺失!");
            }
            dispatchMap.put("gridManager",gridManager);
            dispatchMap.put("districtManager",districtManager);
            dispatchMap.put("secondDistrictManager",secondDistrictManager);
            List<String> users = new ArrayList<>();
            users.add(gridReaders);
            StringBuilder sb = new StringBuilder();
            //week 2023-02-13|2023-02-19
            String week = String.valueOf(jo.get("week"));
            String[] split = week.split("\\|");
            String[] split1 = split[0].split("-");
            String[] split2 = split[1].split("-");
            StringBuilder sb2 = new StringBuilder();
            sb2.append(split1[0] + "年" + split1[1] + "月" + split1[2] + "日-");
            sb2.append(split2[1] + "月" + split2[2] + "日，");
            sb.append(sb2.toString());
            sb.append("您所负责网格的");
            String errorType = String.valueOf(jo.get("errorType"));
            sb.append("1".equals(errorType)?"两类差错":"反悔办理");
            String zf = String.valueOf(jo.get("zf"));
            sb.append("类投诉出现异动，环比增幅"+zf+"%。");
            //todo 报表名称
            sb.append("具体可通过报表");
            sb.append("1".equals(errorType)?"两类差错明细表":"反悔办理明细表");
            sb.append("进行核查，请及时处理，谢谢。");
            dispatchMap.put("content",sb.toString());
            log.info("=======content:{}",sb.toString());
            log.info("=======网格:{}抄送的阅知人为:{}",userGridInfo.getGridName(),userGridInfo.getGridReaders());
            dispatchMap.put("ccUsers",users);
            dispatchMap.put("repeatTrigger",jo.getIntValue("'repeatTrigger'"));
        }else {
            log.info("当前网格id:{}未找到对应的网格长",gridId);
        }
    }

    private void  warning98Handle99Event(Map<String, Object> dispatchMap,String targetType) {
        List<String> userIdList = new ArrayList<>();
        if ("家庭宽带业务".equals(targetType)){
            userIdList.add("zhaoxue");
        }else if ("企业宽带".equals(targetType)){
            userIdList.add("liyan4");
        }else if ("专线服务".equals(targetType)){
            userIdList.add("liyan4");
        }else if ("智慧家庭业务".equals(targetType)){
            userIdList.add("zhaoxue");
        }
        dispatchMap.put(SoundConstant.USER_LIST_STR, userIdList);
    }

    /**
     * 处理服务质量标准数据质量预警单
     * @param dispatchMap 派发参数
     * @param jo JSON对象
     */
    private void handleWarning25(Map<String, Object> dispatchMap, JSONObject jo) {
        log.info("处理服务质量标准数据质量预警单，参数：{}", jo.toJSONString());

        // 设置省专公司处理人员
        String provinceCode = jo.getString("provinceCode");
        String targetType = jo.getString("targetType");

        List<String> userIdList = new ArrayList<>();

        // 根据省份和业务类型分配处理人员
        if (StringUtils.isNotBlank(provinceCode)) {
            // 这里可以根据实际业务需求配置不同省份的处理人员
            // 目前使用默认处理人员
            if ("数据质量".equals(targetType)) {
                userIdList.add("dataQualityUser"); // 数据质量专员
            } else if ("服务质量".equals(targetType)) {
                userIdList.add("serviceQualityUser"); // 服务质量专员
            } else {
                userIdList.add("defaultUser"); // 默认处理人员
            }
        } else {
            // 如果没有指定省份，使用默认处理人员
            userIdList.add("defaultUser");
        }

        dispatchMap.put(SoundConstant.USER_LIST_STR, userIdList);

        // 设置工单回复相关参数（步骤2）
        dispatchMap.put("provinceUser", userIdList.get(0));

        // 设置省内审核相关参数（步骤3）
        dispatchMap.put("reviewManager", getReviewManager(provinceCode)); // 省专三级经理

        // 设置工单归档相关参数（步骤4）
        dispatchMap.put("archiveUser", "systemArchive"); // 系统自动归档
        dispatchMap.put("autoArchive", true); // 启用自动归档

        // 设置API调用相关参数
        dispatchMap.put("replyApiUrl", "/api/warning/reply"); // 工单回复API
        dispatchMap.put("archiveApiUrl", "/api/warning/archive"); // 工单归档API
        dispatchMap.put("archiveSyncApiUrl", "/api/warning/archiveSync"); // 归档结果同步API
        dispatchMap.put("fileUploadApiUrl", "/api/warning/fileUpload"); // 文件接口补传数据API

        // 设置归档同步相关参数
        dispatchMap.put("syncToProvince", true); // 启用省专公司同步
        dispatchMap.put("syncApiTimeout", 30000); // API超时时间（毫秒）

        log.info("服务质量标准数据质量预警单处理完成，分配给用户：{}，审核经理：{}", userIdList, getReviewManager(provinceCode));
    }

    /**
     * 根据省份获取审核经理
     * @param provinceCode 省份代码
     * @return 审核经理用户名
     */
    private String getReviewManager(String provinceCode) {
        // 这里可以根据实际业务需求配置不同省份的三级经理
        // 目前使用默认配置
        if (StringUtils.isNotBlank(provinceCode)) {
            switch (provinceCode) {
                case "BJ":
                    return "bjReviewManager";
                case "SH":
                    return "shReviewManager";
                case "GD":
                    return "gdReviewManager";
                default:
                    return "defaultReviewManager";
            }
        }
        return "defaultReviewManager";
    }

    private List<String> targetNameList = Arrays.asList("营业厅用后即评", "移动业务满意度-营销宣传-省内CATI", "移动业务满意度-营业厅服务-集团CATI", "移动业务满意度-营销宣传-集团CATI");

    private void  warning97Event(Map<String, Object> dispatchMap,String cityName,String targetName) {
        HomeWideWarnStaff staff = userGridInfoMapper.selectHomeWideWarnStaff(cityName);
        if (staff == null) {
            log.error("在home_wide_warn_staff表中未找到预警单对应的区域:" + cityName);
            throw new CustomException("在home_wide_warn_staff表中未找到预警单对应的区域:" + cityName);
        }
        String loginName = userDao.selectHomeWideWarnUserInfo(staff.getPhone());
        if (loginName == null) {
            log.error("对应区域的预警单接收人在dim_lkg_staff表中不存在:" + staff.getPhone() + "---" + staff.getName());
            throw new CustomException("对应区域的预警单接收人在dim_lkg_staff表中不存在:" + staff.getPhone() + "---" + staff.getName());
        }
        List<String> userIdList = new ArrayList<>();
        userIdList.add(loginName);
        dispatchMap.put(SoundConstant.USER_LIST_STR, userIdList);

        //targetNameList中的指标给马婷婷,其它的都给赵雪
        dispatchMap.put("lastUser", targetNameList.indexOf(targetName) > -1 ? "matingting" : "zhaoxue");
    }

    private List<String> cityList = Arrays.asList("石嘴山分公司", "吴忠分公司", "固原分公司", "中卫分公司", "银川分公司");

    private void apply70Event(Map<String, Object> dispatchMap,String userId) {
//        String creatorOrgName = userDao.selectOrgName(userId);
//        String postName = "三级经理";
//        List<SelectBean> assigneeList = authService.getAssigneeList(creatorOrgName, postName);
//        List<String> userIdList = assigneeList.stream().map(x -> x.getValue()).collect(Collectors.toList());
//        dispatchMap.put(SoundConstant.USER_LIST_STR, userIdList);
        dispatchMap.put("sendUser", userId);
    }
    private void apply71Event(Map<String, Object> dispatchMap, String userId) {
        String creatorOrgName = userDao.selectOrgName(userId);
        if (StringUtil.isEmpty(creatorOrgName)) {
            creatorOrgName = "品质管理部";
        }
        int path;
//        String postName;
        if ("品质管理部".equals(creatorOrgName)) {
            //如果发起人是品质管理部的,则第一个审批节点为 品质管理部项目经理(项目经理就是负责人)
            path = 3;
//            postName = "负责人";
        }else if(cityList.indexOf(creatorOrgName) > -1){
            //如果发起人是地市分公司的,则第一个审批节点为 市场经营部 三级经理
            path = 1;
//            creatorOrgName = "市场经营部";
//            postName = "三级经理";
        }else {
            //如果发起人是其它部门的,则第一个审批节点为 业务部门项目经理(项目经理就是负责人)
            path = 2;
//            postName = "负责人";
        }
//        List<SelectBean> assigneeList = authService.getAssigneeList(creatorOrgName, postName);
//        List<String> userIdList = assigneeList.stream().map(x -> x.getValue()).collect(Collectors.toList());
        dispatchMap.put(SoundConstant.USER_LIST_STR, Collections.singletonList(userId));
        dispatchMap.put("path",path);
    }

    private void handlePerson(Map<String, Object> dispatchMap, JSONObject jo) {
        if ("银川".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            dispatchMap.put("areaHandler","wangquan");
        }
        if ("石嘴山".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            dispatchMap.put("areaHandler","chenhongling");
        }
        if ("吴忠".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            dispatchMap.put("areaHandler","liuyuan");
        }
        if ("固原".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            dispatchMap.put("areaHandler","lixiaojun");
        }
        if ("中卫".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            dispatchMap.put("areaHandler","yangrongrong");
        }
    }

    private void handleWarning93(Map<String, Object> dispatchMap, JSONObject jo) {
        handleWarningByConfigType(dispatchMap, jo, "warning_93", "整体申告预警单(warning_93)");
    }

    /**
     * 处理Warning94类型的预警单
     */
    private void handleWarning94(Map<String, Object> dispatchMap, JSONObject jo) {
        handleWarningByConfigType(dispatchMap, jo, "warning_94", "Warning94预警单");
    }

    /**
     * 处理Warning95类型的预警单
     */
    private void handleWarning95(Map<String, Object> dispatchMap, JSONObject jo) {
        handleWarningByConfigType(dispatchMap, jo, "warning_95", "Warning95预警单");
    }

    /**
     * 处理Warning96类型的预警单（升级投诉预警单）
     */
    private void handleWarning96(Map<String, Object> dispatchMap, JSONObject jo) {
        handleWarningByConfigType(dispatchMap, jo, "warning_96", "升级投诉预警单(warning_96)");
    }

    /**
     * 通用的预警单处理方法
     * @param dispatchMap 分发映射
     * @param jo JSON对象
     * @param configType 配置类型
     * @param warningTypeName 预警类型名称（用于日志）
     */
    private void handleWarningByConfigType(Map<String, Object> dispatchMap, JSONObject jo, String configType, String warningTypeName) {
        String cityName = String.valueOf(jo.get("cityName"));
        if (StringUtils.isBlank(cityName)) {
            return;
        }

        // 从通用配置表中读取指定类型的用户配置
        String userId = cityUserConfigService.getUserIdByConfigTypeAndCityName(configType, cityName);
        if (StringUtils.isNotBlank(userId)) {
            dispatchMap.put("cityUser", userId);
            log.info("{} - 城市: {}, 分配给用户: {}", warningTypeName, cityName, userId);
        } else {
            log.warn("{} - 未找到城市 {} 对应的用户配置", warningTypeName, cityName);
        }
    }

/*    private void handleWarning94(Map<String, Object> dispatchMap, JSONObject jo) {
        String cityName = String.valueOf(jo.get("cityName"));
        if (StringUtils.isBlank(cityName)) {
            return;
        }
        if (cityName.startsWith("银川")){
            dispatchMap.put("cityUser","liufang");
        }
        if (cityName.startsWith("石嘴山")){
//            dispatchMap.put("cityUser","zhouyuru");
            dispatchMap.put("cityUser","pq_kouxiaoju");
        }
        if (cityName.startsWith("吴忠")){
            dispatchMap.put("cityUser","pq_wangxianjun");
        }
        if (cityName.startsWith("固原")){
            dispatchMap.put("cityUser","renwei1");
        }
        if (cityName.startsWith("中卫")){
            dispatchMap.put("cityUser","pq_heyingxia");
        }
        if (cityName.startsWith("互联网销售分公司")){
            dispatchMap.put("cityUser","baojianming");
        }
        if (cityName.startsWith("品质")){
            dispatchMap.put("cityUser","pq_zhouzhaoqin");
        }
    }*/

  /*  private void handleWarning95(Map<String, Object> dispatchMap, JSONObject jo) {
        String cityName = String.valueOf(jo.get("cityName"));
        if (StringUtils.isBlank(cityName)) {
            return;
        }
        if (cityName.startsWith("银川")){
            dispatchMap.put("cityUser","liufang");
        }
        if (cityName.startsWith("石嘴山")){
//            dispatchMap.put("cityUser","zhouyuru");
            dispatchMap.put("cityUser","pq_kouxiaoju");
        }
        if (cityName.startsWith("吴忠")){
            dispatchMap.put("cityUser","pq_wangxianjun");
        }
        if (cityName.startsWith("固原")){
            dispatchMap.put("cityUser","renwei1");
        }
        if (cityName.startsWith("中卫")){
            dispatchMap.put("cityUser","pq_heyingxia");
        }

        if (cityName.startsWith("互联网销售分公司")){
            dispatchMap.put("cityUser","baojianming");
        }
        if (cityName.startsWith("品质")){
            dispatchMap.put("cityUser","pq_zhouzhaoqin");
        }

    }*/



    private void handleZbRepair(Map<String, Object> dispatchMap,JSONObject jo) {
        String city = String.valueOf(jo.get("city"));
        if ("银川".equals(city)){
            dispatchMap.put("areaHandler", "xushuya");
        }else if ("石嘴山".equals(city)){
            dispatchMap.put("areaHandler", "pq_kouxiaoju");
        }else if ("吴忠".equals(city)){
            dispatchMap.put("areaHandler", "renshuo0");
        }else if ("固原".equals(city)){
            dispatchMap.put("areaHandler", "guoqingling1");
            //todo 姓名检查
        }else if ("中卫".equals(city)){
            dispatchMap.put("areaHandler", "pq_heyingxia");
        }
    }

    private void handleCase3(Map<String, Object> dispatchMap, JSONObject jo) {
        //todo 账号问题处理
        List<String> users = new ArrayList<>();
        if ("银川".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhoujianhua");
            users.add("yangli");
            users.add("yinwenjun");
        }
        if ("石嘴山".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhouyuru");
            users.add("lvyanqin");
            users.add("wangzhening");
        }
        if ("吴忠".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("heling");
            users.add("xurong1");
            users.add("jiangan");
        }
        if ("固原".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("lili3");
            users.add("zhangshili");
            users.add("dongxin1");
        }
        if ("中卫".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("maxuerong");
            users.add("jiangyan");
            users.add("guowenshe");
        }
        dispatchMap.put("ccUsers",users);
    }

    private void handleCase2(Map<String, Object> dispatchMap, JSONObject jo) {
        List<String> users = new ArrayList<>();
        if ("银川".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhoujianhua");
            users.add("yangli");
            users.add("yinwenjun");
        }
        if ("石嘴山".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhouyuru");
            users.add("lvyanqin");
            users.add("wangzhening");
        }
        if ("吴忠".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("heling");
            users.add("xurong");
            users.add("jiangan");
        }
        if ("固原".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("lili3");
            users.add("zhangshili");
            users.add("dongxin1");
        }
        if ("中卫".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("maxuerong");
            users.add("jiangyan");
            users.add("guowenshe");
        }
        dispatchMap.put("ccUsers",users);
    }

    private void handleCase1(Map<String, Object> dispatchMap, JSONObject jo) {
        List<String> users = new ArrayList<>();
        users.add("sufeng");
        users.add("wenxue");
        users.add("zhangpeng");
        users.add("lihao");
        if ("银川".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhoujianhua");
            users.add("yangli");
            users.add("yinwenjun");
        }
        if ("石嘴山".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("zhouyuru");
            users.add("lvyanqin");
            users.add("wangzhening");
        }
        if ("吴忠".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("heling");
            users.add("xurong");
            users.add("jiangan");
        }
        if ("固原".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("lili3");
            users.add("zhangshili");
            users.add("dongxin1");
        }
        if ("中卫".equals(String.valueOf(jo.get("trnsNumBelgCity")))){
            users.add("maxuerong");
            users.add("jiangyan");
            users.add("guowenshe");
        }
        dispatchMap.put("ccUsers",users);
    }

    public List<String> getAssigneeId(String userId, String identySubtype) {
        LambdaQueryWrapper<SoundAssigneeCreator> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SoundAssigneeCreator::getCreatorId, userId);
        queryWrapper.eq(SoundAssigneeCreator::getIdentysubType, identySubtype);
        List<SoundAssigneeCreator> soundAssigneeCreators = assigneeCreatorDao.selectList(queryWrapper);
        return soundAssigneeCreators.stream().map(SoundAssigneeCreator::getAssigneeId).collect(Collectors.toList());
    }

    private void otherSystemInsertDispatchIdentyBasic(OtherSystemDispatchCssReq dispatchCssReq) {
        DispatchCssDto dispatchDto = new DispatchCssDto();
        BeanUtils.copyProperties(dispatchCssReq, dispatchDto);
        dispatchDto.setUserId(dispatchCssReq.getUserId());
        dispatchDto.setIdentifier(dispatchCssReq.getWorkOrderId());
        dispatchDto.setIdentyType(dispatchCssReq.getIdentyType());
        dispatchDto.setIdentySubtype(dispatchCssReq.getIdentySubtype());
        dispatchDto.setSenderFlag(SoundConstant.OTHER_SYSTEM);
        dispatchDto.setIdentyState(dispatch.getValue());
        dispatchDto.setCreatDate(SoundDateUtil.getDate());
        cssDao.insertIdentyBasicInfo(dispatchDto);
//        identyInfoDao.insertIdentyBasicInfo(dispatchDto);
    }

    private HeaderResp otherSystemGetHeaderResp(OtherSystemHeaderReq req) {
        HeaderResp headerResp = new HeaderResp();
        headerResp.setTransIDH(SoundParamUtil.getTransIDO());
        headerResp.setCutOffDay(SoundDateUtil.getNowDate());
        headerResp.setTransIDHTime(SoundDateUtil.getNowDateTime());
        headerResp.setResponse(RespUtil.getSuccess());
        return headerResp;
    }

    @Override
    public HeaderResp sendDispatchTo(HeaderReq req) {
        log.info("工单派发的入参参数 {}", JsonUtil.bean2Json(req));
        //存入集团处理信息
        insertGroupIdentyLog(req, dispatch.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        if (StringUtil.isEmpty(content)) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        DispatchCssReq dispatchCssReq = JsonUtil.json2Bean(content, DispatchCssReq.class);
        if (dispatchCssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        String identySubType = dispatchCssReq.getIdentySubtype();
        if(StringUtils.equals(identySubType,IdentySubtype.apply_02.getValue())){
            String identifier = dispatchCssReq.getIdentifier();
            HistoricProcessInstance task = layoutService.getTaskId(identifier);
            if(task != null){
                DispatchCssVo dispatchCssVo = new DispatchCssVo();
                BeanUtils.copyProperties(dispatchCssReq,dispatchCssVo);
                Map<String,Object> variMap = dispatchCssVo.toMap();
                variMap.putAll(SoundParamUtil.getVariablesMap(dispatchCssReq.getParaList()));
                String activityId = IdentySubtype.getIdentySubtype(identySubType).name() + SoundConstant.ATTACH_SUB + SoundConstant.REPLY_FLAG;
                String excuteId = flowService.getExecutionId(task.getProcessInstanceId(),activityId);
                variMap.put(SoundConstant.REPLY_FLAG,SoundConstant.NOT);
                flowService.setExecutionVariables(excuteId,variMap);
                flowService.triggerExecution(excuteId);
                headerResp.setResponse(RespUtil.getSuccess());
                return headerResp;
            }
        }

        try {
            //开始流程
            startDispatchProcess(content, dispatchCssReq);
        } catch (Exception e) {
            log.info("派单异常=》"+e.getMessage(),e);
            log.error(e.getMessage(),e);
            try {
                //如果是内部投诉升级任务单抛出的异常,则直接给集团返回成功,其它异常不变,该返回什么就是什么
                //内部投诉升级任务单很特殊,就算省内出了一些异常,也要给集团返回成功,意思就是这个单子就不用管了,集团也不会追究
                //判断是不是内部投诉升级任务单的条件是,异常种类是不是CustomException,并且异常code是不是IdentySubtype.task_12的value,详见handleTask12()方法
                CustomException c = (CustomException)e;
                if (Integer.valueOf(IdentySubtype.task_12.getValue()).equals(c.getCode())) {
                    headerResp.setResponse(RespUtil.getSuccess());
                }else {
                    headerResp.setResponse(RespUtil.getCustomError(e.getMessage()));
                }
            } catch (ClassCastException classCastException) {
                headerResp.setResponse(RespUtil.getCustomError(e.getMessage()));
            }
            return headerResp;
        }
        //记录工单
        insertDispatchIdentyBasic(dispatchCssReq);

        headerResp.setResponse(RespUtil.getSuccess());
        return headerResp;
    }


    private void startDispatchProcess(String content, DispatchCssReq dispatchCssReq) {
        DispatchCssVo dispatchCssVo = new DispatchCssVo();
        BeanUtils.copyProperties(dispatchCssReq, dispatchCssVo);
        IdentySubtype identySubtype = IdentySubtype.getIdentySubtype(dispatchCssReq.getIdentySubtype());
        String processDefinitionProcessId = flowService.getLatestProcessDefinition(null, identySubtype.name()).getId();
        UserTokenQuery tokenQuery = new UserTokenQuery();
        tokenQuery.setUserId(SoundConstant.PROVINCE_USER);
        dispatchCssVo.setType(1);
        dispatchCssVo.setNoticeSend("11");
        //dispatchCssVo.setUserName(tokenQuery.getUserId());
        Map<String, Object> dispatchMap = dispatchCssVo.toMap();
        dispatchMap.putAll(SoundParamUtil.getVariablesMap(dispatchCssReq.getParaList()));
        String roleName = identySubtype.name() + SoundConstant.ATTACH_SUB + FlowTaskSite.accept.name();
        List<FlowSiteUserDto> userList = authService.queryUserInfoByRoleName(FlowUserRole.accept.getValue(), roleName);
        dispatchMap.put(SoundConstant.USER_LIST_STR, userList.stream().map(FlowSiteUserDto::getUserName).collect(Collectors.toList()));
        if(IdentySubtype.task_12.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleTask12(dispatchMap,dispatchCssReq);
        }
        if(IdentySubtype.task_09.getValue().equals(dispatchCssReq.getIdentySubtype())){
            handleTask09(dispatchMap,dispatchCssReq);
        }
        flowService.startProcess(processDefinitionProcessId, null, dispatchMap);
        // 通用督办单任务，记录加密信息到缓存，定时任务获取缓存后解压文件
        cacheService.recordDispatchWithSecretKey(dispatchMap);
    }

    private void handleTask12(Map<String, Object> dispatchMap,DispatchCssReq dispatchCssReq) {
        String identifier = String.valueOf(dispatchMap.get("identifier"));
        int result = orderTrackRecordMapper.getIdentifierCount(identifier);
        if (result > 0) {
            throw new CustomException("重复的 投诉内部升级任务单",Integer.valueOf(IdentySubtype.task_12.getValue()));
        }
        List<ParaList> paraList = dispatchCssReq.getParaList();
        Map<String, String> map = paraList.stream().collect(Collectors.toMap(ParaList::getParaID, ParaList::getParaVal));
        String group = map.get("DspsorgBrnchNm");
        String level = map.get("ExtEarlyWarningLevel");
        //根据部门来判断发给谁,所以dept不能为空
        if (StringUtils.isBlank(group)) {
            throw new CustomException("投诉处理部门为空!",Integer.valueOf(IdentySubtype.task_12.getValue()));
        }
        if ("无".equals(group)) {
            throw new CustomException("未找到对应的工作组!====无",Integer.valueOf(IdentySubtype.task_12.getValue()));
        }
        //流程图中根据升级级别来判断走不同的线程,所以level不能为空
        //level的值为01 02 03 04,分别对应初级,中级,高级,特级
        if (StringUtils.isBlank(level)) {
            throw new CustomException("升级级别为空!");
        }
        String[] strArr = group.split("\\|");
        group = strArr[0].trim();

        Function<String,String> groupToDept = (groupName) -> {
            String dept = userGridInfoMapper.selectDeptTask12(groupName);
            if(StringUtil.isEmpty(dept)){
                throw new CustomException("未找到对应的工作组!====" + groupName,Integer.valueOf(IdentySubtype.task_12.getValue()));
            }
            return dept;
        };

        String dept;
        if (group.toLowerCase().contains("eoms")) {
            String serviceRequest = map.get("ServiceRequest");
            if (StringUtils.isNotBlank(serviceRequest) && (serviceRequest.startsWith("家宽业务→") || serviceRequest.startsWith("家庭业务→"))) {
                //如果工作组是 eoms,并且 工单服务请求分类名称以 '家宽业务→' '家庭业务→' 开头的,都给市场部2
                dept = "市场部2";
            } else {
                dept = groupToDept.apply(group);
            }
        } else if (group.startsWith("拓方")) {
            //拓方开头的都是在线
            dept = "在线";
        } else {
            dept = groupToDept.apply(group);
        }

        Map<String,String> userInfo = userGridInfoMapper.selectUserTask12(dept);
//      dispatchMap.put("firstUser","jindongxun");
        dispatchMap.put("firstUser",userInfo.get("first_user"));
        dispatchMap.put("secondUser",userInfo.get("second_user"));
        dispatchMap.put("thirdUser",userInfo.get("third_user"));
        dispatchMap.put("fourthUser",userInfo.get("fourth_user"));
    }

    private void handleTask09(Map<String, Object> dispatchMap, DispatchCssReq dispatchCssReq) {
        List<ParaList> paraList = dispatchCssReq.getParaList();
        Map<String, String> map = paraList.stream().collect(Collectors.toMap(ParaList::getParaID, ParaList::getParaVal));
        String level = map.get("ExtEarlyWarningLevel");

        // 验证预警级别参数
        if (StringUtils.isBlank(level)) {
            throw new CustomException("预警级别为空!", Integer.valueOf(IdentySubtype.task_09.getValue()));
        }

        // 验证预警级别取值范围
        if (!Arrays.asList("01", "02", "03").contains(level)) {
            throw new CustomException("预警级别取值错误，应为01、02、03之一!", Integer.valueOf(IdentySubtype.task_09.getValue()));
        }

        // 将预警级别添加到流程变量中，用于流程条件判断
        dispatchMap.put("extEarlyWarningLevel", level);
    }

    /**
     * 信息内容记录
     *
     * @param dispatchCssReq
     */
    private void insertDispatchIdentyBasic(DispatchCssReq dispatchCssReq) {
        DispatchCssDto dispatchDto = new DispatchCssDto();
        BeanUtils.copyProperties(dispatchCssReq, dispatchDto);
        dispatchDto.setSenderFlag(Integer.valueOf(SoundConstant.OK));
        dispatchDto.setIdentyState(dispatch.getValue());
        dispatchDto.setCreatDate(SoundDateUtil.getDate());
        cssDao.insertIdentyBasicInfo(dispatchDto);
//        identyInfoDao.insertIdentyBasicInfo(dispatchDto);
    }

    @Override
    public HeaderResp statementTo(HeaderReq req) {
        log.info("工单归档的入参数 {}", JsonUtil.bean2Json(req));
        //记录参数日志
        insertGroupIdentyLog(req, IdentyStatus.statement.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        StatementCssReq cssReq = JsonUtil.json2Bean(content, StatementCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        startStatementProcess(content, cssReq);
        return headerResp;
    }

    private void startStatementProcess(String content, StatementCssReq cssReq) {
        Map<String,Object> variMap = new HashMap<>();
        variMap.put(SoundConstant.RESTART_FLAG,SoundConstant.NOT);
        variMap.putAll(SoundParamUtil.getVariablesMap(cssReq.getParaList()));
        Map<String,String> map = JsonUtil.json2Bean(content, Map.class);
        String identifier = map.get("Identifier");
        String opinion = map.get("FilingOpinion");
        if(org.apache.commons.lang.StringUtils.isEmpty(identifier)|| org.apache.commons.lang.StringUtils.isEmpty(opinion)){
            throw new RuntimeException("必填参数不能为空.");
        }
        //  获取工单参数
        HistoricProcessInstance task = layoutService.getTaskId(identifier);
        boolean isComplaint = false;
        if(task != null){
            Map<String, Object> processVariables = task.getProcessVariables();
            String identySubtype = String.valueOf(processVariables.getOrDefault("identySubtype",""));
            if(org.apache.commons.lang.StringUtils.equals(identySubtype,IdentySubtype.task_12.getValue())){
                isComplaint = true;
            }
        }
        if(isComplaint){
            // 如果是内部升级投诉任务单， 归档接口则进行工单删除操作
            runtimeService.setVariables(task.getProcessInstanceId(),variMap);
            flowService.removeProcessInstance(task.getProcessInstanceId(),"集团归档");
        }else {
            //执行工单归档处理
            executeIdentyTaskEnd(cssReq.getIdentifier(),variMap,SoundConstant.RESTART_FLAG);
        }
        //归档处理最后一步
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.statement.getValue());

        operationNodeLog.setHandlingOpinion(FilingOpinion.getFilingOpinion(opinion).getLabel());
        cssDao.insertOperationNodeLog(operationNodeLog);
        //归档修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.statement.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
    }

    @Override
    public HeaderResp withdrawTo(HeaderReq req) {
        log.info("工单撤单的入参数 {}", JsonUtil.bean2Json(req));
        //记录参数日志
        insertGroupIdentyLog(req, IdentyStatus.withdraw.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        WithdrawCssReq cssReq = JsonUtil.json2Bean(content, WithdrawCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        startWithdrawProcess(content, cssReq);
        return headerResp;
    }

    private void startWithdrawProcess(String content, WithdrawCssReq cssReq) {
        List<Task> taskList = getIdentifierFlowRunTask(cssReq.getIdentifier());
        if (CollectionUtil.isNotEmpty(taskList)) {
            for (Task task : taskList) {
                Map<String, Object> variable = new HashMap<>();
                variable.put("withdrawTime", cssReq.getWithdrawTime());
                variable.put("withdrawReason", cssReq.getWithdrawReason());
                flowService.removeProcessInstance(task.getProcessInstanceId(), SoundConstant.WITHDRAW_FLAG);
            }
        }
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.withdraw.getValue());
        Map<String, String> map = JsonUtil.json2Bean(content, Map.class);
        operationNodeLog.setHandlingOpinion(map.get("WithdrawReason"));
        cssDao.insertOperationNodeLog(operationNodeLog);
        //撤单修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        String identifier = map.get("Identifier");
        identyHeader.setIdentifier(identifier);
//        identyHeader.setIdentyState(IdentyStatus.withdraw.getValue());
//        cssDao.modifyIdentyBasicState(identyHeader);
//        identyInfoDao.modifyIdentyBasicState(identyHeader);
        cssDao.deleteIdentyBasicInfo(identyHeader);
        cssDao.deleteIdentyHandlerHis(identyHeader);
    }

    @Override
    public HeaderResp queryTo(HeaderReq req) {
        log.info("工单查询的入参数 {}", JsonUtil.bean2Json(req));
        HeaderResp headerResp = getHeaderResp(req);
        QueryReq queryReq = JsonUtil.json2Bean(req.getContent(), QueryReq.class);
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", queryReq.getIdentifier());
        HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
        WithdrawCssResp cssResp = new WithdrawCssResp();
        cssResp.setIdentifier(queryReq.getIdentifier());
        List<WithdrawExtIdentylog> identylogs = new ArrayList<>();
        if (historicProcessInstance == null) {
            cssResp.setState(IdentyState.none.getValue());
            cssResp.setExtIdentylogList(identylogs);
            headerResp.setResult(JsonUtil.bean2Json(cssResp));
            return headerResp;
        }
        //流程结束为结束，其余在进行中
        String processInstanceId = historicProcessInstance.getId();
        if (historicProcessInstance.getEndTime() == null) {
            cssResp.setState(IdentyState.being.getValue());
        } else {
            cssResp.setState(IdentyState.file.getValue());
        }
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(null, null, null, processInstanceId,
                null, null, null, BooleanFilter.TRUE, 0, 50);
        if (CollectionUtil.isEmpty(taskList)) {
            cssResp.setExtIdentylogList(identylogs);
            headerResp.setResult(JsonUtil.bean2Json(cssResp));
            return headerResp;
        }
        for (HistoricTaskInstance historicTaskInstance : taskList) {
            String deleteReason = historicTaskInstance.getDeleteReason();
            //有用户环节，但未处理，去除掉
            if (StringUtil.isNotEmpty(deleteReason)) {
                continue;
            }
            WithdrawExtIdentylog identylog = new WithdrawExtIdentylog();
            String userId = historicTaskInstance.getAssignee();
            ReplyCssReq replyCssReq = authService.queryHandlerInfoReplyInfo(userId);
            identylog.setHandler(replyCssReq.getHandler());
            identylog.setHandlerContactInfor(replyCssReq.getHandlerInfor());
            String department = replyCssReq.getHandingDepartment();
            identylog.setHandingDepartment(StringUtil.isEmpty(department) ? SoundConstant.DEPARTMENT : department);
            identylog.setHandingTime(SoundDateUtil.getDateTime(historicTaskInstance.getEndTime()));
            String taskId = historicTaskInstance.getId();
            List<Comment> taskComments = flowService.getTaskComments(taskId, "comment");
            if (CollectionUtil.isNotEmpty(taskComments)) {
                Comment comment = taskComments.get(0);
                identylog.setHandingOpinions(comment.getMessage());
            }
            identylogs.add(identylog);
        }
        Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
        String launchCompany = String.valueOf(processVariables.get(SoundConstant.LAUNCH_COMPANY));
        String forwardCompany = String.valueOf(processVariables.get(SoundConstant.FORWARD_COMPANY));
        cssResp.setOriginUnit(launchCompany);
        cssResp.setReceiverUnit(forwardCompany);
        cssResp.setExtIdentylogList(identylogs);
        headerResp.setResult(JsonUtil.bean2Json(cssResp));
        return headerResp;
    }

    @Override
    public HeaderResp reprocessTo(HeaderReq req) {
        log.info("工单再处理的入参数 {}", JsonUtil.bean2Json(req));
        //记录处理日志
        insertGroupIdentyLog(req, IdentyStatus.reprocess.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        ReprocessCssReq cssReq = JsonUtil.json2Bean(content, ReprocessCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        startReprocessProcess(content, cssReq);
        return headerResp;
    }

    private void startReprocessProcess(String content, ReprocessCssReq cssReq) {
        Map<String, Object> variMap = new HashMap<>();
        variMap.put(SoundConstant.RESTART_FLAG, SoundConstant.OK);
        //执行工单归档处理
        executeIdentyTaskEnd(cssReq.getIdentifier(), variMap, SoundConstant.RESTART_FLAG);
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.reprocess.getValue());
        Map<String, String> map = JsonUtil.json2Bean(content, Map.class);
        operationNodeLog.setHandlingOpinion(map.get("ReprocessingOpinion"));
        cssDao.insertOperationNodeLog(operationNodeLog);
        //再处理修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        String identifier = map.get("Identifier");
        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.reprocess.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
    }

    @Override
    public HeaderResp urgeto(HeaderReq req) {
        log.info("工单催办的入参数 {}", JsonUtil.bean2Json(req));
        //记录处理日志
        insertGroupIdentyLog(req, IdentyStatus.urge.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        UrgeCssReq cssReq = JsonUtil.json2Bean(content, UrgeCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        startUrgeProcess(content, cssReq);
        return headerResp;
    }

    private void startUrgeProcess(String content, UrgeCssReq cssReq) {
        List<Task> taskList = getIdentifierFlowRunTask(cssReq.getIdentifier());
        if (CollectionUtil.isNotEmpty(taskList)) {
            for (Task task : taskList) {
                int newPriority = task.getPriority() + 10;
                flowService.setPriority(task.getId(), newPriority);
                Map<String, Object> variables = new HashMap<>();
                variables.put("urgeTime", cssReq.getUrgeTime());
                variables.put("urgeReason", cssReq.getUrgeReason());
                flowService.setVariables(task.getId(), variables);
            }
        }
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.statement.getValue());
        Map<String, String> map = JsonUtil.json2Bean(content, Map.class);
        String opinion = map.get("UrgeReason");
        operationNodeLog.setHandlingOpinion(opinion);
        cssDao.insertOperationNodeLog(operationNodeLog);
        //催办修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        String identifier = map.get("Identifier");
        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.urge.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
    }

    @Override
    public HeaderResp sendReplyTo(HeaderReq req) {
        log.info("工单回复的入参数 {}", JsonUtil.bean2Json(req));
        //记录处理日志
        insertGroupIdentyLog(req, IdentyStatus.reply.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        ReplyCssReq cssReq = JsonUtil.json2Bean(content, ReplyCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        String identifier = cssReq.getIdentifier();
        List<org.flowable.engine.runtime.ProcessInstance> xbIdentifiers = runtimeService.createProcessInstanceQuery().variableValueEquals("xbIdentifier", identifier).list();
        if(!xbIdentifiers.isEmpty()){
            String id = xbIdentifiers.get(0).getId();
            ProcessInstance processInstance = flowService.getProcessInstance(id);
            Map<String, Object> processVariables = processInstance.getProcessVariables();
            identifier = String.valueOf(processVariables.get("identifier"));
            log.info("协办工单处理逻辑，根据工单号{}获取到原工单号{}",cssReq.getIdentifier(),identifier);
            cssReq.setIdentifier(identifier);
        }

        startReplyProcess(content, cssReq);
        //插入回复的转发省专
        String forwardCompany = cssReq.getLaunchCompany();
        String identyDetail = identyInfoDao.getIdentyDetail(identifier);
        if ("03030201".equals(identyDetail)) {
            identyInfoDao.insertFowardCompany(identifier,forwardCompany);
        }
        return headerResp;
    }

    private void startReplyProcess(String content, ReplyCssReq cssReq) {
        // 处理智能研判预警单(0024)的特殊参数需求
        handleIntelligentJudgmentWarningReply(cssReq);
        
        ReplyCssDto cssDto = new ReplyCssDto();
        BeanUtils.copyProperties(cssReq, cssDto);
        //工单附件插入到数据库
        cssDto.setParaListStr(JsonUtil.bean2Json(cssReq.getParaList()));
        String handlingOpinion = cssDto.getHandlingOpinion();
        if (com.ai.srd.bd.common.utils.StringUtils.isNotEmpty(handlingOpinion) && handlingOpinion.length() > 100) {
            handlingOpinion = handlingOpinion.substring(0,100);
            cssDto.setHandlingOpinion(handlingOpinion);
        }
        cssDao.insertIdentyHandlerHis(cssDto);
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.reply.getValue());
        Map<String, String> map = JsonUtil.json2Bean(content, Map.class);
        String opinion = map.get("HandlingOpinion");
        operationNodeLog.setHandlingOpinion(opinion);
        cssDao.insertOperationNodeLog(operationNodeLog);
        //执行工单回复处理
        Map<String, Object> variables = SoundParamUtil.getVariablesMap(cssReq.getParaList());
        variables.put(SoundConstant.GROUP_REPLY_REJECT_FLAG,SoundConstant.NOT);
        executeIdentyTaskEnd(cssReq.getIdentifier(), variables, SoundConstant.GROUP_REPLY_FLAG);
        //回复修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        String identifier = map.get("Identifier");
        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.reply.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
    }

    @Override
    public HeaderResp currencyTo(HeaderReq req) {
        log.info("工单通用接口 {}", JsonUtil.bean2Json(req));
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        CurrencyCssReq cssReq = JsonUtil.json2Bean(content, CurrencyCssReq.class);
        if (cssReq == null) {
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        Map<String, Object> variMap = new HashMap<>();
        variMap.put(SoundConstant.CURRENCY_FLAG, SoundConstant.OK);
        headerResp.setResult(JsonUtil.bean2Json(cssReq));
        return headerResp;
    }

    @Override
    public void endEmos(OtherSystemHeaderReq headerReq) {
        String workOrderId = headerReq.getWorkOrderId();
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", workOrderId);
        HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
        String excuteId = flowService.getExecutionId(historicProcessInstance.getId(), "test_03_waiting_for_emos");//流程实例id，，key
        flowService.triggerExecution(excuteId);
    }


    /**
     * 附件回复
     *
     * @param cssReq
     * @return
     */
    @Override
    public HeaderResp sendAttachReplyTo(ReplyCssReq cssReq, boolean flag) {
        Map<String, Object> variMap = new HashMap<>();
        boolean ok = false;
        String content = JsonUtil.bean2Json(cssReq);
        OperationNodeLog operationNodeLog = null;
        //集团始终未收到附件
        if (!flag) {
            operationNodeLog = insertOperationNodeLog(content, IdentyStatus.reprocess.getValue());
            operationNodeLog.setHandlingOpinion("集团未收到附件");
            cssDao.insertOperationNodeLog(operationNodeLog);
            variMap.put(SoundConstant.REPLY_FLAG, SoundConstant.NOT);
            //执行工单确认处理节点
            executeIdentyTaskEnd(cssReq.getIdentifier(), variMap, SoundConstant.REPLY_FLAG);
            return null;
        }
        HeaderResp headerResp = sendService.sendReplyTo(cssReq);
        //集团返回信息正确与错误判断
        HeaderCodeResp codeResp = headerResp.getResponse();
        if (!SoundConstant.GROUP_REPLY_SUCCESS.equals(codeResp.getRspCode())) {
            variMap.put(SoundConstant.REPLY_FLAG, SoundConstant.NOT);
        } else {
            variMap.put(SoundConstant.REPLY_FLAG, SoundConstant.OK);
        }
        //记录操作信息，用户能看见操作情况
        operationNodeLog = insertOperationNodeLog(content, IdentyStatus.reply.getValue());
        operationNodeLog.setHandlingOpinion(codeResp.getRspDesc());
        cssDao.insertOperationNodeLog(operationNodeLog);
        Integer rspCode = Integer.valueOf(codeResp.getRspCode());
        //如果成功或者业务上的失败,则流程走向下一节点,其它情况流程还是不走,在后台循环每5分钟执行一次,等待下次重试成功
        if (SoundConstant.GROUP_REPLY_SUCCESS.equals(codeResp.getRspCode()) || rspCode >= 30000) {
            //执行工单确认处理节点
            executeIdentyTaskEnd(cssReq.getIdentifier(), variMap, SoundConstant.REPLY_FLAG);
        }
        return headerResp;
    }

    private HeaderResp getHeaderResp(HeaderReq req) {
        HeaderResp headerResp = new HeaderResp();
        headerResp.setTransIDO(req.getTransIDO());
        headerResp.setTransIDH(SoundParamUtil.getTransIDO());
        headerResp.setCutOffDay(SoundDateUtil.getNowDate());
        headerResp.setTransIDHTime(SoundDateUtil.getNowDateTime());
        headerResp.setProvidePartyID(SoundConstant.USER_PARTY_ID);
        headerResp.setResult("");
        headerResp.setResponse(RespUtil.getSuccess());
        return headerResp;
    }

    @Override
    public boolean isExistIdenty(IdentyHeader header) {
        DispatchCssReq dispatchCssReq = cssDao.selectIdentyBasicInfoById(header);
        return dispatchCssReq != null;
    }


    @Override
    public HeaderResp TestjobTo(HeaderReq cssReq) {
        log.info("测试工单同步删除的入参数 {}", JsonUtil.bean2Json(cssReq));
        HeaderResp headerResp = getHeaderResp(cssReq);
        String content = cssReq.getContent();
        IdentyHeader identyHeader = JsonUtil.json2Bean(content, IdentyHeader.class);
        String identifier = identyHeader.getIdentifier();
        workflowService.removeProcessInstance(identifier, null);
        return headerResp;
    }

    /**
     * @Description 外部系统工单查询
     * @Param req
     * @Return {@link OtherSystemHeaderResp}
     * <AUTHOR>
     * @Date 2022/8/17 16:08
     */
    @Override
    public OtherSystemHeaderResp replyToOtherSystem(OtherSystemHeaderReq req) {

        log.info("外部系统工单查询的入参数 {}", JsonUtil.bean2Json(req));
        OtherSystemHeaderResp headerResp = new OtherSystemHeaderResp();
        QueryReq queryReq = JsonUtil.json2Bean(req.getContent(), QueryReq.class);
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", queryReq.getIdentifier());
        HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
        WithdrawCssResp cssResp = new WithdrawCssResp();
        cssResp.setIdentifier(queryReq.getIdentifier());
        List<WithdrawExtIdentylog> identylogs = new ArrayList<>();
        if (historicProcessInstance == null) {
            cssResp.setState(IdentyState.none.getValue());
            cssResp.setExtIdentylogList(identylogs);
            headerResp.setParameters(null);
            headerResp.setHandlingInformation(JsonUtil.bean2Json(cssResp));
            return headerResp;
        }
        //流程结束为结束，其余在进行中
        String processInstanceId = historicProcessInstance.getId();
        if (historicProcessInstance.getEndTime() == null) {
            cssResp.setState(IdentyState.being.getValue());
        } else {
            cssResp.setState(IdentyState.file.getValue());
        }
        Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
        headerResp.setParameters(JsonUtil.bean2Json(processVariables));
        List<HistoricTaskInstance> taskList = flowService.getHistoricTaskInstances(null, null, null, processInstanceId,
                null, null, null, BooleanFilter.ALL, 0, 50);
        if (CollectionUtil.isEmpty(taskList)) {
            cssResp.setExtIdentylogList(identylogs);
            headerResp.setHandlingInformation(JsonUtil.bean2Json(cssResp));
            return headerResp;
        }
        for (HistoricTaskInstance historicTaskInstance : taskList) {
            String deleteReason = historicTaskInstance.getDeleteReason();
            //有用户环节，但未处理，去除掉
            if (StringUtil.isNotEmpty(deleteReason)) {
                continue;
            }
            WithdrawExtIdentylog identylog = new WithdrawExtIdentylog();
            String userId = historicTaskInstance.getAssignee();
            ReplyCssReq replyCssReq = authService.queryHandlerInfoReplyInfo(userId);
            identylog.setHandler(replyCssReq.getHandler());
            identylog.setHandlerContactInfor(replyCssReq.getHandlerInfor());
            String department = replyCssReq.getHandingDepartment();
            identylog.setHandingDepartment(StringUtil.isEmpty(department) ? SoundConstant.DEPARTMENT : department);
            identylog.setHandingTime(SoundDateUtil.getDateTime(historicTaskInstance.getCreateTime()));
            String taskId = historicTaskInstance.getId();
            List<Comment> taskComments = flowService.getTaskComments(taskId, "comment");
            if (CollectionUtil.isNotEmpty(taskComments)) {
                Comment comment = taskComments.get(0);
                identylog.setHandingOpinions(comment.getMessage());
            }
            identylogs.add(identylog);
        }

        if (CollectionUtils.isNotEmpty(identylogs)){
            for (WithdrawExtIdentylog identylog : identylogs) {
                String handingOpinions = identylog.getHandingOpinions();
                if (StringUtils.isBlank(handingOpinions)){
                    identylog.setHandingOpinions("");
                }
            }
        }
        cssResp.setExtIdentylogList(identylogs);
        headerResp.setHandlingInformation(JsonUtil.bean2Json(cssResp));
        return headerResp;
    }

    /**
     * @Description 通过4A账号查询工单编号
     * @Param req
     * @Return {@link List<QueryIdentifierBy4AResp>}
     * <AUTHOR>
     * @Date 2022/8/17 16:08
     */
    @Override
    public List<QueryIdentifierBy4AResp> queryIdentifierBy4AId(String content) {
        JSONObject jsonObject = JSONObject.parseObject(content);
        String userId = (String) jsonObject.get("userId");
        List<QueryIdentifierBy4AResp> respList = new ArrayList<>();
        QueryIdentifierBy4AResp inProgressResp = new QueryIdentifierBy4AResp();
        inProgressResp.setIdentyState(IdentyState.being.getValue());
        QueryIdentifierBy4AResp finishedResp = new QueryIdentifierBy4AResp();
        finishedResp.setIdentyState(IdentyState.file.getValue());
        List<String> list = cssDao.queryIdentifierBy4AId(userId);

        List<String> inProgressRespList = new ArrayList<>();
        List<String> finishedRespList = new ArrayList<>();
        for (String identifier : list) {
            Map<String, String> variables = new HashMap<>();
            variables.put("identifier", identifier);
            HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
            if (historicProcessInstance != null && historicProcessInstance.getEndTime() == null) {
                inProgressRespList.add(identifier);
            } else {
                finishedRespList.add(identifier);
            }
        }
        inProgressResp.setIdentifier(inProgressRespList);
        finishedResp.setIdentifier(finishedRespList);

        // 返回工单ID和工单标题
        if (CollectionUtils.isNotEmpty(inProgressRespList)) {
            List<QueryIdentifierInfoResp> inProgressIdentifierInfos = cssDao.getIdentifierInfoList(inProgressRespList);
            inProgressResp.setIdentifierInfos(inProgressIdentifierInfos);
        }
        if (CollectionUtils.isNotEmpty(finishedRespList)) {
            List<QueryIdentifierInfoResp> finishedIdentifierInfos = cssDao.getIdentifierInfoList(finishedRespList);
            finishedResp.setIdentifierInfos(finishedIdentifierInfos);
        }
        respList.add(inProgressResp);
        respList.add(finishedResp);
        return respList;
    }

    @Override
    public HeaderResp TrackinforTo(HeaderReq cssReq) {
        HeaderResp headerResp = getHeaderResp(cssReq);
        return headerResp;
    }

    /**
     * 集团工单操作日志记录
     */
    private void insertGroupIdentyLog(HeaderReq req, String identyStatus) {
        //记录参数日志
        String content = req.getContent();
        insertGroupIdentyLogByString(content, identyStatus);
    }


    /**
     * 集团工单操作日志记录
     */
    private void insertGroupIdentyLogByString(String content, String identyStatus) {
        //记录参数日志
        IdentyLogPo identyLogPo = new IdentyLogPo();
        Map<String, String> map = JsonUtil.json2Bean(content, Map.class);
        String identifier = map.get("Identifier");
        if (StringUtil.isEmpty(identifier)) {
            identifier = map.get("identifier");
        }
        identyLogPo.setIdentifier(identifier);
        identyLogPo.setIdentyMsg(content);
        String time = LocalDate.now().toString();
        String nowTime = LocalDateTime.now().toString();
        identyLogPo.setIdentyDate(time);
        identyLogPo.setIdentyTime(nowTime);
        identyLogPo.setIdentyStatus(identyStatus);
        identyLogDao.insertIdentyLog(identyLogPo);
    }

    /**
     * 集团工单操作处理节点日志记录
     */
    private OperationNodeLog insertOperationNodeLog(String content, String identyStatus) {
        //记录参数日志
        OperationNodeLog operationNodeLog = new OperationNodeLog();
        Map<String, Object> map = JsonUtil.json2Bean(content, Map.class);
        String identifier = String.valueOf(map.get("Identifier"));
        String handler = String.valueOf(map.get("Creator"));
        operationNodeLog.setIdentifier(identifier);
        operationNodeLog.setHandler(handler);
        operationNodeLog.setHandingTime(LocalDateTime.now().toString());
        operationNodeLog.setIdentyState(identyStatus);
        operationNodeLog.setForwardCompany(String.valueOf(map.get("ForwardCompany")));
        operationNodeLog.setLaunchCompany(String.valueOf(map.get("LaunchCompany")));
        if (map.get("ParaList") != null) {
            operationNodeLog.setParaListStr(JsonUtil.bean2Json(map.get("ParaList")));
        }
        operationNodeLog.setStateCode(IdentyStatus.getIdentyStatus(identyStatus).getLabel());
        return operationNodeLog;
    }

    /**
     * 根据工单编号获取任务内容
     *
     * @param variables
     * @return
     */
    private HistoricProcessInstance getHistoricTaskInstanceByIdentifier(Map<String, String> variables) {
        List<HistoricProcessInstance> historicProcessInstances = flowService.getHistoricProcessInstances(null, null,
                variables, BooleanFilter.ALL, 0, 1);
        if (CollectionUtil.isEmpty(historicProcessInstances)) {
            return null;
        } else {
            return historicProcessInstances.get(0);
        }
    }

    /**
     * 获取工单流程任务
     *
     * @return
     */
    private List<Task> getIdentifierFlowRunTask(String identifier) {
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", identifier);
        return flowService.getTasks(null, null, variables, null, null, 0, 100);
    }

    /**
     * 执行再处理与归档的操作
     *
     * @param identifier
     * @param variMap
     */
    private void executeIdentyTaskEnd(String identifier, Map<String, Object> variMap, String endStr) {
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", identifier);
        HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
        if (historicProcessInstance != null) {
            Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
            String identySubType = String.valueOf(processVariables.get("identySubtype"));
            String activityId = IdentySubtype.getIdentySubtype(identySubType).name() + SoundConstant.ATTACH_SUB + endStr;
            try {
                String excuteId = flowService.getExecutionId(historicProcessInstance.getId(), activityId);
                flowService.setExecutionVariables(excuteId, variMap);
                flowService.triggerExecution(excuteId);
            } catch (Exception e) {
                log.error("未达到集团处理的节点，不能进行归档与预处理", e);
            }
        }
    }

    /**
     * 给流程添加参数
     *
     * @param identifier
     * @param identyHeader
     */
    private void addIdentyParam(String identifier, IdentyHeader identyHeader) {
        List<Task> taskList = getIdentifierFlowRunTask(identifier);
        if (CollectionUtil.isNotEmpty(taskList)) {
            for (Task task : taskList) {
                Map<String, Object> variables = new HashMap<>();
                //variables.put(SoundConstant.LAUNCH_COMPANY,identyHeader.getLaunchCompany());
                //variables.put(SoundConstant.FORWARD_COMPANY,identyHeader.getForwardCompany());
                variables.put(SoundConstant.PARA_LIST, identyHeader.getParaList());
                flowService.setVariables(task.getId(), variables);
            }
        }
    }

    /**
     * 执行再处理与归档的操作
     *
     * @param identifier
     */
    private void executeIdentyCurrencyEnd(String identifier, Map<String, Object> variMap) {
        Map<String, String> variables = new HashMap<>();
        variables.put("identifier", identifier);
        HistoricProcessInstance historicProcessInstance = getHistoricTaskInstanceByIdentifier(variables);
        if (historicProcessInstance != null) {
            Map<String, Object> processVariables = historicProcessInstance.getProcessVariables();
            String identySubType = String.valueOf(processVariables.get("identySubtype"));
            String activityId = IdentySubtype.getIdentySubtype(identySubType).name() + SoundConstant.ATTACH_SUB + SoundConstant.CURRENCY_FLAG;
            try {
                String excuteId = flowService.getExecutionId(historicProcessInstance.getId(), activityId);
                flowService.setExecutionVariables(excuteId, variMap);
                flowService.triggerExecution(excuteId);
            } catch (Exception e) {
                log.error("集团通用接口未调用");
            }
        }
    }

    @Override
    public void sendReplyAccept(String identifier) {
        Map<String, Object> variMap = new HashMap<>();
        variMap.put(SoundConstant.REPLY_FLAG,SoundConstant.NOT);
        executeIdentyTaskEnd(identifier,variMap,SoundConstant.REPLY_FLAG);
    }

    @Override
    public HeaderResp returnCSS(HeaderReq req) {
        log.info("工单驳回的入参数 {}",JsonUtil.bean2Json(req));
        //记录处理日志
        insertGroupIdentyLog(req,IdentyStatus.returnCss.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        RejectCssReq cssReq = JsonUtil.json2Bean(content,RejectCssReq.class);
        if(cssReq == null){
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        String identifier = cssReq.getIdentifier();
        List<org.flowable.engine.runtime.ProcessInstance> xbIdentifiers = runtimeService.createProcessInstanceQuery().variableValueEquals("xbIdentifier", identifier).list();
        if(!xbIdentifiers.isEmpty()){
            String id = xbIdentifiers.get(0).getId();
            ProcessInstance processInstance = flowService.getProcessInstance(id);
            Map<String, Object> processVariables = processInstance.getProcessVariables();
            identifier = String.valueOf(processVariables.get("identifier"));
            log.info("协办工单处理逻辑，根据工单号{}获取到原工单号{}",cssReq.getIdentifier(),identifier);
            cssReq.setIdentifier(identifier);
        }
        startRejectProcess(content,cssReq);
        return headerResp;
    }

    private void startRejectProcess(String content,RejectCssReq cssReq){
        ReplyCssDto cssDto = new ReplyCssDto();
        BeanUtils.copyProperties(cssReq,cssDto);
        cssDto.setReplyMsg(cssReq.getReturnReason());
        cssDto.setHandlerInfor(cssReq.getHandlerTelephone());
        cssDto.setHandingTime(cssReq.getReturnTime());
        //工单附件插入到数据库
        cssDto.setParaListStr(JsonUtil.bean2Json(cssReq.getParaList()));
        cssDao.insertIdentyHandlerHis(cssDto);
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.returnCss.getValue());
        Map<String,String> map = JsonUtil.json2Bean(content, Map.class);
        String opinion = map.get("HandlingOpinion");
        operationNodeLog.setHandlingOpinion(opinion);
        operationNodeLog.setHandler(cssReq.getHandler());
        operationNodeLog.setHandlingOpinion(cssReq.getReturnReason());
        cssDao.insertOperationNodeLog(operationNodeLog);
        Map<String,Object> variable = SoundParamUtil.getVariablesMap(cssReq.getParaList());
        variable.put("ReturnTime",cssReq.getReturnTime());
        variable.put("ReturnReason",cssReq.getReturnReason());
        //执行工单回复处理
        variable.put(SoundConstant.GROUP_REPLY_REJECT_FLAG,SoundConstant.OK);
        executeIdentyTaskEnd(cssReq.getIdentifier(), variable, SoundConstant.GROUP_REPLY_FLAG);
        //回复修改工单状态
        IdentyHeader identyHeader = new IdentyHeader();
        String identifier = map.get("Identifier");
        identyHeader.setIdentifier(identifier);
        identyHeader.setIdentyState(IdentyStatus.returnCss.getValue());
        cssDao.modifyIdentyBasicState(identyHeader);
        identyInfoDao.modifyIdentyBasicState(identyHeader);
    }


    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateDataInfo(List<ZbCusRepairTaskDay> datas) {
        for (ZbCusRepairTaskDay data : datas) {
            repairTaskDayMapper.updateByEntityId(data);
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateTrackStatus(String taskId, String processInstanceId) {
        Map<String, Object> variables = flowService.getTaskVariables(taskId, null);
        String identifier = String.valueOf(variables.get("identifier"));
        identyInfoDao.updateTrackStatus(identifier);
    }

    @Override
    public HeaderResp sync(HeaderReq req) {
        log.info("工单信息同步的入参数 {}",JsonUtil.bean2Json(req));
        //记录处理日志
        insertGroupIdentyLog(req,IdentyStatus.syncData.getValue());
        HeaderResp headerResp = getHeaderResp(req);
        String content = req.getContent();
        RejectCssReq cssReq = JsonUtil.json2Bean(content,RejectCssReq.class);
        if(cssReq == null){
            headerResp.setResponse(RespUtil.getParamEmpty());
            return headerResp;
        }
        String identifier = cssReq.getIdentifier();
        OperationNodeLog operationNodeLog = insertOperationNodeLog(content, IdentyStatus.syncData.getValue());
        Map<String,String> map = JsonUtil.json2Bean(content, Map.class);
        String opinion = map.get("HandlingOpinion");
        operationNodeLog.setHandlingOpinion(opinion);
        operationNodeLog.setHandler(cssReq.getHandler());
        operationNodeLog.setHandlingOpinion(cssReq.getReturnReason());
        cssDao.insertOperationNodeLog(operationNodeLog);
        Map<String,Object> variable = SoundParamUtil.getVariablesMap(cssReq.getParaList());
        //更新流程变量
        HistoricProcessInstance processInstance = layoutService.getTaskId(identifier);
        if(processInstance == null) {
            headerResp.setResponse(RespUtil.getCustomError("工单不存在"));
            return  headerResp;
        }
        runtimeService.setVariables(processInstance.getProcessInstanceId(),variable);
       /* //插入回复的转发省专
        String identifier = cssReq.getIdentifier();
        String forwardCompany = cssReq.getForwardCompany();
        String identyDetail = identyInfoDao.getIdentyDetail(identifier);
        if ("03030201".equals(identyDetail)) {
            identyInfoDao.insertFowardCompany(identifier,forwardCompany);
        }*/
        return headerResp;
    }

}
