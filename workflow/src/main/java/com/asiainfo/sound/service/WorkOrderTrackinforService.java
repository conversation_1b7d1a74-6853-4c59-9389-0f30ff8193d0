package com.asiainfo.sound.service;

import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.OrderTrackTaskLog;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.domain.req.TrackinforQueryReq;
import com.asiainfo.sound.domain.resp.TrackinforInfoResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3
 **/
public interface WorkOrderTrackinforService {


    void handleAttachUpload(String identifier,String groupRealAttachName,String identySubtypeName);

    boolean isExistsAttach(String identifier);

    void handleTrackinfor(ReplyCssReq cssReq, OrderTrackTaskLog trackTaskLog);

    void handleCurrentNodeTrackinfor(ReplyCssReq cssReq, OrderTrackTaskLog trackTaskLog, String taskName, String taskId);

    /**
     * 查询工单轨迹信息上报信息
     *
     * @param identifier 工单编号
     * @return 工单轨迹记录列表
     */
    List<OrderTrackRecord> queryTrackinforInfo(String identifier);

    /**
     * 根据条件查询工单轨迹信息上报信息
     *
     * @param queryReq 查询条件
     * @return 工单轨迹记录列表
     */
    List<OrderTrackRecord> queryTrackinforInfoByCondition(TrackinforQueryReq queryReq);

    /**
     * 查询工单轨迹信息上报信息（格式化响应）
     *
     * @param identifier 工单编号
     * @return 格式化的工单轨迹记录列表
     */
    List<TrackinforInfoResp> queryTrackinforInfoFormatted(String identifier);

    /**
     * 根据条件查询工单轨迹信息上报信息（格式化响应）
     *
     * @param queryReq 查询条件
     * @return 格式化的工单轨迹记录列表
     */
    List<TrackinforInfoResp> queryTrackinforInfoByConditionFormatted(TrackinforQueryReq queryReq);

}
