package com.asiainfo.sound.daoFlow;

import com.ai.srd.bd.common.annotation.DataSource;
import com.ai.srd.bd.common.enums.DataSourceType;
import com.asiainfo.sound.domain.dto.IdentyHeader;
import com.asiainfo.sound.domain.po.IdentyLogPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @desc:
 * @author: zhengxy
 * @create: 2021/12/22 10:56
 */
@Repository
@DataSource(DataSourceType.MASTER)
public interface IdentyLogDao{
    void insertIdentyLog(IdentyLogPo identyLogPo);

    List<IdentyLogPo> queryIdentyLogByIdentifier(IdentyHeader identyHeader);
}
