<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.OrderAttachSequenceMapper">

    <select id="getCurrentDayCount" resultType="java.lang.Integer">
        select count(1)
        from order_attach_sequence
        where sequence_date = #{currentDay}
    </select>

    <select id="getCurrentDayMax" resultType="java.lang.Integer">
        select max(sequence_id)
        from order_attach_sequence
        where sequence_date = #{currentDay}
    </select>

</mapper>
