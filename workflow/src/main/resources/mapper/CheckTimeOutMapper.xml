<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.daoFlow.CheckTimeOutDao">
    <select id="selectTimeOutInstanceId" resultType="com.asiainfo.sound.domain.query.ActRuTask">
        select ID_,PROC_INST_ID_, ASSIGNEE_ from act_ru_task where PROC_INST_ID_
        in (select PROC_INST_ID_
        from act_ru_execution
        are,act_re_procdef arp
        where are.PROC_DEF_ID_ = arp.ID_
        and arp.KEY_ in( 'repair_01','repair_02')
        and PARENT_ID_ is
        NULL and DATE_ADD(START_TIME_,INTERVAL 1 HOUR) <![CDATA[<]]> now())
    </select>
    <select id="selectSend" resultType="java.lang.String">
        select * from sound_message_send
    </select>
    <insert id="insertRecord" parameterType="java.util.List">
        INSERT INTO sound_message_send (act_ru_task_id)
        VALUES
        <foreach collection ="list" item="item" separator =",">
            (#{item})
        </foreach >
    </insert>


</mapper>
