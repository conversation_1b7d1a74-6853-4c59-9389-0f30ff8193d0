<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~     Copyright (c) 2021-2030, SRD-BD ai-cloudx-team All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~  Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the bd.srd.ai.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: SRD-BD ai-cloudx-team
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.asiainfo.sound.dao.SoundHistoryWorkMainOrdersMapper">

  <resultMap id="soundHistoryWorkMainOrdersMap" type="com.asiainfo.sound.domain.entity.SoundHistoryWorkMainOrders">
                  <id property="id" column="id"/>
                        <result property="identifier" column="identifier"/>
                        <result property="identyType" column="identy_type"/>
                        <result property="operationType" column="operation_type"/>
                        <result property="operationScenes" column="operation_scenes"/>
                        <result property="identyTitle" column="identy_title"/>
                        <result property="content" column="content"/>
                        <result property="receive" column="receive"/>
                        <result property="identyCreateTime" column="identy_create_time"/>
                        <result property="identyHandTime" column="identy_hand_time"/>
                        <result property="status" column="status"/>
                        <result property="drafter" column="drafter"/>
                        <result property="provinceReceiveTime" column="province_receive_time"/>
                        <result property="provinceIdentifier" column="province_identifier"/>
                        <result property="handler" column="handler"/>
                        <result property="currentSession" column="current_session"/>
                        <result property="provinceResult" column="province_result"/>
            </resultMap>

</mapper>








