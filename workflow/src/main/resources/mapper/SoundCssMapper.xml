<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.daoFlow.SoundCssDao">
    <insert id="insertIdentyBasicInfo">
        insert into sound_identy_basic_info(identifier, identy_type, identy_subtype, identy_detail, title, content,
                                            origin_unit, receiver_unit, creat_time, process_time, creator,
                                            creator_contact_info,
                                            attach_list, identy_status, sender_flag, creat_date, user_id,
                                            param_list) value (#{identifier}, #{identyType}, #{identySubtype},
                                                               #{identyDetail}, #{title},
                                                               #{content}, #{originUnit}, #{receiverUnit}, #{creatTime},
                                                               #{processTime}, #{creator},
                                                               #{creatorContactInfo}, #{attachList}, #{identyState},
                                                               #{senderFlag}, #{creatDate},
                                                               #{userId}, #{paramListStr})
    </insert>
    <insert id="insertIdentyHandlerHis">
        insert into sound_identy_handle_his
        (identifier, handing_time, handler, handler_infor, handling_opinion,
         handler_rank, handing_department, attach_list, launchCompany, forwardCompany,
         reply_msg, para_list, param_status)
            value (#{identifier}, #{handingTime}, #{handler}, #{handlerInfor},
                   #{handlingOpinion}, #{handlerRank}, #{handingDepartment}, #{attachList},
                   #{launchCompany}, #{forwardCompany}, #{replyMsg}, #{paraListStr}, #{paramStatus})
    </insert>
    <update id="modifyIdentyBasicState">
        update sound_identy_basic_info
        set identy_status = #{identyState}
        where identifier = #{identifier}
    </update>
    <select id="selectWithdrawExtIdentyLogs" resultType="com.asiainfo.sound.domain.dto.ReplyCssDto">
        select handing_time       as handingTime,
               Handing_department as handingDepartment,
               handler            as handler,
               handler_rank       as handlerRank,
               handler_infor      as handlerInfor,
               handling_opinion   as handlingOpinion,
               attach_list        as attachList,
               launchCompany,
               forwardCompany,
               para_list          as paraListStr,
               reply_msg          as replyMsg
        from sound_identy_handle_his
        where identifier = #{identifier}
        order by id
    </select>
    <select id="selectIdentyBasicInfoById" resultType="com.asiainfo.sound.domain.req.DispatchCssReq">
        select identifier           as identifier,
               identy_type          as identyType,
               identy_subtype       as identySubtype,
               identy_detail        as identyDetail,
               title                as title,
               content              as content,
               origin_unit          as originUnit,
               receiver_unit        as receiverUnit,
               creat_time           as creatTime,
               process_time         as processTime,
               creator              as creator,
               creator_contact_info as creatorContactInfo,
               attach_list          as attachList,
               identy_status        as identyStatus
        from sound_identy_basic_info
        where identifier = #{identifier}
    </select>
    <select id="selectIdentyBasicInfos" resultType="com.asiainfo.sound.domain.vo.DispatchCssVo">
        select identifier as identifier,
        identy_type as identyType,
        identy_subtype as identySubtype,
        identy_detail as identyDetail,
        title as title,
        content as content,
        origin_unit as originUnit,
        receiver_unit as receiverUnit,
        creat_time as creatTime,
        process_time as processTime,
        creator as creator,
        creator_contact_info as creatorContactInfo,
        attach_list as attachList,
        identy_status as identyStatus
        from sound_identy_basic_info
        where 1=1 and identy_type = #{identyType}
        and identy_subtype = #{identySubtype}
        <if test="identifier != null and identifier != ''">
            and identifier = #{identifier}
        </if>
    </select>
    <select id="selectIdentyDetailCode" resultType="com.asiainfo.sound.domain.vo.SelectBean">
        select identy_detail_id   as value,
               identy_detail_desc as label
        from sound_dim_identy_detail_code
    </select>

    <select id="selectIdentyReply" resultType="com.asiainfo.sound.domain.req.ReplyCssReq">
        select
        from
    </select>

    <select id="selectDispatchCssByCondition" resultType="com.asiainfo.sound.domain.dto.DispatchCssDto">
        select identy_subtype as identySubtype,
        identy_detail as identyDetail,
        identy_type as identyType,
        title as title,
        content as content,
        process_time as processTime,
        attach_list as attachList,
        identifier as identifier,
        origin_unit as originUnit,
        receiver_unit as receiverUnit,
        creat_time as creatTime,
        creator as creator,
        creator_contact_info as creatorContactInfo,
        identy_status as identyStatus
        from sound_identy_basic_info
        where 1=1 and identy_type='03'
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
        <if test="identyPage.identySubtype != null and identyPage.identySubtype != ''">
            and identy_subtype = #{identyPage.identySubtype}
        </if>
        <if test="identyPage.identifier != null and identyPage.identifier != ''">
            and identifier like #{identyPage.identifier}
        </if>
        <if test="identyPage.title != null and identyPage.title != ''">
            and title like #{identyPage.title}
        </if>
        order by creat_time desc

    </select>

    <select id="findOperationNodeLogBy" resultType="com.asiainfo.sound.domain.po.OperationNodeLog">
        select state            as identyState,
               handing_time     as handingTime,
               handling_opinion as handlingOpinion,
               handler          as handler,
               state_code       as stateCode,
               launchCompany,
               forwardCompany
        from sound_identy_apply_handle_his
        where identifier = #{identifier}
        order by handing_time desc
    </select>

    <insert id="insertOperationNodeLog">
        insert into sound_identy_apply_handle_his
        (identifier, state, handing_time, handling_opinion, handler, state_code, launchCompany, forwardCompany,
         para_list)
            value (#{identifier}, #{identyState}, #{handingTime}, #{handlingOpinion}, #{handler}, #{stateCode},
                   #{launchCompany}, #{forwardCompany}, #{paraListStr})
    </insert>

    <select id="selectIdentyBasicInfoByCreatTime" resultType="com.asiainfo.sound.domain.vo.DispatchCssVo">
        select identifier     as identifier,
               identy_type    as identyType,
               identy_subtype as identySubtype
        from sound_identy_basic_info
        where 1 = 1
          and sender_flag = 1
          and creat_date = #{creatDate}
    </select>

    <select id="selectIdentyBasicInfosByUser" resultType="com.asiainfo.sound.domain.vo.DispatchCssVo">
        select identifier as identifier,
        identy_type as identyType,
        identy_subtype as identySubtype,
        identy_detail as identyDetail,
        title as title,
        content as content,
        origin_unit as originUnit,
        receiver_unit as receiverUnit,
        creat_time as creatTime,
        process_time as processTime,
        creator as creator,
        creator_contact_info as creatorContactInfo,
        attach_list as attachList,
        identy_status as identyStatus,
        sender_flag as type
        from sound_identy_basic_info
        where 1=1
        and sender_flag = '0'
        <if test="dto.title != null and dto.title != ''">
            and title like CONCAT(CONCAT('%',#{dto.title}),'%')
        </if>
        <if test="startTime != null and startTime != ''">
            and creat_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and creat_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="dto.identyType != null and dto.identyType != ''">
            and identy_type = #{dto.identyType}
        </if>
        <if test="dto.identySubtype != null and dto.identySubtype != ''">
            and identy_subtype = #{dto.identySubtype}
        </if>
        <if test="dto.identifier != null and dto.identifier != ''">
            and identifier like #{dto.identifier}
        </if>
        and user_id = #{dto.userId}
        order by creat_time desc
    </select>

    <select id="selectCreatorCount" resultType="long">
        select count(1)
        from sound_identy_basic_info
        where user_id = #{userId}
          and sender_flag = '0'

    </select>

    <select id="selectBeansByDataCommonCode" resultType="com.asiainfo.sound.domain.po.IdentyCommonCodePo">
        select data_type as dataType,
               data_id   as dataId,
               data_name as dataName
        from sound_dim_identy_common_code
    </select>
    <select id="selectBeanByDataCommonCodeByDataType" resultType="com.asiainfo.sound.domain.po.IdentyCommonCodePo">
        select data_type as dataType,
               data_id as dataId,
               data_name as dataName
        from sound_dim_identy_common_code
        where data_type = #{dataType} and data_id = #{dataId}
    </select>

    <delete id="deleteIdentyBasicInfo">
        delete
        from sound_identy_basic_info
        where identifier = #{identifier,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteIdentyHandlerHis">
        delete
        from sound_identy_apply_handle_his
        where identifier = #{identifier,jdbcType=VARCHAR}
    </delete>

    <insert id="insertComplainTaskParamList">
        insert into sound_identy_complaint_param_list
        (identifier, current_title, problem_name, problem_content, problem_reason, governance_measures,
         goal, completion_time, number, headquarter, headquarter_name, specific_work)
            value (#{identifier}, #{currentTitle}, #{problemName}, #{problemContent}, #{problemReason},
                   #{governanceMeasures},
                   #{goal}, #{completionTime}, #{number}, #{headquarter}, #{headquarterName}, #{specificWork})
    </insert>

    <select id="selectOtherSystemUrl" resultType="com.asiainfo.sound.domain.vo.OtherSystemMappingVo">
        SELECT *
        from sound_othersystem_mapping
        where othersystem_id = #{otherSystemId}
    </select>
    <select id="queryIdentifierBy4AId" resultType="java.lang.String">
        SELECT identifier
        from sound_identy_basic_info
        where user_id = #{userId}
          and identy_subtype = '0302'
          and sender_flag = 2
    </select>

    <select id="getIdentifierInfoList" resultType="com.asiainfo.sound.domain.resp.QueryIdentifierInfoResp">
        SELECT identifier,title,creat_time from sound_identy_basic_info where identifier in
        <foreach collection="identifiers" item="identifier" open="(" separator="," close=")">
            #{identifier}
        </foreach>
    </select>

    <select id="selectTrackStatus" resultType="com.asiainfo.sound.domain.dto.DispatchCssDto">
        select i.identifier, i.identy_type, i.identy_subtype, i.reply_content, c.retry_count, b.task_retry_count
        from sound_identy_basic_info i
                 inner join order_track_task_config c
                            on i.track_status = c.track_status
                 left join (
            select identifier, count(1) task_retry_count
            from order_track_task_log
            where track_status = #{trackStatus}
            group by identifier
        ) b
                           on i.identifier = b.identifier
        where c.enable = 1
          and c.retry_count > 0
          and i.track_status = #{trackStatus}
          and ifnull(b.task_retry_count, 0) &lt;= c.retry_count
    </select>

    <select id="select191Timeout" resultType="String">
        select i.identifier
        from sound_identy_basic_info i
                 inner join order_track_task_config c
                            on i.track_status = c.track_status
                 left join (
            select identifier, count(1) task_retry_count
            from order_track_task_log
            where track_status = #{trackStatus}
            group by identifier
        ) b
                           on i.identifier = b.identifier
        where c.enable = 1
          and c.retry_count > 0
          and i.track_status = #{trackStatus}
          and ifnull(b.task_retry_count, 0) > c.retry_count
    </select>

</mapper>