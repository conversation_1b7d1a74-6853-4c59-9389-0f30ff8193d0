<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.FlowableMapper">
    <update id="setRunVariable">
        update ACT_RU_VARIABLE set TEXT_=#{varValue} WHERE NAME_ = #{varName} and
        proc_inst_id_ = (select a.proc_inst_id_ from(select proc_inst_id_ from ACT_RU_VARIABLE where name_ = 'identifier' and text_ =#{identifier}) a);
    </update>
    <update id="setHisVariable">
        update ACT_HI_VARINST set TEXT_=#{varValue} WHERE NAME_ = #{varName} and
        proc_inst_id_ = (select a.proc_inst_id_ from(select proc_inst_id_ from ACT_HI_VARINST where name_ = 'identifier' and text_ =#{identifier}) a);
    </update>
</mapper>
