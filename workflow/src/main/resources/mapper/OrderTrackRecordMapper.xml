<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.OrderTrackRecordMapper">

    <select id="queryByTaskId" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where task_id = #{taskId}
    </select>

    <select id="getOrderTrackRecordListByIdentifier" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where identifier = #{identifier}
    </select>

    <select id="getExistsAttachTrackListByIdentifier" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where identifier = #{identifier}
          and attach_name_list is not null
          and attach_file_name is not null
    </select>

    <update id="updateAttachListById">
        update order_track_record
        set attach_list = #{attachList},attach_name_list = #{groupRealAttachNameList}
        where id = #{orderTrackRecordId}
    </update>

    <select id="getExistsAttachTrackCountByIdentifier" resultType="java.lang.Integer">
        select count(1)
        from order_track_record
        where identifier = #{identifier}
          and attach_name_list is not null
          and attach_file_name is not null
    </select>

    <select id="getLastByIdentifier" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where identifier = #{identifier}
        order by id desc
        limit 1
    </select>

    <update id="updateBasicInfoReplyContent">
        update sound_identy_basic_info
        set reply_content = #{replyJson},
            track_status  = 190
        where identifier = #{identifier}
    </update>

    <insert id="saveMessageLog">
        INSERT INTO message_send_log(`phone`, `message`, `send_time`, `order_identifier`, `receiver_name`, `send_type`)
        VALUES (#{mobile}, #{message}, #{sysTime}, #{identifier}, #{loginName}, #{type});
    </insert>

    <select id="getIdentifierCount" resultType="java.lang.Integer">
        select count(1) from sound_identy_basic_info where identifier = #{identifier}
    </select>

    <select id="getCurrentNodeTrackRecords" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where identifier = #{identifier}
        <if test="taskId != null and taskId != ''">
            and task_id = #{taskId}
        </if>
        order by create_time desc
        limit 1
    </select>

    <select id="getOrderTrackRecordsByCondition" resultType="com.asiainfo.sound.domain.OrderTrackRecord">
        select *
        from order_track_record
        where identifier = #{identifier}
        <if test="trackType != null and trackType != ''">
            and track_type = #{trackType}
        </if>
        <if test="startTime != null and startTime != ''">
            and handing_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and handing_time &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>
</mapper>
