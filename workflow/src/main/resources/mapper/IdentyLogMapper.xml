<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.daoFlow.IdentyLogDao">
    <insert id="insertIdentyLog">
        insert into sound_group_identy_log
        (identifier,identy_date,identy_time,identy_msg,identy_status)
        value (#{identifier},#{identyDate},#{identyTime},#{identyMsg},#{identyStatus})
    </insert>

    <select id="queryIdentyLogByIdentifier" resultType="com.asiainfo.sound.domain.po.IdentyLogPo">
        select identy_time as identyTime,
               identy_msg as identyMsg,
               identy_status as identyStatus
        from sound_group_identy_log
        where identy_status = 06 and identifier = #{identifier}
        order by identy_time
    </select>

</mapper>