<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.OrderTrackTaskLogMapper">

    <resultMap id="BaseResultMap" type="com.asiainfo.sound.domain.OrderTrackTaskLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="identifier" column="identifier" jdbcType="VARCHAR"/>
            <result property="identyType" column="identy_type" jdbcType="VARCHAR"/>
            <result property="identySubtype" column="identy_subtype" jdbcType="VARCHAR"/>
            <result property="trackStatus" column="track_status" jdbcType="INTEGER"/>
            <result property="responseCode" column="response_code" jdbcType="VARCHAR"/>
            <result property="responseResult" column="response_result" jdbcType="VARCHAR"/>
            <result property="paramContent" column="param_content" jdbcType="VARCHAR"/>
            <result property="countLimit" column="count_limit" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,identifier,identy_type,
        identy_subtype,track_status,response_code,
        response_result,param_content,count_limit,
        create_time
    </sql>
</mapper>
