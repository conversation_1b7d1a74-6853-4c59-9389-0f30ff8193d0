<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.daoFlow.ComplaintDao">
    <select id="getPeriodByYear" resultType="java.lang.String">
        SELECT distinct b.number
        FROM sound_identy_basic_info a
                 JOIN sound_identy_complaint_param_list b
                      ON a.identifier = b.identifier
        WHERE a.sender_flag = 1
          and b.number like concat(concat('%', #{year}), '%')
        order by b.number desc
    </select>

    <select id="selectTotalResultInfo" resultType="com.asiainfo.sound.domain.po.ComplaintResultPo">
        select count(a.identifier) as sumTotal,
        ifnull(sum(case when a.identy_status='03' then 1 else 0 end ),0) as completeTotal
        from sound_identy_basic_info a
        join sound_identy_complaint_param_list b
        on a.identifier = b.identifier
        where 1=1
        <if test="periods!=null and periods!=''">
            and b.number = #{periods}
        </if>
        <if test="year!=null and year!=''">
            and year(a.creat_date) = #{year}
        </if>
        and a.sender_flag = #{senderFlag,jdbcType=INTEGER}
        and a.identy_subtype = '0206'
    </select>

    <select id="selectReplyResultInfo" resultType="com.asiainfo.sound.domain.po.ComplaintResultPo">
        select ifnull(sum(case when h.param_status='01' then 1 else 0 end ),0) as unStart,
               ifnull(sum(case when h.param_status='02' then 1 else 0 end ),0) as running,
               ifnull(sum(case when h.param_status='03' then 1 else 0 end ),0) as finish
        from sound_identy_basic_info a
        join sound_identy_complaint_param_list b
        on a.identifier = b.identifier
        join ( SELECT a.identifier,a.param_status FROM (
        SELECT a.identifier,a.param_status FROM sound_identy_handle_his a
        join sound_identy_basic_info b
        on a.identifier = b.identifier
        where b.identy_subtype = '0206'
        ORDER BY identifier ASC,handing_time DESC
        ) a GROUP BY identifier) h
        on a.identifier = h.identifier
        where 1=1
        <if test="periods!=null and periods!=''">
            and b.number = #{periods}
        </if>
        <if test="year!=null and year!=''">
            and year(a.creat_date) = #{year}
        </if>
        and a.sender_flag = #{senderFlag,jdbcType=INTEGER}
        and a.identy_subtype = '0206'
    </select>

    <select id="selectStatementResultInfo" resultType="com.asiainfo.sound.domain.po.ComplaintResultPo">
        select
        ifnull(sum(CASE WHEN c.identifier IS NOT NULL and b.completion_Time <![CDATA[ < ]]>DATE_FORMAT(c.handing_time,'%Y%m%d') THEN 1 ELSE 0 END),0) as errorEnd,
        ifnull(sum(CASE WHEN c.identifier IS NULL and b.completion_Time <![CDATA[ < ]]> DATE_FORMAT(NOW(),'%Y%m%d') THEN 1 ELSE
        0 END),0) as wait
        from sound_identy_basic_info a
        join sound_identy_complaint_param_list b
        on a.identifier = b.identifier
        LEFT JOIN sound_identy_apply_handle_his c
        ON a.identifier = c.identifier AND c.state = '03'
        where 1=1
        <if test="periods!=null and periods!=''">
            and b.number = #{periods}
        </if>
        <if test="year!=null and year!=''">
            and year(a.creat_date) = #{year}
        </if>
        and a.sender_flag = #{senderFlag,jdbcType=INTEGER}
        and a.identy_subtype = '0206'
    </select>

    <select id="selectTimeReplyInfo" resultType="com.asiainfo.sound.domain.po.ComplaintResultPo">
        select
            ifnull(sum(CASE WHEN c.identifier IS NOT NULL and b.completion_Time <![CDATA[ >= ]]> c.handing_time THEN 1 ELSE 0 END),0) as timeReply,
            ifnull(sum(CASE WHEN c.identifier IS NOT NULL and b.completion_Time <![CDATA[ < ]]> c.handing_time THEN 1 ELSE 0 END),0) as untimeReply,
            ifnull(sum(CASE WHEN c.identifier IS NULL and b.completion_Time <![CDATA[ >= ]]> DATE_FORMAT(NOW(),'%Y%m%d') THEN 1 ELSE 0 END),0) as timeNoreply,
            ifnull(sum(CASE WHEN c.identifier IS NULL and b.completion_Time <![CDATA[ < ]]> DATE_FORMAT(NOW(),'%Y%m%d') THEN 1 ELSE 0 END),0) as untimeNoreply
        from sound_identy_basic_info a
        join sound_identy_complaint_param_list b
        on a.identifier = b.identifier
        LEFT JOIN sound_identy_handle_his c
        ON a.identifier = c.identifier AND c.param_status = '03'
        where 1=1
        <if test="periods!=null and periods!=''">
            and b.number = #{periods}
        </if>
        <if test="year!=null and year!=''">
            and year(a.creat_date) = #{year}
        </if>
        and a.sender_flag = #{senderFlag,jdbcType=INTEGER}
        and a.identy_subtype = '0206'
    </select>
</mapper>