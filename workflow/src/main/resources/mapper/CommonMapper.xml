<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.daoFlow.CommonDao">
    <select id="selectIdentyDetail" resultType="com.asiainfo.sound.domain.po.IdentyDetailPo">
        select identy_detail_id as identyDetailId,
               identy_detail_desc as identyDetailName,
               identy_subtype as identySubtype,
               identy_subtype_name as identySubtypeName
        from sound_dim_identy_detail_code
        where data_type = #{identyType}
    </select>
    <select id="selectProblemType" resultType="com.asiainfo.sound.domain.po.ProblemTypePo">
        select identy_detail_id as identyDetailId,
               identy_detail_desc as identyDetailName,
               identy_subtype as identySubtype,
               identy_subtype_name as identySubtypeName,
               identy_type as identyType,
               identy_type_name as  identyName
        from sound_dim_identy_detail_code
        where data_type = #{identyType}
    </select>

    <select id="selectProblemTypeNew" resultType="com.asiainfo.sound.domain.po.ProblemTypeNew">
        select * from sound_dim_identy_detail_code_new
    </select>
</mapper>