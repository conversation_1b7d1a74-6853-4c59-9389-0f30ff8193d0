<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.OrderTrackAttachMapper">

    <resultMap id="BaseResultMap" type="com.asiainfo.sound.domain.OrderTrackAttach">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="attachType" column="attach_type" jdbcType="INTEGER"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="originalFileName" column="original_file_name" jdbcType="VARCHAR"/>
            <result property="groupFileName" column="group_file_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="identifier" column="identifier" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,attach_type,file_name,
        original_file_name,group_file_name,create_time,
        identifier
    </sql>
</mapper>
