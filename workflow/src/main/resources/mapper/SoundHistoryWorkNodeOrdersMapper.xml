<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~     Copyright (c) 2021-2030, SRD-BD ai-cloudx-team All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~  Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the bd.srd.ai.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: SRD-BD ai-cloudx-team
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.asiainfo.sound.dao.SoundHistoryWorkNodeOrdersMapper">

  <resultMap id="soundHistoryWorkNodeOrdersMap" type="com.asiainfo.sound.domain.entity.SoundHistoryWorkNodeOrders">
                  <id property="id" column="id"/>
                        <result property="identifier" column="identifier"/>
                        <result property="nodeOrder" column="node_order"/>
                        <result property="userNo" column="user_no"/>
                        <result property="userName" column="user_name"/>
                        <result property="status" column="status"/>
                        <result property="content" column="content"/>
                        <result property="identyStartTime" column="identy_start_time"/>
                        <result property="identyEndTime" column="identy_end_time"/>
                        <result property="identyHandTime" column="identy_hand_time"/>
            </resultMap>

</mapper>








