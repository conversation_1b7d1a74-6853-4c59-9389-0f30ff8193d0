<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.UserDao">

    <select id="selectUserList" parameterType="com.asiainfo.sound.domain.dto.UserDto" resultType="com.asiainfo.sound.domain.vo.UserVo">
        select id as id ,  name as username , login_name as userid, login_name as userid , oa_id as oaId
        from dim_lkg_staff
        where del_flag = '0' and login_name != ''
        <if test="userName!=null and userName!=''">
          and  name  like CONCAT('%',#{userName},'%')
        </if>
    </select>

    <select id="selectDepartment" resultType="com.asiainfo.sound.domain.vo.DepartmentVo">
        select  dept_name as dapartmentName,
                dept_id as dapartmentId,
                dept_id as id
        from sys_dept
    </select>

    <select id="selectUserHandlerInfo" resultType="com.asiainfo.sound.domain.req.ReplyCssReq">
       SELECT name as handler,
              mobile as handlerInfor,
              post_name as handlerRank,
              org_name as handingDepartment
       FROM dim_lkg_staff WHERE login_name = #{userId}
    </select>

    <select id="selectAllUserInfo" resultType="com.asiainfo.sound.domain.vo.SelectBean">
        select login_name as value, name as label from dim_lkg_staff;
    </select>

    <!--todo 角色表已与工作流用户组分开-->
    <select id="selectUserRoleInfos" resultType="com.asiainfo.sound.domain.dto.UserRoleDto">
      SELECT u.login_name AS userId,
             c.role_id AS roleId,
             c.role_type as roleType
      FROM dim_lkg_staff u
               JOIN identy_user_role c
                    ON u.login_name = c.user_id
      WHERE u.login_name = #{userId}
    </select>
    <select id="selectUserByUserId" resultType="com.asiainfo.sound.domain.vo.UserVo">
        select  name as username ,
                login_name as userid ,
                mobile as mobilePhone
        from dim_lkg_staff u
        where u.login_name = #{userId}
    </select>

    <select id="selectUserInfoByRoleName" resultType="com.asiainfo.sound.domain.dto.FlowSiteUserDto">
        select iurr.user_id as userName
        from identy_user_role_relation iurr
        where iurr.role_id = #{roleName}
    </select>

    <select id="selectUserListByRoleId" resultType="com.asiainfo.sound.domain.vo.SelectBean">
        select su.login_name as value,
               name as label
        from dim_lkg_staff su
            join identy_user_role_relation iurr
        on su.login_name = iurr.user_id
        where iurr.role_id = #{roleId}
    </select>

    <delete id="deleteIdentyUserRoleByRoleId">
        delete from identy_user_role_relation where role_id = #{roleId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertIdentyUserRoleRelation">
        insert into identy_user_role_relation
        (user_id,role_id)
        VALUES
        <foreach collection="userList" item="userId" separator=",">
            (#{userId},#{roleId})
        </foreach>
    </insert>

    <select id="queryAllDepartment" resultType="com.asiainfo.sound.domain.vo.SelectRootBean">
        select dept_id as value ,
        dept_name as label,
        order_num as orderNum,
        parent_id as parent,
        ancestors
        from sys_dept
        where del_flag = '0' order by orderNum asc
    </select>

    <select id="queryDepartmentUsers" resultType="com.asiainfo.sound.domain.vo.SelectBean">
        select d.login_name as value, d.name as label from dim_lkg_staff d
        where d.dept_id = #{deptId,jdbcType=VARCHAR} order by order_num asc
    </select>
    <select id="queryDepartmentName" resultType="java.lang.String">
        SELECT dept_name from sys_dept where dept_id=#{deptId}
    </select>
    <select id="selectGridUserInfo" resultType="java.lang.String">
        select login_name from dim_lkg_staff where name= #{name} and mobile=#{phone};
    </select>
    <select id="queryUsersByPostAndOrg" resultType="com.asiainfo.sound.domain.vo.SelectBean">
        select login_name as value, name as label from dim_lkg_staff
        where org_name = #{orgName} and post_name in
        <foreach collection="postNameList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 20
    </select>
    <select id="selectOrgName" resultType="java.lang.String">
        select org_name from dim_lkg_staff where login_name= #{loginName};
    </select>
    <select id="selectHomeWideWarnUserInfo" resultType="java.lang.String">
        select login_name from dim_lkg_staff where mobile=#{phone};
    </select>

    <select id="selectCityByGrid" resultType="java.util.Map">
        SELECT any_value(city_name),any_value(sub_name),sub_id,any_value(area_name) FROM ods_md_bs_agent_grid_cfg_day_p where sub_id in
        <foreach collection="gridList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach> GROUP BY sub_id;
    </select>

    <select id="selectOrgInfo" resultType="java.util.Map">
       SELECT login_name,org_name FROM dim_lkg_staff WHERE login_name in
        <foreach collection="loginNameList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </select>
</mapper>
