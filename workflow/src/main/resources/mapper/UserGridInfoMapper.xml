<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.UserGridInfoMapper">
    <sql id="Base_Column_List">
        id,city,county,grid_id,grid_name,grid_manager,grid_auditor,grid_readers,second_grid_auditor
    </sql>
    <select id="selectInfoByGridId" resultType="com.asiainfo.sound.domain.UserGridInfo">
        select
        <include refid="Base_Column_List"/>
        from user_warning_99 where grid_id=#{gridId}
    </select>

    <select id="selectHomeWideWarnStaff" resultType="com.asiainfo.sound.domain.HomeWideWarnStaff">
        select * from home_wide_warn_staff where area=#{area};
    </select>
    <insert id="insertLoginLog">
        INSERT INTO workflow_login_log
        (login_name, login_type, login_time)
        VALUES(#{loginName},#{loginType},#{loginTime});
    </insert>

    <select id="selectUserTask12" resultType="Map">
        select * from user_task_12 where dept = #{dept}
    </select>

    <select id="selectDeptTask12" resultType="java.lang.String">
        select dept from user_task_12_dept where group_name like concat('%',#{dept},'%') limit 1
    </select>
</mapper>
