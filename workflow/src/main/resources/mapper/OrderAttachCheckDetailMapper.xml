<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.OrderAttachCheckDetailMapper">

    <select id="getCheckSuccessCount" resultType="java.lang.Integer">
        select count(1)
        from order_attach_check_detail
        where result = '成功' and file_name in
        <foreach collection="list" item="attach" open="(" separator="," close=")">
            #{attach}
        </foreach>
    </select>

</mapper>
