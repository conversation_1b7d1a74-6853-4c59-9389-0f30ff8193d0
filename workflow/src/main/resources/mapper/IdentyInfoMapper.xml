<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.asiainfo.sound.dao.IdentyInfoDao">
    <insert id="insertIdentyBasicInfo">
        insert into sound_identy_basic_info(
        identifier,identy_type,identy_subtype,identy_detail,title,content,
        origin_unit,receiver_unit,creat_time,process_time,creator,creator_contact_info,
        attach_list,identy_status,sender_flag,creat_date,user_id
        )value (#{identifier},#{identyType},#{identySubtype},#{identyDetail},#{title},
        #{content},#{originUnit},#{receiverUnit},#{creatTime},#{processTime},#{creator},
        #{creatorContactInfo},#{attachList},#{identyState},#{senderFlag},#{creatDate},#{userId})
    </insert>
        <update id="modifyIdentyBasicState">
        update sound_identy_basic_info
        set identy_status = #{identyState}
        where identifier = #{identifier}
    </update>

    <insert id="insertDyPortalDealTask">
        insert into portal_todo_deal_task(
        op_time,order_type,task_id,order_id,order_name,order_detail,user_name,del_flag
        )value(#{opTime},#{orderType},#{taskId},#{orderId},#{orderName},#{orderDetail},#{userName},#{delFlag})
    </insert>
    <insert id="insertOriginFileNameAndNew">
        INSERT INTO order_track_attach
        (attach_type, file_name, original_file_name)
        VALUES('1',#{newFileName},#{fileName});

    </insert>
    <update id="insertFowardCompany">
        update sound_identy_basic_info
        set  forward_company_0302 = #{forwardCompany}
        where identifier= #{id}
    </update>
    <update id="insertGroupHandleType">
        update sound_identy_basic_info
        set  operation_type_0302 = #{operationType}
        where identifier= #{identifier}
    </update>

    <update id="modifyDyPortalDealTask">
        update portal_todo_deal_task
        set del_flag = #{delFlag}
        where task_id = #{taskId}
    </update>
    <update id="updateTrackStatus">
        update sound_identy_basic_info set track_status = '192' where identifier= #{identifier}
    </update>
    <update id="updateTrackInfo">
        update order_track_attach set identifier=#{identifier} where file_name=#{fileName}
    </update>

    <delete id="deleteIdentyBasicInfo">
        delete from sound_identy_basic_info where identifier = #{identifier,jdbcType=VARCHAR}
    </delete>

    <select id="getIdentySubType" resultType="java.lang.String">
        select identy_subtype
        from sound_identy_basic_info
        where identifier = #{identifier}
    </select>
    <select id="getIdentyDetail" resultType="java.lang.String">
        select identy_detail
        from sound_identy_basic_info
        where identifier = #{identifier}
    </select>
    <select id="getFowardCompany" resultType="java.lang.String">
        select forward_company_0302
        from sound_identy_basic_info
        where identy_detail = '03030201'
          and identifier = #{identifier}
          and operation_type_0302 in ('0', '1')
    </select>


    <select id="getVariableByIdentifier" resultType="java.util.HashMap">
        select text_,name_,PROC_INST_ID_ from ACT_RU_VARIABLE where PROC_INST_ID_ in
        (
        select PROC_INST_ID_ from ACT_RU_VARIABLE where name_='identifier' and text_ in
        <foreach collection="identifierList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        and name_ in
        <foreach collection="varList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getHisVariableByIdentifier" resultType="java.util.HashMap">
        select text_,name_,PROC_INST_ID_ from ACT_HI_VARINST where PROC_INST_ID_ in
        (
        select PROC_INST_ID_ from ACT_HI_VARINST where name_='identifier' and text_ in
        <foreach collection="identifierList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
        and name_ in
        <foreach collection="varList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTwoTypeErrorIdenty" resultType="com.asiainfo.sound.domain.dto.TwoTypeErrorDto">
        select identifier,identy_status
        from sound_identy_basic_info
        where identy_subtype = '0099'
        <choose>
            <when test="startTime!=null and startTime!=''">
                and date_format(creat_date,'%Y%m') >= #{startTime} and date_format(creat_date,'%Y%m') <![CDATA[ <= ]]> #{time}
            </when>
            <otherwise>
                and date_format(creat_date,'%Y-%m') = #{time}
            </otherwise>
        </choose>
    </select>

    <select id="queryWorkflowStatusByMonth" resultType="com.asiainfo.sound.domain.dto.WorkflowStatusDto">
        select a.identy_subtype,any_value(b.identy_name) identy_name,a.sender_flag,count(1) total,count(a.identy_status='02'or a.identy_status='03' or a.track_status is not null or null) complete
        from sound_identy_basic_info a left join sound_identy_dim_workflow b on a.identy_subtype=b.identy_subtype
        <choose>
            <when test="startTime!=null and startTime!=''">
                where date_format(creat_date,'%Y%m') >= #{startTime} and date_format(creat_date,'%Y%m') <![CDATA[ <= ]]> #{time} and a.identy_subtype != '0007'
                group by a.identy_subtype,case sender_flag when '2' then '0' else sender_flag end order by field(sender_flag,2,0,1),total desc;
            </when>
            <otherwise>
                where date_format(creat_date,'%Y%m') = #{time}
                group by a.identy_subtype,case sender_flag when '2' then '0' else sender_flag end order by field(sender_flag,2,0,1),total desc;
            </otherwise>
        </choose>
        <!--  a.identy_subtype == '0007'是 触点感知洞察预警单,工单有问题,不统计 -->
    </select>

    <select id="queryUncompleteIdentifier" resultType="java.lang.String">
        select identifier from sound_identy_basic_info where identy_subtype = #{identySubtype} and ((identy_status = '01' and track_status is null) or identy_status in('04','05','06','07','100'))
        <choose>
            <when test="startTime!=null and startTime!=''">
                 and date_format(creat_date,'%Y%m') >= #{startTime} and date_format(creat_date,'%Y%m') <![CDATA[ <= ]]> #{time}
            </when>
            <otherwise>
                 and date_format(creat_date,'%Y%m') = #{time}
            </otherwise>
        </choose>
    </select>

<!--    <select id="queryUncompleteAssignee" resultType="java.lang.String">-->
<!--        select assignee_ from ACT_HI_TASKINST where end_time_ is null and proc_inst_id_ =-->
<!--        (select proc_inst_id_ from ACT_HI_VARINST where name_ = 'identifier' and text_ = #{identifier});-->
<!--    </select>-->
    <select id="queryUncompleteAssignee" resultType="java.lang.String">
        select any_value(assignee_) from ACT_HI_TASKINST where end_time_ is null and proc_inst_id_ in
        (select proc_inst_id_ from ACT_HI_VARINST where name_ = 'identifier' and text_ in
        <foreach collection="identifierList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>) GROUP BY proc_inst_id_;
    </select>
</mapper>