external:
    css:
        url: 10.254.123.224:13107
    token:
        url: 10.254.123.224:13107
oa:
    id: 25
    name: 不需要
    pwd: 0B9D4C530337EB276F3684FBBBB5AA9F
    # 将代办推送到oa - 负载均衡
    pushUrl: http://10.254.123.166:9080
    # 将代办推送到oa - 测试
    #pushUrl: http://10.254.123.162:9080
    sso-type: oa
    url-for-task: /login?userName=jindongxun&redirect=/order/00
moa:
    #auth-url: http://10.254.123.75:8080/moa/MOA_NX_CMCC/api/account/authentication,http://10.254.123.75:8080/moa/MOA_NX_CMCC/api/account/authentication
    auth-url: http://10.254.123.75:8085/moa/MOA_NX_CMCC/api/account/authentication,http://10.254.123.75:8086/moa/MOA_NX_CMCC/api/account/authentication
    #auth-url2: http://10.254.123.207:28899/MOA_NX_CMCC/checkingToken
    auth-url2: http://10.254.123.209:28899/moa/MOA_NX_CMCC/checkingToken,http://10.254.123.211:28899/moa/MOA_NX_CMCC/checkingToken
grid:
    #auth-url: http://10.254.123.13:19010/fuseapp_web/finedo/login4a/gridverify?token=
    auth-url: http://10.254.123.13:9010/fuseapp_web/finedo/login4a/gridverify?token=
sound:
    messageSendFlag: 0
    env:
        flag: 0
    identy: # 工作流附件
        input: ./data/ouput
        output: ./data/input
        model: ./data/model
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: ************************************,10.102.42.86:17700,10.102.42.88:17700/dypt?targetServerType=master&currentSchema=workflow_gray&useUnicode=true&characterEncoding=utf-8
                username: nx_dayin
                password: 61#U5rYg!s
            slave:
                # 从数据源开关/默认关闭
                enabled: true
                url: ************************************,10.102.42.86:17700,10.102.42.88:17700/dypt?targetServerType=master&currentSchema=uni_portal_nx&useUnicode=true&characterEncoding=utf-8
                username: nx_dayin
                password: 61#U5rYg!s
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
    jpa:
        database-platform: org.hibernate.dialect.PostgreSQLDialect
    redis:
        database: 3
        host: **************
        password: nxdy@123
        port: 16379
        timeout: 10000
    session:
        store-type: redis
static:
    file:
        dir: ./upload/
use_portal_token_verify: false # 是否使用门户单点登录



#sftp
sftp:
  #ip: localhost
  ip: ************
  port: 35
  user: sfcsv9511
  passwd: Sfcsv_951!
  outpath: /outgoing/csvc/
  inpath: /incoming/csvc/



# Swagger配置
swagger:
    # 是否开启swagger
    enabled: true
    # 请求前缀
    pathMapping: graydoc/
