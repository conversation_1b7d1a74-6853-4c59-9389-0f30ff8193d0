server:
  port: 10101
desIV: 12345678
desKey: saJUmccyw8$8juz
response-body-advice:
  enabled: false
knife4j:
  basic:
    enable: false
  production: false
mybatis:
  check-config-location: true
  configuration:
    cache-enabled: true
    map-underscore-to-camel-case: true
  mapUnderscoreToCamelCase: true
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    # 逻辑删除配置
    db-config:
      # 删除前
      logic-not-delete-value: 0
      # 删除后
      logic-delete-value: 1
portal_url: http://nx-portal.asiainfo.work/nxportal # 门户地址

# CORS配置
cors:
  allowed-origins: http://**************:8080,http://**************:8090,http://localhost:8080,http://localhost:10080,http://127.0.0.1:8080,http://127.0.0.1:10080,http://localhost:8888,http://localhost:10101

# 禁用框架的CORS配置

spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  application:
    name: sound-service
  datasource:
    dynamic:
      datasource:
        master:
          mapperPackage: com.asiainfo.sound.dao
  profiles:
    active: dev
  session:
    timeout: 8h
  liquibase:
    change-log: classpath:db/master.xml
    enabled: false
  artemis:
    concurrency: 10
    offset: 15
    host: localhost
    port: 61616
    user: default
    password: 6Crh5u
    mode: native
    jmsname: nx_attachment_check_prod_753922
ws: #对接4A OA相关配置
  appAcctId: ''
  methedDY4A: CheckAiuapToken
  methedDYOA: CheckOATokenSoap
  nameSpaceDY4A: http://**************:8003/uac/services/CheckAiuapToken
  nameSpaceDYOA: http://service.app.jilin.local.a4.asiainfo.com
  serviceIdDY: JLDYPT
  wsdlUrlDY4A: http://**************:8003/uac/services/CheckAiuapToken?wsdl
  wsdlUrlDYOA: http://*************:8080/uap/services/CheckOATokenSoap?wsdl
flowable:
  database-schema-update: none
  # 流程图字体配置，解决中文乱码问题
  font:
    activity: SimSun      # 活动节点字体
    label: SimSun         # 标签字体  
    annotation: SimSun    # 注释字体



# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*



# token配置
token:
  # 令牌自定义标识
  header: empty
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30


# 项目相关配置
ai:
  # 名称
  name: test
  # 版本
  version: 2.0.2
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径
  profile: ./uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 是否需要验证码
  requiredCaptcha: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: char
  # 启动初始化脚本
  initDB: false
  # 初始加载缓存
  initCache: false

logging:
  level:
    org:
      flowable: debug
