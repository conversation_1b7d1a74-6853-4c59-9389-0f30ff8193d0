<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <property name="log.path" value="logs"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/log-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date %X [%thread] %file:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.baomidou.mybatisplus.core.metadata.TableInfoHelper" level="error"/>
    <logger name="com.huawei.insa2.comm.cmpp.message.CMPPActiveRepMessage" level="OFF"/>
    <logger name="com.ai.srd.bd.framework.filter.LogCostFilter" level="warn"/>
    <logger name="org.flowable.engine.impl.persistence.entity" level="warn" />
    <logger name="org.flowable.task.service.impl.persistence.entity" level="warn" />
    <logger name="com.ai.srd.bd.framework.datasource.DynamicDataSourceContextHolder" level="warn" />
    <logger name="com.baomidou.dynamic.datasource.aop.DynamicDataSourcePackageInterceptor" level="warn" />
    <logger name="com.asiainfo.sound.dao" level="debug" />
    <logger name="com.asiainfo.sound.daoFlow" level="info" />
    <logger name="logging.level.org.flowable" level="debug" />

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>
</configuration>
