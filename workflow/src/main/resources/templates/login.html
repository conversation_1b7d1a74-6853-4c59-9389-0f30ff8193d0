<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Login</title>
  <link rel="stylesheet" href="../static/lib/kitadmin/css/layui.css" th:href="@{lib/kitadmin/css/layui.css}">
  <link rel="stylesheet" href="../static/lib/kitadmin/css/login.css" th:href="@{lib/kitadmin/css/login.css}">
  <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
  <script>
    if (document.getElementsByClassName('layui-layout-body').length > 0) {
        location.href = '/login.html';
    }
</script>
</head>

<body>
  <div class="kit-login">
    <div class="kit-login-bg"></div>
    <div class="kit-login-wapper">
      <h2 class="kit-login-slogan">欢迎使用 <br> AiBootPlus 系统开发模板</h2>
      <div class="kit-login-form">
        <h4 class="kit-login-title">登录</h4>
        <form class="layui-form" type="POST" action="">
          <div class="kit-login-row">
            <div class="kit-login-col">
              <i class="layui-icon">&#xe612;</i>
              <span class="kit-login-input">
                            <input type="text" name="username" autocomplete="current-username" lay-verify="required" class="layui-input" placeholder="用户名/邮箱/手机号" />
                        </span>
            </div>
            <div class="kit-login-col"></div>
          </div>
          <div class="kit-login-row">
            <div class="kit-login-col">
              <i class="layui-icon">&#xe64c;</i>
              <span class="kit-login-input">
                            <input type="password" name="password" autocomplete="current-password"  lay-verify="required" class="layui-input" placeholder="密码" />
                        </span>
            </div>
            <div class="kit-login-col"></div>
          </div>

          <div class="layui-row layui-col-space5" style="margin:0 0 10px 0">
            <div class="layui-col-xs6">
              <input type="text" name="validateCode" lay-verify="required" class="layui-input" placeholder="输入验证码" maxlength="5" />
            </div>
            <div class="layui-col-xs6" style="line-height: 38px;">
              <a href="javascript:void(0);" title="点击更换验证码">
                <img th:src="@{/get-kaptcha-image(type=${captchaType})}" class="imgcode" width="50%" />
              </a>
            </div>
          </div>


          <div class="kit-login-row">
            <div class="kit-login-col">
              <input type="checkbox" name="rememberMe" value="true" title="记住帐号" lay-skin="primary">
            </div>
          </div>
          <div class="kit-login-row">
            <button class="layui-btn kit-login-btn" lay-submit="submit" lay-filter="login_hash">登录</button>
          </div>
          <div class="kit-login-row" style="margin-bottom:0;">
            <a href="javascript:;" style="color: rgb(153, 153, 153); text-decoration: none; font-size: 13px;" id="forgot">忘记密码</a>
          </div>
        </form>
      </div>
    </div>
  </div>
  <script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; </script>
  <script src="../static/lib/kitadmin/polyfill.min.js" th:src="@{/lib/kitadmin/polyfill.min.js}"></script>
  <script src="../static/lib/kitadmin/layui.js" th:src="@{/lib/kitadmin/layui.js}"></script>
  <script>
    //'axios', 'lodash'
    layui.use(['layer', 'form'], function() {
      var form = layui.form,
        //axios = layui.axios,
        $ = layui.jquery;
      //_ = layui.lodash;

      $('.imgcode').click(function() {
        var url = ctx + "/get-kaptcha-image?type=" + captchaType + "&s=" + Math.random();
        $(".imgcode").attr("src", url);
      });

      $('#forgot').on('click', function() {
        layer.msg('请联系管理员.');
      });

      //监听提交
      form.on('submit(login_hash)', function(data) {
        var layIndex = layer.load(2, {
          shade: [0.1, '#393D49']
        });
        $.ajax({
          type: "post",
          url: ctx + "login",
          data: data.field,
          success: function(r) {
            if (r.code == 0) {
              location.href = ctx + 'index';
            } else {
              layer.msg(r.msg);
              layer.closeAll('loading');
            }
          },
          error:function(){
            layer.closeAll('loading');
            layer.msg("位置错误！");
          }
        });

        return false;
         });
    });



    // function getParams(href) {
    //   var p = href.substr(href.indexOf('?') + 1);
    //   if (href === p) return null;
    //   var params = p.split('&');
    //   var data = {};
    //   _.forEach(params, function(item, index) {
    //     var kv = item.split('=');
    //     var key = kv[0];
    //     var value = kv[1];
    //     data[key] = value;
    //   });
    //   return data;
    // }
  </script>
</body>

</html>