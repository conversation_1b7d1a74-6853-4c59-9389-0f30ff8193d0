<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>Sumslack后台管理模板</title>
	<!-- build:css -->
	<link rel="stylesheet" href="../static/lib/kitadmin/css/layui.css" th:href="@{lib/kitadmin/css/layui.css}">
	<link rel="stylesheet" href="../static/lib/kitadmin/css/theme/default.css" th:href="@{lib/kitadmin/css/theme/default.css}" id="theme">
	<link rel="stylesheet" href="../static/lib/kitadmin/css/nprogress.css" th:href="@{lib/kitadmin/css/nprogress.css}">
	<!-- endbuild -->
</head>

<body class="layui-layout-body kit-theme-default">
	<div class="layui-layout layui-layout-admin">
		<!-- header -->
		<div th:replace="inc/top"></div>
		<!-- silds -->
		<div th:replace="inc/left"></div>
		<!-- main -->
		<div class="layui-body" kit-body="true">
			<router-view></router-view>
		</div>
		<!-- footer -->
		<div th:replace="inc/footer"></div>
	</div>
	<!-- build:js -->
	<script th:inline="javascript"> var ctx = [[@{/}]]; </script>

	<script src="../static/lib/kitadmin/polyfill.min.js" th:src="@{/lib/kitadmin/polyfill.min.js}"></script>
	<script src="../static/lib/kitadmin/layui.js" th:src="@{/lib/kitadmin/layui.js}"></script>
	<script src="../static/lib/kitadmin/kitadmin.js" th:src="@{/lib/kitadmin/kitadmin.js}"></script>
	<!-- endbuild -->
	<!-- build:use -->
	<script>
		layui.config({
			base: 'js/'
		}).use('index');
	</script>
	<!-- endbuild -->
</body>

</html>