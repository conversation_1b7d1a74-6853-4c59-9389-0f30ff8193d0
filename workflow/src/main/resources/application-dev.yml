external:
  css:
    url: localhost:10101
  token:
    url: localhost:10101


oa:
  id: 25
  name: 不需要
  pwd: 0B9D4C530337EB276F3684FBBBB5AA9F
  # 将代办推送到oa - 负载均衡
  pushUrl: http://**************:9080
  # 将代办推送到oa - 测试
  #pushUrl: http://**************:9080
  sso-type: 4a
  url-for-task: /login?userName=jindongxun&redirect=/order/00
  group:
    token-url: http://localhost:10101/testOAGroupGetToken
    auth-url: http://localhost:10101/testOAGroupAuthentication
    redirect-url: http://localhost/demo?userName=%s&redirect=workflow
    client-id: uni_1740_nx_nxdy
    app-id: 20240
    app-key: 88e5ddeda0d5a4ab639544507ae98330
    client-secret: IvM75ERREIbOunDv5WGnoMr2RVgUJbH5T0QROg6QvgE=


moa:
  # 生产环境有多个地址,要做负载均衡
  auth-url: http://localhost:10101/testOAAuthentication,http://localhost:10101/testOAAuthentication
  auth-url2: http://localhost:10101/testOAAuthentication,http://localhost:10101/testOAAuthentication
  group:
    auth-url: http://localhost:10101/testOAAuthentication
    app-id: sndypt
grid:
  auth-url: http://localhost:10101/testGridAuthentication?token=
sound:
  #短信发送标识 0不发 1-发
  messageSendFlag: 0
  env:
    flag: 0
  identy: # 工作流附件
    # 定时从集团outpath配置的对应路径，将文件剪切到本地这里，GZLSEND_1001_951_20220826_002.xls
    input: ./data/ouput
    output: ./data/input
    model: ./data/model
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************************************************************************************************************************************************************
        username: root
        password: griddev3
#        url: ****************************************************************************************************************************************************************************************************************************************
#        username: root
#        password: 123456
      slave:
        enabled: true
        url: ***********************************************************************************************************************************************************************************************************
        username: root
        password: griddev3
#        url: ******************************************************************************************************************************************************************************************************************************************
#        username: root
#        password: 123456

#      master:
#        url: **************************************************************************************************************************************************************************************************************************************
#        username: dev
#        password: devpwd01$%@
#      slave:
#        enabled: true
#        url: ***********************************************************************************************************************************************************************************************************
#        username: dev
#        password: devpwd01$%@
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: false
      testOnBorrow: false
      testOnReturn: false
  redis:
    database: 10
    host: redis.asiainfo.work
    password: asiainfo@123
    port: 31922
#    host: 127.0.0.1
#    port: 6379
#    timeout: 10000
  session:
    store-type: redis
  main:
    allow-bean-definition-overriding: true
static:
  file:
    dir: ./upload/
use_portal_token_verify: false # 是否使用门户单点登录
#sftp
sftp:
  ip: ftp.asiainfo.work
  port: 31357
  user: sftp
  passwd: sftp
  # 会定时从集团sftp这个文件下载到本地，下载后会删除这里的对应文件
  outpath: /upload/outgoing
  inpath: /upload/incoming
