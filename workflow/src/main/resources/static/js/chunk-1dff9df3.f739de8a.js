(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1dff9df3"],{5055:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"debug"},[n("div",{staticClass:"debug-show"},[n("div",{staticClass:"debug-send-box"},[n("el-input",{attrs:{disabled:!0},model:{value:e.restInfo.restSerialNo,callback:function(t){e.$set(e.restInfo,"restSerialNo",t)},expression:"restInfo.restSerialNo"}},[n("template",{slot:"prepend"},[e._v("接口ID")]),n("el-button",{attrs:{slot:"append"},on:{click:e.handleCall<PERSON><PERSON>},slot:"append"},[e._v("发送")])],2)],1),n("el-checkbox",{model:{value:e.mock,callback:function(t){e.mock=t},expression:"mock"}},[e._v("mock")]),n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"请求头部",name:"requestHeader"}},[n("el-button",{staticClass:"add-btn",attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增 ")]),n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.table,border:""}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"requestHead",label:"请求头",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-input",{attrs:{size:"mini"},on:{change:function(n){return e.requestChange(t)}},model:{value:t.row.requestHead[t.$index].headKey,callback:function(n){e.$set(t.row.requestHead[t.$index],"headKey",n)},expression:"scope.row.requestHead[scope.$index].headKey"}})]}}])}),n("el-table-column",{attrs:{prop:"content",label:"内容"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-input",{attrs:{size:"mini"},on:{change:function(n){return e.contentChange(t)}},model:{value:t.row.requestHead[t.$index].content,callback:function(n){e.$set(t.row.requestHead[t.$index],"content",n)},expression:"scope.row.requestHead[scope.$index].content"}})]}}])}),n("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(n){return e.handleDel(t)}}},[e._v("删除")])]}}])})],1)],1),n("el-tab-pane",{attrs:{label:"请求参数",name:"requestArgs"}},[n("div",{staticClass:"radio-box",on:{change:e.handleChecked}},[n("el-radio",{attrs:{label:"x-www-form-urlencoded",disabled:!0},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("x-www-form-urlencoded")]),n("el-radio",{attrs:{label:"form-data",disabled:!0},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("form-data")]),n("el-radio",{attrs:{label:"raw"},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("raw")]),n("span",[e._v("JSON(application/json)")])],1)])],1)],1),n("div",{staticClass:"main-content"},[e.displayRowEidt?n("div",[n("editor",{attrs:{width:"100%",height:"300",lang:"json"},on:{init:e.editorInit,input:e.inputJson},model:{value:e.restInfo.requestArgs,callback:function(t){e.$set(e.restInfo,"requestArgs",t)},expression:"restInfo.requestArgs"}})],1):e._e()]),n("div",{directives:[{name:"show",rawName:"v-show",value:e.hasResData,expression:"hasResData"}],staticClass:"response-info",on:{"tab-click":e.handleClickRes}},[n("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":"1",mode:"horizontal"}},[n("el-menu-item",{attrs:{index:"1"}},[e._v("响应信息")])],1),n("div",[n("json-viewer",{attrs:{value:e.resData,"expand-depth":4,copyable:"",sort:""}})],1)],1)])},i=[],s=(n("4de4"),n("d3b7"),n("a15b"),n("e9c4"),n("b0c0"),n("a434"),n("b75b")),o=n("c86f"),r=n("349e"),d=n.n(r),l={name:"DebugShow",props:["restInfoVO","restInfo"],data:function(){return{inputContent:"",activeName:"requestArgs",activeNameRes:"",table:[],requestHead:[],radio:"raw",displayRowEidt:!1,lang:"json",hasResData:!1,resData:"",innerContent:"{}",mock:!1}},components:{JsonViewer:d.a,EditorDebugShow:o["default"],editor:n("7c9e")},computed:{},created:function(){this.$route.params&&this.$route.params.id&&(this.id=this.$route.params.id)},mounted:function(){this.handleChecked();var e={name:"requestArgs"};this.handleClick(e)},watch:{requestArgs:function(e){this.innerContent=e}},methods:{handleClickRes:function(e,t){console.log(e,t)},debugEditorChange:function(e){this.$emit("debugEditorChange",e)},handleCallApi:function(){var e=this,t=this.mock,n="";if(0===this.requestHead.length)n=this.restInfo.restSerialNo;else{var a=[];this.requestHead.filter((function(e){var t=e.headKey+"="+e.content;a.push(t)}));var i=a.join("&");n=this.restInfo.restSerialNo+"?"+i}var o=JSON.stringify(JSON.parse(this.innerContent));1==t?Object(s["c"])(o,n,t).then((function(t){e.hasResData=!0,e.activeNameRes="requestHeader",e.resData=t.data})):Object(s["b"])(o,n).then((function(t){e.hasResData=!0,e.activeNameRes="requestHeader",e.resData=t.data}))},handleClick:function(e){"requestArgs"===e.name&&"raw"===this.radio?this.displayRowEidt=!0:this.displayRowEidt=!1},handleAdd:function(){var e={headKey:"",content:""};this.requestHead.push(e),this.table.push({requestHead:this.requestHead})},requestChange:function(e){},contentChange:function(e){},handleDel:function(e){this.requestHead.splice(e.$index,1),this.table.splice(e.$index,1)},handleChecked:function(){this.displayRowEidt="raw"===this.radio},editorInit:function(){n("2099"),n("1d29"),n("818b"),n("8a2a"),n("be9d"),n("0696"),n("bb36"),n("061c"),n("95b8"),n("b039"),n("e1a9")},inputJson:function(){}}},c=l,u=n("2877"),h=Object(u["a"])(c,a,i,!1,null,null,null);t["default"]=h.exports},c86f:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.debugResponse?n("div",[n("editor",{staticClass:"knife4j-debug-ace-editor",attrs:{options:e.debugOptions,value:e.value,lang:e.mode,theme:"eclipse",width:"100%",height:e.editorHeight},on:{input:e.change,init:e.editorInit}})],1):n("div",[n("editor",{attrs:{value:e.value,lang:e.mode,theme:"eclipse",width:"100%",height:e.editorHeight},on:{init:e.editorInit,input:e.change}})],1)])},i=[],s={name:"EditorShow",components:{editor:n("7c9e")},props:{value:{type:String,required:!0,default:""},mode:{type:String,required:!0,default:"json"},debugResponse:{type:Boolean,default:!1}},data:function(){return{editor:null,editorHeight:200,debugOptions:{readOnly:!1,autoScrollEditorIntoView:!0,displayIndentGuides:!1,fixedWidthGutter:!0},commonOptions:{readOnly:!1}}},methods:{resetEditorHeight:function(){var e=this;setTimeout((function(){var t=e.editor.session.getLength();1==t&&(t=15),t<15&&(t=e.debugResponse?30:15),t>20&&(e.debugResponse||(t=20));var n=16*t;e.editorHeight=n}),10)},change:function(e){this.debugResponse?this.$emit("debugEditorChange",e):(this.resetEditorHeight(),this.$emit("change",e))},editorInit:function(e){this.editor=e,n("2099"),n("1d29"),n("818b"),n("8a2a"),n("be9d"),n("0696"),n("bb36"),this.debugResponse?(this.editor.getSession().setUseWrapMode(!0),this.editor.setOptions(this.debugOptions),"text"==this.mode&&this.editor.getSession().setUseWrapMode(!0)):this.editor.setOptions(this.commonOptions),this.resetEditorHeight()}}},o=s,r=n("2877"),d=Object(r["a"])(o,a,i,!1,null,null,null);t["default"]=d.exports}}]);