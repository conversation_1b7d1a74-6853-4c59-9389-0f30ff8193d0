(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-375a043e"],{"1e4b":function(t,l,s){"use strict";s.r(l);var e=function(){var t=this,l=t.$createElement,s=t._self._c||l;return s("div",{staticClass:"app-container home"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{staticStyle:{"padding-left":"20px"},attrs:{sm:24,lg:12}},[s("h2",[t._v("SRD-BD 基础开发平台")]),s("p",[s("b",[t._v("当前版本:")]),t._v(" "),s("span",[t._v("v"+t._s(t.version))])]),s("h4",[t._v("框架使用情况")]),s("ul",[s("li",[t._v("湖北作战地图")])])]),s("el-col",{staticStyle:{"padding-left":"50px"},attrs:{sm:24,lg:12}},[s("el-row",[s("el-col",{attrs:{span:12}},[s("h2",[t._v("技术选型")])])],1),s("el-row",[s("el-col",{attrs:{span:6}},[s("h4",[t._v("后端技术")]),s("ul",[s("li",[t._v("SpringBoot 2.3.4")]),s("li",[t._v("Spring Security")]),s("li",[t._v("JWT 0.9.1")]),s("li",[t._v("Mybatis-Plus 3.4.1")]),s("li",[t._v("Druid 1.2.2")]),s("li",[t._v("Fastjson")]),s("li",[t._v("...")])])]),s("el-col",{attrs:{span:6}},[s("h4",[t._v("前端技术")]),s("ul",[s("li",[t._v("Vue")]),s("li",[t._v("Vuex")]),s("li",[t._v("Element-ui")]),s("li",[t._v("Axios")]),s("li",[t._v("Sass")]),s("li",[t._v("Quill")]),s("li",[t._v("...")])])])],1)],1)],1),s("el-divider"),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{xs:24,sm:24,md:12,lg:8}},[s("el-card",{staticClass:"update-log"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("联系信息")])]),s("div",{staticClass:"body"},[s("p",[s("i",{staticClass:"el-icon-s-promotion"}),t._v(" 文档："),s("el-link",{attrs:{href:"http://10.1.235.71/docs/back/component/basecomponent.html",target:"_blank"}},[t._v("http://10.1.235.71/docs")])],1),s("p",[s("i",{staticClass:"el-icon-user-solid"}),t._v(" 技术支持： SRD-BD 解决方案部技术委员会 ")])])])],1),s("el-col",{attrs:{xs:24,sm:24,md:12,lg:8}},[s("el-card",{staticClass:"update-log"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("更新日志")])]),s("el-collapse",{attrs:{accordion:""}},[s("el-collapse-item",{attrs:{title:"v1.0.0 - 2020-12-17"}},[s("ol",[s("li",[t._v("集成地图组件后端服务接口")]),s("li",[t._v("集成接口可视化开发平台")])])]),s("el-collapse-item",{attrs:{title:"v1.0.0 - 2020-12-15"}},[s("ol",[s("li",[t._v("SRD-BD2前后端分离系统基线版本正式发布")])])]),s("el-collapse-item",{attrs:{title:"v1.0.0 - 2020-12-10"}},[s("ol",[s("li",[t._v("SRD-BD2前后端分离系统-Springboot版本规划")])])])],1)],1)],1),s("el-col",{attrs:{xs:24,sm:24,md:12,lg:8}},[s("el-card",{staticClass:"update-log"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("关于系统")])]),s("div",{staticClass:"body"},[s("ul",[s("li",[t._v("内容1")]),s("li",[t._v("内容2")])])])])],1)],1)],1)},a=[],i={name:"index",data:function(){return{version:"1.0.0"}},methods:{goTarget:function(t){window.open(t,"_blank")}}},o=i,r=(s("97bb"),s("2877")),c=Object(r["a"])(o,e,a,!1,null,"4b8d7030",null);l["default"]=c.exports},"97bb":function(t,l,s){"use strict";s("f528")},f528:function(t,l,s){}}]);