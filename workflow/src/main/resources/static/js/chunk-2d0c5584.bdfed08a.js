(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c5584"],{"3f61":function(e,a,r){"use strict";r.r(a);var t=function(){var e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"区域名称",prop:"areaName"}},[r("el-input",{attrs:{placeholder:"请输入区域名称",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.areaName,callback:function(a){e.$set(e.queryParams,"areaName",a)},expression:"queryParams.areaName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"cyan",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.areaTopData,"row-key":"areaCode",lazy:"",load:e.load,"tree-props":{children:"children",hasChildren:"hasChild"}}},[r("el-table-column",{attrs:{prop:"areaName",label:"区域名称",width:"180"}}),r("el-table-column",{attrs:{prop:"areaCode",label:"区域编码",width:"180"}}),r("el-table-column",{attrs:{prop:"level",label:"行政区划等级"}})],1)],1)},l=[],n=r("b775");function i(e){return Object(n["a"])({url:"/fastrest/area/list",method:"post",data:e})}var s={data:function(){return{loading:!0,showSearch:!0,restArea:{areaCode:"",areaName:"",level:"",hasChild:!0},areaTopData:[],queryParams:{areaCode:null,areaName:null}}},created:function(){this.getAreaList()},methods:{handleQuery:function(){this.areaTopData.length=0,this.getAreaList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},getAreaList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(a){for(var r=0;r<a.data.length;r++)e.restArea=a.data[r],e.areaTopData.push(e.restArea);e.loading=!1}))},load:function(e,a,r){var t=this;this.queryParams.areaCode=e.areaCode,i(this.queryParams).then((function(e){e.data.hasChild&&r(e.data.children),t.queryParams={areaCode:null,areaName:null}}))}}},o=s,u=r("2877"),d=Object(u["a"])(o,t,l,!1,null,null,null);a["default"]=d.exports}}]);