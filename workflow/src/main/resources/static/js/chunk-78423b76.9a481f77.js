(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-78423b76"],{cff3:function(e,t,s){"use strict";s.d(t,"d",(function(){return a})),s.d(t,"c",(function(){return i})),s.d(t,"a",(function(){return n})),s.d(t,"e",(function(){return l})),s.d(t,"b",(function(){return o}));var r=s("b775");function a(e){return Object(r["a"])({url:"/fastrest/dir/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/fastrest/dir/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/fastrest/dir",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/fastrest/dir",method:"put",data:e})}function o(e){return Object(r["a"])({url:"/fastrest/dir/"+e,method:"delete"})}},e7d5:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{attrs:{id:"doc"}},[s("div",{attrs:{id:"api-edit-details"}},[s("div",{staticClass:"form",attrs:{id:"api-edit-content"}},[e._m(0),s("div",{staticClass:"item"},[s("div",{staticClass:"col-sm-1 label"},[e._v("接口名称")]),s("div",{staticClass:"col-sm-11"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.restInfo.restName,expression:"restInfo.restName"}],staticClass:"uk-input",attrs:{type:"text",maxlength:"30",placeholder:"请输入接口名称"},domProps:{value:e.restInfo.restName},on:{input:function(t){t.target.composing||e.$set(e.restInfo,"restName",t.target.value)}}})])]),s("div",{staticClass:"item"},[s("div",{staticClass:"col-sm-1 label"},[e._v("接口编码")]),s("div",{staticClass:"col-sm-5"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.restInfo.restCode,expression:"restInfo.restCode"}],staticClass:"uk-input",attrs:{type:"text"},domProps:{value:e.restInfo.restCode},on:{input:function(t){t.target.composing||e.$set(e.restInfo,"restCode",t.target.value)}}})]),s("div",{staticClass:"col-sm-1 label"},[e._v("接口序列号")]),s("div",{staticClass:"col-sm-5"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.restInfo.restSerialNo,expression:"restInfo.restSerialNo"}],staticClass:"uk-input",attrs:{type:"text",readonly:""},domProps:{value:e.restInfo.restSerialNo},on:{input:function(t){t.target.composing||e.$set(e.restInfo,"restSerialNo",t.target.value)}}})])]),s("ul",{attrs:{"uk-tab":""}},[s("li",{on:{click:function(t){e.flag.demoShow="request"}}},[s("a",[e._v("请求参数(示例)")])]),s("li",{staticClass:"uk-active",on:{click:function(t){e.flag.demoShow="response"}}},[s("a",[e._v("响应结果(示例)")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"request"==e.flag.demoShow,expression:"flag.demoShow=='request'"}],staticClass:"item"},[s("editor",{attrs:{width:"100%",height:"300",lang:"json"},on:{init:e.editorInit,input:e.inputJson},model:{value:e.restInfo.requestArgs,callback:function(t){e.$set(e.restInfo,"requestArgs",t)},expression:"restInfo.requestArgs"}})],1),s("div",{directives:[{name:"show",rawName:"v-show",value:"response"==e.flag.demoShow,expression:"flag.demoShow=='response'"}],staticClass:"item"},[s("editor",{attrs:{width:"100%",height:"300",lang:"json"},on:{init:e.editorInit,input:e.inputJson},model:{value:e.restInfo.responseArgs,callback:function(t){e.$set(e.restInfo,"responseArgs",t)},expression:"restInfo.responseArgs"}})],1),e._m(1),s("div",{staticClass:"item"},[s("editor",{attrs:{width:"100%",height:"300",lang:"json"},on:{init:e.editorInit,input:e.inputJson},model:{value:e.restInfo.tpl.template,callback:function(t){e.$set(e.restInfo.tpl,"template",t)},expression:"restInfo.tpl.template"}})],1),e._m(2),s("div",{staticClass:"rest-sql"},[s("el-button",{staticClass:"add-btn",attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")]),e._l(e.restInfo.sqls,(function(t,r){return s("div",{key:r,staticClass:"rest-interface"},[s("div",{staticClass:"rest-sql-head-info"},[s("i",{ref:"arrowBtn",refInFor:!0,staticClass:"arrow-icon el-icon-caret-right",on:{click:function(t){return e.handleDisplay(r)}}}),s("el-form",{ref:"sqlForm",refInFor:!0,attrs:{model:t,size:"mini",disabled:void 0===e.parame[r]||e.parame[r],rules:e.rules}},[s("el-form-item",{staticClass:"var-name",attrs:{label:"变量名称：",prop:"varName"}},[s("el-tooltip",{attrs:{effect:"dark",content:"Top Center 提示文字",placement:"top-start"}},[s("i",{staticClass:"question-icon el-icon-question"})]),s("el-input",{on:{blur:e.judgeVarName},model:{value:t.varName,callback:function(s){e.$set(t,"varName",s)},expression:"item.varName"}})],1),s("el-form-item",{staticClass:"sn",attrs:{label:"排序号："}},[s("el-input",{model:{value:t.sn,callback:function(s){e.$set(t,"sn",s)},expression:"item.sn"}})],1),s("el-form-item",{staticClass:"var-type",attrs:{label:"变量类型：",prop:"varType"}},[s("el-select",{model:{value:e.varTypes[r],callback:function(t){e.$set(e.varTypes,r,t)},expression:"varTypes[index]"}},e._l(e.options,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{staticClass:"result-type",attrs:{label:"变量结果是否存入map：",prop:"resultType"}},[s("el-select",{model:{value:e.resultTypes[r],callback:function(t){e.$set(e.resultTypes,r,t)},expression:"resultTypes[index]"}},e._l(e.resultOptions,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),s("div",{staticClass:"edit-btn"},[s("button",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑 ")]),e._v(" | "),s("button",{on:{click:function(t){return e.handleMore(r)}}},[e._v(" 更多 "),s("span",{staticClass:"iconfont iconarrow_down"})]),s("div",{ref:"more",refInFor:!0,staticClass:"more-choose",on:{mouseleave:function(t){return e.closeMore(r)}}},[s("button",{on:{click:function(t){return e.handleCopy(r)}}},[e._v("复制")]),s("button",{on:{click:function(t){return e.handleDel(r)}}},[e._v("删除")])])])],1),s("div",{ref:"ruleType",refInFor:!0,staticClass:"rule-type"},[s("el-form",{ref:"item",refInFor:!0,attrs:{model:t,rules:e.rules,"label-width":"100px",size:"mini"}},[s("el-form-item",{attrs:{label:"规则配置：",prop:"sqlTpl"}},[s("el-input",{attrs:{type:"textarea"},model:{value:t.sqlTpl,callback:function(s){e.$set(t,"sqlTpl",s)},expression:"item.sqlTpl"}})],1),s("el-form-item",{staticClass:"remark",attrs:{label:"备注："}},[s("el-input",{attrs:{type:"textarea"},model:{value:t.remark,callback:function(s){e.$set(t,"remark",s)},expression:"item.remark"}})],1)],1)],1)])}))],2)]),s("div",{staticClass:"base-btn"},[s("el-row",[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",[e._v("取消")])],1)],1)])])},a=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("ul",{attrs:{"uk-tab":""}},[s("li",{staticClass:"uk-active"},[s("a",[e._v("基本信息")])])])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("ul",{attrs:{"uk-tab":""}},[s("li",{staticClass:"uk-active"},[s("a",[e._v("模板编辑")])])])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("ul",{attrs:{"uk-tab":""}},[s("li",{staticClass:"uk-active"},[s("a",[e._v("变量配置")])])])}],i=s("53ca"),n=(s("4de4"),s("d3b7"),s("159b"),s("b64b"),s("a434"),s("b75b")),l=s("cff3"),o={name:"DeployShow",components:{editor:s("7c9e")},props:["restInfoVO","restInfo","varTypeNum","resultTypeNum"],data:function(){return{flag:{demoShow:"response"},parame:{},resultTypes:[],rules:{restName:{required:!0,message:"请输入接口名称",trigger:"blur"},restCode:{required:!0,message:"请输入接口编码",trigger:"blur"},dirCode:{required:!0,message:"请选择所属目录",trigger:"blur"},method:{required:!0,message:"请输入接口方法",trigger:"blur"},request:{required:!0,message:"请输入请求参数样例",trigger:"blur"},response:{required:!0,message:"请输入响应结果样例",trigger:"blur"},system:{required:!0,message:"请输入归属系统",trigger:"blur"},template:{required:!0,message:"请输入响应结果JSON模版",trigger:"blur"},sqlTpl:{required:!0,message:"请输入规则配置",trigger:"blur"},varName:{required:!0,message:"请输入变量名称",trigger:"blur"},varType:{required:!0,message:"请选择变量类型",trigger:"blur"},resultType:{required:!0,message:"请确定变量结果是否存入map",trigger:"blur"}},options:[{value:1,label:"单个值(str),如:34"},{value:2,label:"单数组(str),如:[1,2,3]"},{value:3,label:"数组集合(str),如:[[1,3,5],[2,4,6]]"},{value:4,label:'单个对象(obj),如:{"name":"中国","code":"ai"}'},{value:5,label:"多个对象(obj)，如:[{},{}]"},{value:6,label:'单列转数组(str),如["34","56"]'},{value:7,label:'键值对象(obj),如{"userName":"中国","code":"ai"}'},{value:8,label:"单个对象(str),如:\"{'name':'中国','code':'ai'}\""},{value:9,label:'多个对象(str)，如:"[{},{}]"'}],resultOptions:[{value:0,label:"否"},{value:1,label:"是"}],varTypes:[]}},watch:{varTypeNum:function(){var e=this;this.varTypeNum&&this.varTypeNum.filter((function(t){e.judgeVarType(t)}))},resultTypeNum:function(){var e=this;this.resultTypeNum&&this.resultTypeNum.filter((function(t){e.judgeResultType(t)}))}},methods:{editorInit:function(){s("2099"),s("1d29"),s("818b"),s("8a2a"),s("be9d"),s("0696"),s("bb36"),s("061c"),s("95b8"),s("b039"),s("e1a9")},inputJson:function(){},getDirList:function(){var e=this;Object(l["d"])().then((function(t){if(200==t.code)for(var s=0;s<t.data.length;s++)e.dir=t.data[s],e.dirList.push(e.dir)}))},onSubmit:function(){var e=this,t=this.deepCopy(this.restInfo);this.varTypes.filter((function(e,s){"number"===typeof e&&(t.sqls[s].varType=e)})),this.resultTypes.filter((function(e,s){"number"===typeof e&&(t.sqls[s].resultType=e)})),console.log("restINFO==>",t),Object(n["j"])(t).then((function(t){200===t.code?(e.$message({message:"更新成功",type:"success"}),Object.keys(e.parame).forEach((function(t){e.parame[t]=!0}))):e.$message.error("更新失败")}))},handleDisplay:function(e){"rule-type"===this.$refs.ruleType[e].className?(this.$refs.ruleType[e].style.display="block",this.$refs.arrowBtn[e].style.transform="rotate(90deg)",this.$refs.ruleType[e].className="rule-type display-type"):(this.$refs.ruleType[e].style.display="none",this.$refs.arrowBtn[e].style.transform="rotate(0deg)",this.$refs.ruleType[e].className="rule-type")},handleEdit:function(e){this.parame[e]?this.parame[e]=!this.parame[e]:this.$set(this.parame,e,!1)},handleMore:function(e){this.$refs.more.filter((function(e){e.className="more-choose",e.style.display="none"})),"more-choose"===this.$refs.more[e].className?(this.$refs.more[e].style.display="block",this.$refs.more[e].className="more-choose display-type"):(this.$refs.more[e].style.display="none",this.$refs.more[e].className="more-choose")},handleAdd:function(){var e={id:null,varName:"",sn:null,varType:null,remark:"",sqlTpl:""};this.restInfo.sqls.push(e),this.handleEdit(this.restInfo.sqls.length-1)},handleDel:function(e){var t=this;this.$confirm('是否确认删除变量名为"'+this.restInfo.sqls[e].varName+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,type:"warning"}).then((function(){t.restInfo.sqls[e].id?Object(n["f"])(t.restInfo.sqls[e].id).then((function(s){200===s.code?(t.$message({type:"success",message:"删除成功"}),t.restInfo.sqls.splice(e,1)):t.$message.error("删除失败")})):t.restInfo.sqls.splice(e,1)})).catch((function(){})),this.$refs.more[e].style.display="none"},handleCopy:function(e){var t=this.deepCopy(this.restInfo.sqls[e]);t.id=null,this.judgeVarType(t.varType),this.judgeResultType(t.resultType),this.restInfo.sqls.push(t),this.handleEdit(this.restInfo.sqls.length-1),this.$refs.more[e].style.display="none"},deepCopy:function(e){var t=Array.isArray(e)?[]:{};if(e&&"object"===Object(i["a"])(e))for(var s in e)e[s]&&"object"===Object(i["a"])(e[s])?t[s]=this.deepCopy(e[s]):t[s]=e[s];return t},judgeVarType:function(e){1===e?this.varTypes.push("单个值(str),如:34"):2===e?this.varTypes.push("单数组(str),如:[1,2,3]"):3===e?this.varTypes.push("数组集合(str),如:[[1,3,5],[2,4,6]]"):4===e?this.varTypes.push('单个对象(obj),如:{"name":"中国","code":"ai"}'):5===e?this.varTypes.push("多个对象(obj)，如:[{},{}]"):6===e?this.varTypes.push('单列转数组(str),如["34","56"]'):7===e?this.varTypes.push('键值对象(obj),如{"userName":"中国","code":"ai"}'):8===e?this.varTypes.push("单个对象(str),如:\"{'name':'中国','code':'ai'}\""):9===e&&this.varTypes.push('多个对象(str)，如:"[{},{}]"')},judgeResultType:function(e){0===e?this.resultTypes.push("否"):1===e&&this.resultTypes.push("是")},judgeVarName:function(){var e=this,t=[];this.restInfo.sqls.filter((function(e){t.push(e.varName)})),t.filter((function(s){t.indexOf(s)!=t.lastIndexOf(s)&&e.$confirm("数据项中有重复变量名称，请删除或修改重复变量名称的数据项","警告",{confirmButtonText:"确定",closeOnClickModal:!1,type:"warning"}).then((function(){})).catch((function(){}))}))},closeMore:function(e){this.$refs.more[e].style.display="none"},handleSqls:function(){}}},u=o,c=s("2877"),p=Object(c["a"])(u,r,a,!1,null,null,null);t["default"]=p.exports}}]);