<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=/favicon.ico><link rel=stylesheet href=https://at.alicdn.com/t/font_1977519_2y4y7jh9s3t.css><title>SRD-BD 基础开发平台</title><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #FFF;
      opacity: 0.5;
    }</style><script>window.ctx='/';
      window.x={v:'1610090128811',ctx:'/',cdn:'/'};</script><link href=/css/chunk-libs.3e01de99.css rel=stylesheet><link href=/css/app.5208e58b.css rel=stylesheet></head><body><div id=app><div id=loader-wrapper><div id=loader></div><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>欢迎使用基础开发平台,资源加载中，请稍后... ...</div></div></div><script>(function(e){function c(c){for(var t,r,a=c[0],o=c[1],f=c[2],h=0,i=[];h<a.length;h++)r=a[h],Object.prototype.hasOwnProperty.call(u,r)&&u[r]&&i.push(u[r][0]),u[r]=0;for(t in o)Object.prototype.hasOwnProperty.call(o,t)&&(e[t]=o[t]);l&&l(c);while(i.length)i.shift()();return d.push.apply(d,f||[]),n()}function n(){for(var e,c=0;c<d.length;c++){for(var n=d[c],t=!0,r=1;r<n.length;r++){var a=n[r];0!==u[a]&&(t=!1)}t&&(d.splice(c--,1),e=o(o.s=n[0]))}return e}var t={},r={runtime:0},u={runtime:0},d=[];function a(e){return o.p+"js/"+({}[e]||e)+"."+{"chunk-095b22ac":"814668d0","chunk-0aa6a3ea":"04c5ccee","chunk-2f8c138d":"f0074d48","chunk-e9af1076":"073ceaaf","chunk-2d0b2b28":"9ffc10ea","chunk-a77ddd8e":"d24b9629","chunk-2d0a2db2":"2dd51c7c","chunk-171ca186":"37df8e70","chunk-2d0c5584":"bdfed08a","chunk-2d0c8d3c":"bfd31884","chunk-2d0e2366":"68f2ed78","chunk-67cefc8c":"f3814478","chunk-2d0f012d":"1bde8e38","chunk-3f93175c":"fc2c04f6","chunk-375a043e":"e55ddc7d","chunk-5c200c6c":"4e06c570","chunk-a7e597e8":"8e693b1b","chunk-c28ad000":"cf835e85","chunk-d19c1a98":"c361b3b2","chunk-78423b76":"9a481f77","chunk-1dff9df3":"f739de8a","chunk-6edc3c8b":"ee466103"}[e]+".js"}function o(c){if(t[c])return t[c].exports;var n=t[c]={i:c,l:!1,exports:{}};return e[c].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.e=function(e){var c=[],n={"chunk-2f8c138d":1,"chunk-a77ddd8e":1,"chunk-171ca186":1,"chunk-67cefc8c":1,"chunk-375a043e":1,"chunk-5c200c6c":1,"chunk-c28ad000":1,"chunk-6edc3c8b":1};r[e]?c.push(r[e]):0!==r[e]&&n[e]&&c.push(r[e]=new Promise((function(c,n){for(var t="css/"+({}[e]||e)+"."+{"chunk-095b22ac":"31d6cfe0","chunk-0aa6a3ea":"31d6cfe0","chunk-2f8c138d":"84f98409","chunk-e9af1076":"31d6cfe0","chunk-2d0b2b28":"31d6cfe0","chunk-a77ddd8e":"91ab3d01","chunk-2d0a2db2":"31d6cfe0","chunk-171ca186":"0d3546eb","chunk-2d0c5584":"31d6cfe0","chunk-2d0c8d3c":"31d6cfe0","chunk-2d0e2366":"31d6cfe0","chunk-67cefc8c":"88890a62","chunk-2d0f012d":"31d6cfe0","chunk-3f93175c":"31d6cfe0","chunk-375a043e":"34f57c0d","chunk-5c200c6c":"7b6cbdf1","chunk-a7e597e8":"31d6cfe0","chunk-c28ad000":"b5bdb2c6","chunk-d19c1a98":"31d6cfe0","chunk-78423b76":"31d6cfe0","chunk-1dff9df3":"31d6cfe0","chunk-6edc3c8b":"f2a1e0da"}[e]+".css",u=o.p+t,d=document.getElementsByTagName("link"),a=0;a<d.length;a++){var f=d[a],h=f.getAttribute("data-href")||f.getAttribute("href");if("stylesheet"===f.rel&&(h===t||h===u))return c()}var i=document.getElementsByTagName("style");for(a=0;a<i.length;a++){f=i[a],h=f.getAttribute("data-href");if(h===t||h===u)return c()}var l=document.createElement("link");l.rel="stylesheet",l.type="text/css",l.onload=c,l.onerror=function(c){var t=c&&c.target&&c.target.src||u,d=new Error("Loading CSS chunk "+e+" failed.\n("+t+")");d.code="CSS_CHUNK_LOAD_FAILED",d.request=t,delete r[e],l.parentNode.removeChild(l),n(d)},l.href=u;var s=document.getElementsByTagName("head")[0];s.appendChild(l)})).then((function(){r[e]=0})));var t=u[e];if(0!==t)if(t)c.push(t[2]);else{var d=new Promise((function(c,n){t=u[e]=[c,n]}));c.push(t[2]=d);var f,h=document.createElement("script");h.charset="utf-8",h.timeout=120,o.nc&&h.setAttribute("nonce",o.nc),h.src=a(e);var i=new Error;f=function(c){h.onerror=h.onload=null,clearTimeout(l);var n=u[e];if(0!==n){if(n){var t=c&&("load"===c.type?"missing":c.type),r=c&&c.target&&c.target.src;i.message="Loading chunk "+e+" failed.\n("+t+": "+r+")",i.name="ChunkLoadError",i.type=t,i.request=r,n[1](i)}u[e]=void 0}};var l=setTimeout((function(){f({type:"timeout",target:h})}),12e4);h.onerror=h.onload=f,document.head.appendChild(h)}return Promise.all(c)},o.m=e,o.c=t,o.d=function(e,c,n){o.o(e,c)||Object.defineProperty(e,c,{enumerable:!0,get:n})},o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,c){if(1&c&&(e=o(e)),8&c)return e;if(4&c&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&c&&"string"!=typeof e)for(var t in e)o.d(n,t,function(c){return e[c]}.bind(null,t));return n},o.n=function(e){var c=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(c,"a",c),c},o.o=function(e,c){return Object.prototype.hasOwnProperty.call(e,c)},o.p="/",o.oe=function(e){throw console.error(e),e};var f=window["webpackJsonp"]=window["webpackJsonp"]||[],h=f.push.bind(f);f.push=c,f=f.slice();for(var i=0;i<f.length;i++)c(f[i]);var l=h;n()})([]);</script><script src=/js/chunk-elementUI.cbeb46c9.js></script><script src=/js/chunk-libs.42644947.js></script><script src=/js/app.5e047eb6.js></script></body></html>