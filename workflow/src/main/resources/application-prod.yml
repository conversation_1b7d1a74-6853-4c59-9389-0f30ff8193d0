external:
    css:
        url: 10.250.49.251:2357
    token:
        url: 10.250.49.251:2357

# CORS配置
cors:
    allowed-origins: http://nx-portal.asiainfo.work,https://nx-portal.asiainfo.work

oa:
    id: 25
    name: 不需要
    pwd: 0B9D4C530337EB276F3684FBBBB5AA9F
    # 将代办推送到oa - 负载均衡
    pushUrl: http://10.254.123.166:9080
    # 将代办推送到oa - 测试
    #pushUrl: http://10.254.123.162:9080
    sso-type: oa
    url-for-task: /login?userName=jindongxun&redirect=/order/00
    group:
        redirect-url: http://10.250.162.240:8090/demo?userName=%s&redirect=workflow
        client-id: uni_1740_nx_nxdy
        app-id: 20240
        #测试
        token-url: http://10.255.82.77:8080/bap/gw/aopoauth/oauth/token
        auth-url: http://10.255.82.77:8080/bap/gw/rest/sso/common/ticket/auth/checkticket
        app-key: 88e5ddeda0d5a4ab639544507ae98330
        client-secret: IvM75ERREIbOunDv5WGnoMr2RVgUJbH5T0QROg6QvgE=
        #正式
        #token-url: http://10.255.82.78:8080/bap/gw/aopoauth/oauth/token
        #auth-url: http://10.255.82.78:8080/bap/gw/rest/sso/common/ticket/auth/checkticket
        #app-key: 65d71309c67aebe80ec2c8f4d8560419
        #client-secret: TXL3WgMVpONw27OZ2RplSpUi4Q2WQYWIC1GehseRaBc=
moa:
    #测试
    #auth-url: http://10.254.123.75:8080/moa/MOA_NX_CMCC/api/account/authentication,http://10.254.123.75:8080/moa/MOA_NX_CMCC/api/account/authentication
    #auth-url2: http://10.254.123.207:28899/MOA_NX_CMCC/checkingToken
    #正式
    auth-url: http://10.254.123.75:8085/moa/MOA_NX_CMCC/api/account/authentication,http://10.254.123.75:8086/moa/MOA_NX_CMCC/api/account/authentication
    auth-url2: http://10.254.123.209:28899/moa/MOA_NX_CMCC/checkingToken,http://10.254.123.211:28899/moa/MOA_NX_CMCC/checkingToken
    group:
        app-id: sndypt
        #测试
        #auth-url: http://10.254.123.207:8080/authenticator/verify
        #正式
        auth-url: http://10.254.123.234:20000/group_singural/authenticator/verify
grid:
    #auth-url: http://10.254.123.13:19010/fuseapp_web/finedo/login4a/gridverify?token=
    auth-url: http://10.254.123.13:9010/fuseapp_web/finedo/login4a/gridverify?token=
sound:
    messageSendFlag: 1
    env:
        flag: 1
    identy: # 工作流附件
        input: ./data/ouput
        output: ./data/input
        model: ./data/model
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: ************************************,10.102.42.86:17700,10.102.42.88:17700/dypt?targetServerType=master&currentSchema=workflow_nx&useUnicode=true&characterEncoding=utf-8
                username: nx_dayin
                password: 61#U5rYg!s
            slave:
                # 从数据源开关/默认关闭
                enabled: true
                url: ************************************,10.102.42.86:17700,10.102.42.88:17700/dypt?targetServerType=master&currentSchema=uni_portal_nx&useUnicode=true&characterEncoding=utf-8
                username: nx_dayin
                password: 61#U5rYg!s
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
    jpa:
        database-platform: org.hibernate.dialect.PostgreSQLDialect
    redis:
        database: 1
        host: **************
        password: nxdy@123
        port: 16379
        timeout: 10000
    session:
        store-type: redis
    main:
        allow-bean-definition-overriding: true
static:
    file:
        dir: ./upload/
use_portal_token_verify: false # 是否使用门户单点登录



sftp:
  ip: ************
  port: 2302
  user: sftpcsv9511
  passwd: SFtpcsv_951!%
  outpath: /outgoing/csvc/
  inpath: /incoming/csvc/



# Swagger配置
swagger:
    # 是否开启swagger
    enabled: true
    # 请求前缀
    pathMapping: swaggerdoc/
