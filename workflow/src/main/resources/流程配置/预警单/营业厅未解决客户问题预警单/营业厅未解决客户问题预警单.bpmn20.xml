<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="warning_13" name="营业厅未解决客户问题预警单" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="warning_13" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-EA529804-5165-482B-B9C7-09799CC33D82" name="工单派发" flowable:assignee="${userId}" flowable:formKey="warning_13_sendform" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.MesgSaveEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-3629318B-A360-4ED2-A448-DE84519FCA5B"></exclusiveGateway>
    <serviceTask id="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" name="生成附件" flowable:class="com.asiainfo.sound.service.flow.IdentiferAttachList"></serviceTask>
    <receiveTask id="warning_13_restartFlag" name="集团操作"></receiveTask>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <exclusiveGateway id="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></exclusiveGateway>
    <exclusiveGateway id="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6"></exclusiveGateway>
    <exclusiveGateway id="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></exclusiveGateway>
    <userTask id="sid-940DECE4-AF31-4639-AEE7-406E766611B6" name="工单处理" flowable:assignee="${firstUser}" flowable:formKey="warning_13_step1" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${firstUsers}" flowable:elementVariable="firstUser">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <serviceTask id="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <sequenceFlow id="sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" sourceRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985"></sequenceFlow>
    <receiveTask id="warning_13_replyFlag" name="回复集团"></receiveTask>
    <exclusiveGateway id="sid-19F36496-15BF-468A-8D21-51ECE0C31779"></exclusiveGateway>
    <userTask id="sid-F5C4DC79-2A6E-48F3-B608-1A17E84C74AD" name="三级领导审批" flowable:assignee="${examUser}" flowable:formKey="warning_13_step2" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878"></exclusiveGateway>
    <sequenceFlow id="sid-FFF3577E-6682-4156-8315-CEB27E257C14" sourceRef="sid-F5C4DC79-2A6E-48F3-B608-1A17E84C74AD" targetRef="sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878"></sequenceFlow>
    <sequenceFlow id="sid-93F305CA-F825-4BE5-A460-6143D917C69B" sourceRef="warning_13_replyFlag" targetRef="sid-19F36496-15BF-468A-8D21-51ECE0C31779"></sequenceFlow>
    <sequenceFlow id="sid-004C6B10-F13A-4AE7-AB2C-179263730EF4" sourceRef="sid-19F36496-15BF-468A-8D21-51ECE0C31779" targetRef="warning_13_restartFlag">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-97ACB2CB-1D82-40AF-A88B-95F72F987761" sourceRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" targetRef="warning_13_replyFlag"></sequenceFlow>
    <sequenceFlow id="sid-4EF569AD-BB1E-4B5C-B1B8-38DDAC8EA267" sourceRef="sid-EA529804-5165-482B-B9C7-09799CC33D82" targetRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6"></sequenceFlow>
    <userTask id="sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C" name="省侧接口人工单审批" flowable:assignee="${actUserId}" flowable:formKey="warning_13_endform" flowable:formFieldValidation="true">
      <documentation>省侧接口人工单审批(集团)</documentation>
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-AAEED4B1-9A35-418C-9A22-F86C31301086" sourceRef="sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C" targetRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></sequenceFlow>
    <sequenceFlow id="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-9409C096-2669-4888-8A14-6690A376E236" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-8CC5E697-7302-4336-84BB-BCC37CAFFDC1" sourceRef="startEvent1" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82"></sequenceFlow>
    <sequenceFlow id="sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" sourceRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6" targetRef="sid-F5C4DC79-2A6E-48F3-B608-1A17E84C74AD"></sequenceFlow>
    <sequenceFlow id="sid-AB16FA5E-F9BA-47D1-831E-1D9B4202519D" sourceRef="sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-84875890-3436-45D8-9A47-A7A28715AE88" sourceRef="sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878" targetRef="sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B0A6C947-1DE9-4863-B149-CE666D845BB9" sourceRef="sid-19F36496-15BF-468A-8D21-51ECE0C31779" targetRef="sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-0F06257A-767B-41D9-A965-556E3A1493E7" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-6C48F8E5-4B6A-4A74-A036-AFD6F1091EE1" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==1&&passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-ABF5D885-39BE-48C6-BDBF-9F297FE6B8D5" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==0&&passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-FDCF767B-E5FA-4391-9010-399C4298203B" sourceRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" sourceRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" targetRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" sourceRef="warning_13_restartFlag" targetRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_warning_13">
    <bpmndi:BPMNPlane bpmnElement="warning_13" id="BPMNPlane_warning_13">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="60.0" y="148.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EA529804-5165-482B-B9C7-09799CC33D82" id="BPMNShape_sid-EA529804-5165-482B-B9C7-09799CC33D82">
        <omgdc:Bounds height="80.0" width="100.0" x="195.0" y="123.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" id="BPMNShape_sid-3629318B-A360-4ED2-A448-DE84519FCA5B">
        <omgdc:Bounds height="40.0" width="40.0" x="1050.0" y="332.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" id="BPMNShape_sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
        <omgdc:Bounds height="80.0" width="100.0" x="915.0000116229065" y="387.5000041560696"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_13_restartFlag" id="BPMNShape_warning_13_restartFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="375.0" y="387.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="28.0" x="405.0" y="493.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" id="BPMNShape_sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE">
        <omgdc:Bounds height="40.0" width="40.0" x="225.0" y="407.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" id="BPMNShape_sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6">
        <omgdc:Bounds height="40.0" width="40.0" x="330.0" y="143.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" id="BPMNShape_sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533">
        <omgdc:Bounds height="40.0" width="40.0" x="1050.0" y="140.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-940DECE4-AF31-4639-AEE7-406E766611B6" id="BPMNShape_sid-940DECE4-AF31-4639-AEE7-406E766611B6">
        <omgdc:Bounds height="80.0" width="100.0" x="450.0" y="122.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" id="BPMNShape_sid-01B628AE-2481-4CE6-92CE-6A28379DD98D">
        <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="467.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_13_replyFlag" id="BPMNShape_warning_13_replyFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="615.0" y="387.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-19F36496-15BF-468A-8D21-51ECE0C31779" id="BPMNShape_sid-19F36496-15BF-468A-8D21-51ECE0C31779">
        <omgdc:Bounds height="40.0" width="40.0" x="543.8" y="407.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F5C4DC79-2A6E-48F3-B608-1A17E84C74AD" id="BPMNShape_sid-F5C4DC79-2A6E-48F3-B608-1A17E84C74AD">
        <omgdc:Bounds height="80.0" width="100.0" x="690.0" y="123.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878" id="BPMNShape_sid-DEC1AE30-04F6-4338-979F-19BEC9AD4878">
        <omgdc:Bounds height="40.0" width="40.0" x="840.0" y="143.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C" id="BPMNShape_sid-ED5B6BA3-5F84-4B25-B9BF-2989F5A09B2C">
        <omgdc:Bounds height="80.0" width="100.0" x="930.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-84875890-3436-45D8-9A47-A7A28715AE88" id="BPMNEdge_sid-84875890-3436-45D8-9A47-A7A28715AE88" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.5" flowable:targetDockerY="42.5">
        <omgdi:waypoint x="879.867561983471" y="162.9173553719008"></omgdi:waypoint>
        <omgdi:waypoint x="929.9999999999981" y="162.70933609958507"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AAEED4B1-9A35-418C-9A22-F86C31301086" id="BPMNEdge_sid-AAEED4B1-9A35-418C-9A22-F86C31301086" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="1029.9499999999873" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="1050.0" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-ABF5D885-39BE-48C6-BDBF-9F297FE6B8D5" id="BPMNEdge_sid-ABF5D885-39BE-48C6-BDBF-9F297FE6B8D5" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="350.0" y="182.94666499498496"></omgdi:waypoint>
        <omgdi:waypoint x="350.0" y="462.0"></omgdi:waypoint>
        <omgdi:waypoint x="407.30456271404313" y="499.79207823967164"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" id="BPMNEdge_sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="245.0" y="407.5"></omgdi:waypoint>
        <omgdi:waypoint x="245.0" y="202.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4EF569AD-BB1E-4B5C-B1B8-38DDAC8EA267" id="BPMNEdge_sid-4EF569AD-BB1E-4B5C-B1B8-38DDAC8EA267" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="294.94999999997106" y="163.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.0" y="163.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0F06257A-767B-41D9-A965-556E3A1493E7" id="BPMNEdge_sid-0F06257A-767B-41D9-A965-556E3A1493E7" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="1070.0" y="179.94482087227416"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="332.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-93F305CA-F825-4BE5-A460-6143D917C69B" id="BPMNEdge_sid-93F305CA-F825-4BE5-A460-6143D917C69B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="615.0" y="427.5"></omgdi:waypoint>
        <omgdi:waypoint x="583.7098170128585" y="427.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" id="BPMNEdge_sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1070.0" y="140.0"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="96.0"></omgdi:waypoint>
        <omgdi:waypoint x="500.0" y="96.0"></omgdi:waypoint>
        <omgdi:waypoint x="500.0" y="122.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" id="BPMNEdge_sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="549.9499999999828" y="162.6040625"></omgdi:waypoint>
        <omgdi:waypoint x="689.9999999999909" y="162.89583333333331"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" id="BPMNEdge_sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1070.0" y="372.4435478654593"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="819.9499999999958" y="507.41666666666663"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6C48F8E5-4B6A-4A74-A036-AFD6F1091EE1" id="BPMNEdge_sid-6C48F8E5-4B6A-4A74-A036-AFD6F1091EE1" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="353.6357476635514" y="179.31129145259226"></omgdi:waypoint>
        <omgdi:waypoint x="389.0" y="338.0"></omgdi:waypoint>
        <omgdi:waypoint x="965.0" y="338.0"></omgdi:waypoint>
        <omgdi:waypoint x="965.0000064283116" y="387.5000041560696"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" id="BPMNEdge_sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="245.0" y="447.43746859296476"></omgdi:waypoint>
        <omgdi:waypoint x="245.0" y="507.0"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="507.4597702776267"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FDCF767B-E5FA-4391-9010-399C4298203B" id="BPMNEdge_sid-FDCF767B-E5FA-4391-9010-399C4298203B" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1070.0" y="372.4366286863271"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="427.0"></omgdi:waypoint>
        <omgdi:waypoint x="1014.9500116229041" y="427.26190691253754"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" id="BPMNEdge_sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="719.9999999998702" y="507.5"></omgdi:waypoint>
        <omgdi:waypoint x="432.9499186986084" y="507.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AB16FA5E-F9BA-47D1-831E-1D9B4202519D" id="BPMNEdge_sid-AB16FA5E-F9BA-47D1-831E-1D9B4202519D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="860.0" y="143.0"></omgdi:waypoint>
        <omgdi:waypoint x="860.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="601.5" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="549.95" y="136.6379310344828"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-97ACB2CB-1D82-40AF-A88B-95F72F987761" id="BPMNEdge_sid-97ACB2CB-1D82-40AF-A88B-95F72F987761" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="915.0000055825935" y="427.5000034633913"></omgdi:waypoint>
        <omgdi:waypoint x="714.94999999961" y="427.5000006919856"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FFF3577E-6682-4156-8315-CEB27E257C14" id="BPMNEdge_sid-FFF3577E-6682-4156-8315-CEB27E257C14" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="789.9499999999999" y="163.0"></omgdi:waypoint>
        <omgdi:waypoint x="840.0" y="163.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-004C6B10-F13A-4AE7-AB2C-179263730EF4" id="BPMNEdge_sid-004C6B10-F13A-4AE7-AB2C-179263730EF4" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="543.8" y="427.5"></omgdi:waypoint>
        <omgdi:waypoint x="474.95000000000005" y="427.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9409C096-2669-4888-8A14-6690A376E236" id="BPMNEdge_sid-9409C096-2669-4888-8A14-6690A376E236" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="369.88372093022974" y="162.93355481727573"></omgdi:waypoint>
        <omgdi:waypoint x="449.99999999999955" y="162.66649999999998"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8CC5E697-7302-4336-84BB-BCC37CAFFDC1" id="BPMNEdge_sid-8CC5E697-7302-4336-84BB-BCC37CAFFDC1" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="89.94999936756076" y="163.0"></omgdi:waypoint>
        <omgdi:waypoint x="195.0" y="163.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" id="BPMNEdge_sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="375.0" y="427.5"></omgdi:waypoint>
        <omgdi:waypoint x="264.9055169536781" y="427.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B0A6C947-1DE9-4863-B149-CE666D845BB9" id="BPMNEdge_sid-B0A6C947-1DE9-4863-B149-CE666D845BB9" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="563.67529228371" y="407.6170046801872"></omgdi:waypoint>
        <omgdi:waypoint x="563.0" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="980.0" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="980.0" y="199.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>