{"key": "warning_13", "name": "整改工作投诉预警单", "fields": [{"fieldType": "OptionFormField", "id": "identyDetail", "name": "工单细类", "type": "select", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "00001301", "name": "营业厅推诿率"}, {"id": "00001302", "name": "营业厅典型性问题推诿率"}], "params": {"showType": "2", "order": 1}, "optionsExpression": null}, {"fieldType": "FormField", "id": "title", "name": "工单标题", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "content", "name": "工单内容", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"fieldType": "OptionFormField", "id": "userNameList", "name": "选择处理人", "type": "workflowHandlerList", "required": true, "placeholder": "empty", "params": {"showType": "1", "userRoleId": "warning_13_accept"}}, {"fieldType": "FormField", "id": "processTime", "name": "要求处理时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "OptionFormField", "id": "isLeaderApproval", "name": "是否需要三级领导审批", "type": "select", "value": "", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "0", "name": "需要"}, {"id": "1", "name": "不需要"}], "optionsExpression": null, "params": {"showType": "2", "order": 13}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 11}}, {"fieldType": "OptionFormField", "id": "noticeSend", "name": "通知", "type": "select", "value": "11", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "11", "name": "短信"}, {"id": "00", "name": "不通知"}], "optionsExpression": null, "params": {"showType": "1"}}, {"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 2}}, {"fieldType": "FormField", "id": "originUnit", "name": "工单发起方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 5}}, {"fieldType": "FormField", "id": "receiver<PERSON><PERSON><PERSON>", "name": "工单接收方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 6}}, {"fieldType": "FormField", "id": "creatTime", "name": "创建时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 7}}, {"fieldType": "FormField", "id": "creator", "name": "创建人", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 8}}, {"fieldType": "FormField", "id": "creatorContactInfo", "name": "创建人联系方式", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 9}}]}