{"key": "warning_17", "name": "用后即评预警单", "fields": [{"fieldType": "OptionFormField", "id": "identyDetail", "name": "工单细类", "type": "select", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "00001701", "name": "低分不满预警单"}, {"id": "00001702", "name": "重复不满预警单"}], "params": {"showType": "2", "order": 1, "syncFlag": "var"}, "optionsExpression": null}, {"fieldType": "FormField", "id": "title", "name": "工单标题", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "content", "name": "工单内容", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"fieldType": "OptionFormField", "id": "userNameList", "name": "选择多个处理人", "type": "workflowHandlerList", "required": true, "placeholder": "empty", "params": {"showType": "1", "userRoleId": "warning_17_accept"}}, {"fieldType": "FormField", "id": "processTime", "name": "要求处理时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "FormField", "id": "productType", "name": "产品类型", "type": "codeSelect", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "selectId": "productTypeYHJP", "order": 11}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 12}}, {"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 2}}, {"fieldType": "FormField", "id": "originUnit", "name": "工单发起方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 5}}, {"fieldType": "FormField", "id": "receiver<PERSON><PERSON><PERSON>", "name": "工单接收方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 6}}, {"fieldType": "FormField", "id": "creatTime", "name": "创建时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 9}}, {"fieldType": "FormField", "id": "creator", "name": "创建人", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 10}}, {"fieldType": "FormField", "id": "creatorContactInfo", "name": "创建人联系方式", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 11}}, {"fieldType": "OptionFormField", "id": "is<PERSON><PERSON>le", "name": "是否需要处理", "type": "select", "value": "", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "0", "name": "是"}, {"id": "1", "name": "否"}], "optionsExpression": null, "params": {"showType": "1"}}, {"fieldType": "FormField", "id": "saticefactiom", "name": "满意度", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 13}}, {"fieldType": "FormField", "id": "relative", "name": "满意度环比", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 14}}, {"fieldType": "OptionFormField", "id": "extEarlyWarningLevel", "name": "预警级别", "type": "select", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "01", "name": "红色"}, {"id": "02", "name": "橙色"}, {"id": "03", "name": "黄色"}, {"id": "04", "name": "无"}], "params": {"showType": "0", "order": 15}, "optionsExpression": null}]}