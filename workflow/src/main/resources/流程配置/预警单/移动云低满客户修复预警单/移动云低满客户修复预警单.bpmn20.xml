<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="warning_23" name="移动云低满客户修复预警单" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="warning_18" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-EA529804-5165-482B-B9C7-09799CC33D82" name="工单派发" flowable:assignee="${userId}" flowable:formKey="warning_18_sendform" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.MesgSaveEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <userTask id="sid-5654C886-6881-4A5C-84FF-001D402A3013" name="省侧接口人审批" flowable:assignee="${actUserId}" flowable:formKey="warning_23_endform" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <serviceTask id="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" name="生成附件" flowable:class="com.asiainfo.sound.service.flow.IdentiferAttachList"></serviceTask>
    <receiveTask id="warning_23_restartFlag" name="集团操作"></receiveTask>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <sequenceFlow id="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" sourceRef="startEvent1" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82"></sequenceFlow>
    <exclusiveGateway id="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></exclusiveGateway>
    <sequenceFlow id="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" sourceRef="warning_23_restartFlag" targetRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></sequenceFlow>
    <sequenceFlow id="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></exclusiveGateway>
    <sequenceFlow id="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" sourceRef="sid-5654C886-6881-4A5C-84FF-001D402A3013" targetRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></sequenceFlow>
    <userTask id="sid-940DECE4-AF31-4639-AEE7-406E766611B6" name="工单处理" flowable:assignee="${firstUser}" flowable:formKey="warning_23_step1" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${firstUsers}" flowable:elementVariable="firstUser">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <userTask id="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" name="三级领导审批" flowable:assignee="${taskUser}" flowable:formKey="warning_23_step2" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB"></exclusiveGateway>
    <sequenceFlow id="sid-7785EE0F-A08E-4D1D-A614-5CD9C961D12A" sourceRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" targetRef="sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB"></sequenceFlow>
    <sequenceFlow id="sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" sourceRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6" targetRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD"></sequenceFlow>
    <sequenceFlow id="sid-0F1525FE-6AB9-4F23-B7A0-6179558311B7" sourceRef="sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB" targetRef="sid-5654C886-6881-4A5C-84FF-001D402A3013">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <receiveTask id="warning_23_replyFlag" name="回复集团"></receiveTask>
    <exclusiveGateway id="sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80"></exclusiveGateway>
    <sequenceFlow id="sid-E58F3F89-C4D6-474A-89C4-1A6E41B4A3C5" sourceRef="warning_23_replyFlag" targetRef="sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80"></sequenceFlow>
    <sequenceFlow id="sid-70917429-160A-445A-8F66-91F5271D80D8" sourceRef="sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80" targetRef="warning_23_restartFlag">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-4AC18B3E-B042-4B56-90E1-AB688D83F839" sourceRef="sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80" targetRef="sid-5654C886-6881-4A5C-84FF-001D402A3013">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-78B3CDF6-4817-4FD5-936D-DC59B06464AF" sourceRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" targetRef="warning_23_replyFlag"></sequenceFlow>
    <sequenceFlow id="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" sourceRef="sid-EA529804-5165-482B-B9C7-09799CC33D82" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6"></sequenceFlow>
    <sequenceFlow id="sid-0F06257A-767B-41D9-A965-556E3A1493E7" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-5E1C7974-7A64-4A02-9738-BC943EA13FB0" sourceRef="sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==1}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_warning_23">
    <bpmndi:BPMNPlane bpmnElement="warning_23" id="BPMNPlane_warning_23">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="15.0" y="95.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EA529804-5165-482B-B9C7-09799CC33D82" id="BPMNShape_sid-EA529804-5165-482B-B9C7-09799CC33D82">
        <omgdc:Bounds height="80.0" width="100.0" x="105.0" y="70.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-5654C886-6881-4A5C-84FF-001D402A3013" id="BPMNShape_sid-5654C886-6881-4A5C-84FF-001D402A3013">
        <omgdc:Bounds height="80.0" width="100.0" x="825.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" id="BPMNShape_sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
        <omgdc:Bounds height="80.0" width="100.0" x="930.0000135248367" y="370.0000092983253"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_23_restartFlag" id="BPMNShape_warning_23_restartFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="240.0" y="370.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="28.0" x="16.0" y="396.0000092983253"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" id="BPMNShape_sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE">
        <omgdc:Bounds height="40.0" width="40.0" x="135.0" y="390.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" id="BPMNShape_sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533">
        <omgdc:Bounds height="40.0" width="40.0" x="960.0000135248367" y="90.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-940DECE4-AF31-4639-AEE7-406E766611B6" id="BPMNShape_sid-940DECE4-AF31-4639-AEE7-406E766611B6">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" id="BPMNShape_sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD">
        <omgdc:Bounds height="80.0" width="100.0" x="555.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB" id="BPMNShape_sid-87ACF3F6-B288-4BF4-BF3C-E19C42CD6FAB">
        <omgdc:Bounds height="40.0" width="40.0" x="720.0" y="90.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_23_replyFlag" id="BPMNShape_warning_23_replyFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="705.0" y="370.0000092983253"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80" id="BPMNShape_sid-3FA2E20B-FB5C-4A7B-B142-ECF2F6FF0E80">
        <omgdc:Bounds height="40.0" width="40.0" x="420.0" y="390.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-7785EE0F-A08E-4D1D-A614-5CD9C961D12A" id="BPMNEdge_sid-7785EE0F-A08E-4D1D-A614-5CD9C961D12A" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="654.9499999999999" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="687.5" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="687.5" y="110.5"></omgdi:waypoint>
        <omgdi:waypoint x="720.0" y="110.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" id="BPMNEdge_sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="155.0" y="390.0"></omgdi:waypoint>
        <omgdi:waypoint x="155.0" y="149.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" id="BPMNEdge_sid-62E0A5DD-0B42-4816-85C9-00D26D586116" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="204.95" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="275.0" y="110.0"></omgdi:waypoint>
        <omgdi:waypoint x="275.0" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="345.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4AC18B3E-B042-4B56-90E1-AB688D83F839" id="BPMNEdge_sid-4AC18B3E-B042-4B56-90E1-AB688D83F839" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="440.0" y="390.0"></omgdi:waypoint>
        <omgdi:waypoint x="440.0" y="259.0"></omgdi:waypoint>
        <omgdi:waypoint x="875.0" y="259.0"></omgdi:waypoint>
        <omgdi:waypoint x="875.0" y="139.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0F06257A-767B-41D9-A965-556E3A1493E7" id="BPMNEdge_sid-0F06257A-767B-41D9-A965-556E3A1493E7" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="980.0000135248367" y="130.44667056085098"></omgdi:waypoint>
        <omgdi:waypoint x="980.0000135248367" y="370.0000092983253"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-78B3CDF6-4817-4FD5-936D-DC59B06464AF" id="BPMNEdge_sid-78B3CDF6-4817-4FD5-936D-DC59B06464AF" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="930.000013524656" y="410.0000092983253"></omgdi:waypoint>
        <omgdi:waypoint x="804.9499999997834" y="410.0000092983253"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" id="BPMNEdge_sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="980.0000106318239" y="90.5"></omgdi:waypoint>
        <omgdi:waypoint x="980.0" y="17.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="17.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="60.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" id="BPMNEdge_sid-EE3EBED8-342B-4E65-85E3-90537AC93DB1" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="444.95000000000005" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="555.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-70917429-160A-445A-8F66-91F5271D80D8" id="BPMNEdge_sid-70917429-160A-445A-8F66-91F5271D80D8" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="420.0" y="410.0"></omgdi:waypoint>
        <omgdi:waypoint x="339.95000000000005" y="410.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" id="BPMNEdge_sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="135.00000079472852" y="410.00000148401256"></omgdi:waypoint>
        <omgdi:waypoint x="43.94993072618436" y="410.0000082569129"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" id="BPMNEdge_sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="924.9499999999999" y="104.99499935660427"></omgdi:waypoint>
        <omgdi:waypoint x="961.7843991233682" y="108.68181839472388"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" id="BPMNEdge_sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="44.94988292724031" y="110.4400004698572"></omgdi:waypoint>
        <omgdi:waypoint x="104.99999999999929" y="110.19980000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E58F3F89-C4D6-474A-89C4-1A6E41B4A3C5" id="BPMNEdge_sid-E58F3F89-C4D6-474A-89C4-1A6E41B4A3C5" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="704.9999982882771" y="410.0000078224006"></omgdi:waypoint>
        <omgdi:waypoint x="459.90315120792195" y="410.000000588894"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5E1C7974-7A64-4A02-9738-BC943EA13FB0" id="BPMNEdge_sid-5E1C7974-7A64-4A02-9738-BC943EA13FB0" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="720.0" y="110.5"></omgdi:waypoint>
        <omgdi:waypoint x="582.5" y="110.5"></omgdi:waypoint>
        <omgdi:waypoint x="582.5" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="444.95000000000005" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" id="BPMNEdge_sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="240.0" y="410.0"></omgdi:waypoint>
        <omgdi:waypoint x="174.907357301705" y="410.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0F1525FE-6AB9-4F23-B7A0-6179558311B7" id="BPMNEdge_sid-0F1525FE-6AB9-4F23-B7A0-6179558311B7" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="759.931036121663" y="110.5"></omgdi:waypoint>
        <omgdi:waypoint x="792.5" y="110.5"></omgdi:waypoint>
        <omgdi:waypoint x="792.5" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="825.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>