{"key": "warning_23_step1", "name": "工单处理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "处理并提交三级领导审批"}], "params": {"setVisible": {"1": ["dept", "taskUser"]}}, "optionsExpression": null}, {"fieldType": "OptionFormField", "id": "dept", "name": "领导部门", "type": "deptSelect", "required": true, "placeholder": "empty", "params": {"hidden": true}}, {"fieldType": "OptionFormField", "id": "taskUser", "name": "选择三级领导", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "overrideId": true, "placeholder": "empty", "params": {"hidden": true, "userNum": "one"}}, {"fieldType": "OptionFormField", "id": "reply_isRepair", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}], "params": {"syncFlag": "var"}, "optionsExpression": null}, {"fieldType": "FormField", "id": "reply_visting", "name": "拜访方式", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}, {"fieldType": "FormField", "id": "reply_causeAnaysis", "name": "原因分析", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}, {"fieldType": "FormField", "id": "reply_repairPlan", "name": "省公司修复方案或举措", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}, {"fieldType": "FormField", "id": "reply_repairProcer", "name": "修复过程中重点问题", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}, {"fieldType": "OptionFormField", "id": "reply_isFollowUp", "name": "是否需要跟进", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "0", "name": "是"}, {"id": "1", "name": "否"}], "params": {"syncFlag": "var"}, "optionsExpression": null}, {"fieldType": "FormField", "id": "reply_customerResponse", "name": "客户反应问题的解决举", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}, {"fieldType": "FormField", "id": "reply_deadline", "name": "完成时间", "type": "date", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": false, "syncFlag": "var"}}]}