{"key": "warning_01", "name": "重点政策投诉预测预警单", "fields": [{"fieldType": "OptionFormField", "id": "identyDetail", "name": "工单细类", "type": "dropdown", "value": {"name": "请选择一个..."}, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "00000101", "name": "重点政策投诉"}], "params": {"showType": "2", "order": 1}, "optionsExpression": null}, {"fieldType": "FormField", "id": "title", "name": "工单标题", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "content", "name": "工单内容", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"id": "品质管理部二级经理", "name": "品质管理部二级经理", "type": "workflowHandler", "required": true, "placeholder": null, "params": {"showType": "1", "userRoleId": "warning_01_accept"}}, {"fieldType": "FormField", "id": "processTime", "name": "要求处理时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 11}}, {"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 2}}, {"fieldType": "FormField", "id": "originUnit", "name": "工单发起方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 5}}, {"fieldType": "FormField", "id": "receiver<PERSON><PERSON><PERSON>", "name": "工单接收方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 6}}, {"fieldType": "FormField", "id": "creatTime", "name": "创建时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 7}}, {"fieldType": "FormField", "id": "creator", "name": "创建人", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 8}}, {"fieldType": "FormField", "id": "creatorContactInfo", "name": "创建人联系方式", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 9}}]}