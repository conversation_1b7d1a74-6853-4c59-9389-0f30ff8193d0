{"key": "warning_01_chose<PERSON>do", "name": "品质管理部接口人处理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "品质管理部二级经理审核", "name": "品质管理部二级经理审核"}, {"id": "回复集团", "name": "回复集团"}], "params": {"setVisible": {"品质管理部二级经理审核": ["dept", "品质管理部二级经理"], "回复集团": ["launchCompany", "forwardCompany", "ccDept", "ccUsers"]}}, "optionsExpression": null}, {"fieldType": "OptionFormField", "id": "dept", "name": "选择受理部门", "type": "deptSelect", "required": true, "placeholder": "empty", "params": {"hidden": true}}, {"fieldType": "OptionFormField", "id": "品质管理部二级经理", "name": "选择审批人", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "overrideId": true, "placeholder": "empty", "params": {"hidden": true, "userRoleId": "task_05_exam_01"}}, {"fieldType": "OptionFormField", "id": "ccDept", "name": "选择抄送人部门", "type": "deptSelect", "required": false, "placeholder": "empty", "params": {"hidden": true}}, {"fieldType": "OptionFormField", "id": "ccUsers", "name": "选择抄送人", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "overrideId": true, "placeholder": "empty", "params": {"hidden": true, "userRoleId": "task_05_exam_01", "userNum": "more"}}, {"fieldType": "OptionFormField", "id": "launchCompany", "name": "发起省专", "type": "codeSelect", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "default": "951"}}, {"fieldType": "OptionFormField", "id": "forwardCompany", "name": "转发省专", "type": "codeSelect", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "default": "0057"}}, {"fieldType": "FormField", "id": "reply_governanceMeasures", "name": "治理举措", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true}}, {"fieldType": "FormField", "id": "reply_goal", "name": "完成标志", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true}}, {"fieldType": "FormField", "id": "reply_completionTime", "name": "完成时限", "type": "stringDate", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "default": "长期开展"}}, {"fieldType": "FormField", "id": "reply_evaluation", "name": "是否需要效果评估", "type": "codeSelect", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "<PERSON><PERSON><PERSON>"}}, {"fieldType": "FormField", "id": "reply_status", "name": "举措进展状态", "type": "codeSelect", "value": "", "required": false, "readOnly": true, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "goverStatus", "default": "01"}}]}