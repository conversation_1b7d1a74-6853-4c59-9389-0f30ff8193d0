{"key": "warning_02_quality_manager", "name": "品质管理部项目经理审核", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "业务归口部门经理审核", "name": "业务归口部门经理审核"}, {"id": "品质管理部二级经理审核", "name": "品质管理部二级经理审核"}, {"id": "结束", "name": "结束"}], "params": {"setVisible": {"业务归口部门经理审核": ["dept", "业务归口部门经理"], "结束": ["launchCompany", "forwardCompany"]}}, "optionsExpression": null}, {"fieldType": "OptionFormField", "id": "dept", "name": "选择受理部门", "type": "deptSelect", "required": true, "placeholder": "empty", "params": {"hidden": true}}, {"id": "业务归口部门经理", "name": "业务归口部门经理", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "placeholder": null, "params": {"hidden": true, "userRoleId": "warning_01_业务归口部门经理"}}, {"fieldType": "OptionFormField", "id": "launchCompany", "name": "发起省专", "type": "codeSelect", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "default": "951"}}, {"fieldType": "OptionFormField", "id": "forwardCompany", "name": "转发省专", "type": "codeSelect", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "default": "0057"}}]}