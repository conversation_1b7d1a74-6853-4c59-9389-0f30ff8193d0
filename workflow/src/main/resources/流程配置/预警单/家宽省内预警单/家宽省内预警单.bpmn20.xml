<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="warning_97" name="家宽省内预警单" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="warning_51" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-EA529804-5165-482B-B9C7-09799CC33D82" name="预警接收人" flowable:assignee="${userId}" flowable:formKey="warning_97_start" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <sequenceFlow id="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" sourceRef="startEvent1" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82"></sequenceFlow>
    <serviceTask id="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <userTask id="sid-5B6C8B6C-9B81-4727-8F9E-232EC1B2014D" name="品质管理部负责人" flowable:assignee="${lastUser}" flowable:formKey="warning_97_end" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" sourceRef="sid-EA529804-5165-482B-B9C7-09799CC33D82" targetRef="sid-5B6C8B6C-9B81-4727-8F9E-232EC1B2014D"></sequenceFlow>
    <sequenceFlow id="sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" sourceRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985"></sequenceFlow>
    <sequenceFlow id="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" sourceRef="sid-5B6C8B6C-9B81-4727-8F9E-232EC1B2014D" targetRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_warning_97">
    <bpmndi:BPMNPlane bpmnElement="warning_97" id="BPMNPlane_warning_97">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="150.0" y="250.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EA529804-5165-482B-B9C7-09799CC33D82" id="BPMNShape_sid-EA529804-5165-482B-B9C7-09799CC33D82">
        <omgdc:Bounds height="80.0" width="100.0" x="270.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="28.0" x="981.0" y="251.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" id="BPMNShape_sid-01B628AE-2481-4CE6-92CE-6A28379DD98D">
        <omgdc:Bounds height="80.0" width="100.0" x="705.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-5B6C8B6C-9B81-4727-8F9E-232EC1B2014D" id="BPMNShape_sid-5B6C8B6C-9B81-4727-8F9E-232EC1B2014D">
        <omgdc:Bounds height="80.0" width="100.0" x="465.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" id="BPMNEdge_sid-089DEE50-4E13-48AE-AB71-18BFE70DDE10" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="804.9499999999477" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="981.0" y="265.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" id="BPMNEdge_sid-62E0A5DD-0B42-4816-85C9-00D26D586116" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="369.9499999999802" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="465.0" y="265.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" id="BPMNEdge_sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="179.94999923927443" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="269.99999999988023" y="265.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" id="BPMNEdge_sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="564.9499999999477" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="705.0" y="265.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>