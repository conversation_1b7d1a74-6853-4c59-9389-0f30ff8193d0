<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="warning_99" name="两类差错反悔办理焦点投诉预警单" isExecutable="true">
    <startEvent id="startEvent1" name="开始" flowable:formKey="warning_99" flowable:formFieldValidation="true"></startEvent>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <serviceTask id="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <userTask id="sid-2354CD29-28D6-403D-8656-F72CCF730DC4" name="品管部门审核" flowable:assignee="pq_liujie" flowable:formKey="warning_99_businessmanager" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4" name="网格长" flowable:assignee="${gridManager}" flowable:formKey="warning_99_chosepersondo" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="create" class="com.asiainfo.sound.service.flow.SendCopyEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0" name="区县经理审核" flowable:assignee="${districtManager}" flowable:formKey="warning_99_qualitystart" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-32EC0414-138D-47D4-9162-76E9A31F5E47" sourceRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985"></sequenceFlow>
    <sequenceFlow id="sid-BD0A554B-DB9E-4EEB-B0CA-7C5FFA70348D" sourceRef="startEvent1" targetRef="sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4"></sequenceFlow>
    <sequenceFlow id="sid-3C6B3ADB-E8D0-4501-80EF-DF591DB43A16" sourceRef="sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4" targetRef="sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0"></sequenceFlow>
    <exclusiveGateway id="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D"></exclusiveGateway>
    <sequenceFlow id="sid-CBF17287-CCBD-434E-B745-4E456DE8A4D2" sourceRef="sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0" targetRef="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D"></sequenceFlow>
    <exclusiveGateway id="sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A"></exclusiveGateway>
    <sequenceFlow id="sid-B0B35507-1899-4F1A-A7CD-A463732BA283" sourceRef="sid-2354CD29-28D6-403D-8656-F72CCF730DC4" targetRef="sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A"></sequenceFlow>
    <sequenceFlow id="sid-C91A5470-8791-4CD5-B9A0-CE6B33C09766" sourceRef="sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A" targetRef="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-FBAB9DC9-16D1-4504-86DB-E72BF1E17FB5" sourceRef="sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A" targetRef="sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-F2E82503-7986-4258-8263-A854893B7CDD" name="分管服务副总审核" flowable:assignee="${secondDistrictManager}" flowable:formKey="warning_99_second_qualitystart" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-B6D9BCDF-DC3D-47EA-B336-069CB305B1E8" sourceRef="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D" targetRef="sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-E9658B3D-6088-4202-A6A0-EAC1097A85AB" sourceRef="sid-F2E82503-7986-4258-8263-A854893B7CDD" targetRef="sid-2354CD29-28D6-403D-8656-F72CCF730DC4"></sequenceFlow>
    <sequenceFlow id="sid-A61E472F-8340-478C-85E3-237DD4D215B7" sourceRef="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D" targetRef="sid-F2E82503-7986-4258-8263-A854893B7CDD">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品管部门审核'&&repeatTrigger=='0'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-31C8D748-F829-4A16-88C0-0CB7345F2E40" sourceRef="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D" targetRef="sid-2354CD29-28D6-403D-8656-F72CCF730DC4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品管部门审核'&&repeatTrigger=='1'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_warning_99">
    <bpmndi:BPMNPlane bpmnElement="warning_99" id="BPMNPlane_warning_99">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.000000000000007" x="59.99999642372148" y="99.99999701976785"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="28.0" x="1095.0" y="101.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-01B628AE-2481-4CE6-92CE-6A28379DD98D" id="BPMNShape_sid-01B628AE-2481-4CE6-92CE-6A28379DD98D">
        <omgdc:Bounds height="80.0" width="100.0" x="915.0" y="75.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-2354CD29-28D6-403D-8656-F72CCF730DC4" id="BPMNShape_sid-2354CD29-28D6-403D-8656-F72CCF730DC4">
        <omgdc:Bounds height="79.99999999999997" width="100.0" x="599.9999821186071" y="74.9999977648259"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4" id="BPMNShape_sid-9B85CBAD-3BA0-4AC6-B937-F292816EE5A4">
        <omgdc:Bounds height="80.00000000000001" width="100.00000000000006" x="164.999990165234" y="74.99999776482589"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0" id="BPMNShape_sid-F52DDE25-97AB-4CE1-B52F-1980A2B459C0">
        <omgdc:Bounds height="80.00000000000001" width="100.00000000000006" x="344.99998971819906" y="74.99999776482586"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D" id="BPMNShape_sid-6DD0FBD7-CBF5-43B4-B677-67ED36C6A18D">
        <omgdc:Bounds height="40.0" width="40.0" x="509.99998480081604" y="94.99999716877946"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A" id="BPMNShape_sid-5AE8EFE7-4978-413F-A6C8-544C53FC287A">
        <omgdc:Bounds height="40.0" width="40.0" x="764.9999544024488" y="94.99999418854736"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F2E82503-7986-4258-8263-A854893B7CDD" id="BPMNShape_sid-F2E82503-7986-4258-8263-A854893B7CDD">
        <omgdc:Bounds height="80.0" width="100.0" x="630.0" y="255.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-A61E472F-8340-478C-85E3-237DD4D215B7" id="BPMNEdge_sid-A61E472F-8340-478C-85E3-237DD4D215B7" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="539.1110034414373" y="125.83871599965258"></omgdi:waypoint>
        <omgdi:waypoint x="646.6852339072651" y="255.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BD0A554B-DB9E-4EEB-B0CA-7C5FFA70348D" id="BPMNEdge_sid-BD0A554B-DB9E-4EEB-B0CA-7C5FFA70348D" flowable:sourceDockerX="15.000000000000004" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.00000000000003" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="89.9499954912883" y="114.9999970993294"></omgdi:waypoint>
        <omgdi:waypoint x="164.99998820424133" y="114.99999749873372"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3C6B3ADB-E8D0-4501-80EF-DF591DB43A16" id="BPMNEdge_sid-3C6B3ADB-E8D0-4501-80EF-DF591DB43A16" flowable:sourceDockerX="50.00000000000003" flowable:sourceDockerY="40.00000000000001" flowable:targetDockerX="50.00000000000003" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="264.9499901652341" y="114.99999776482588"></omgdi:waypoint>
        <omgdi:waypoint x="344.99998971819906" y="114.99999776482588"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CBF17287-CCBD-434E-B745-4E456DE8A4D2" id="BPMNEdge_sid-CBF17287-CCBD-434E-B745-4E456DE8A4D2" flowable:sourceDockerX="50.00000000000003" flowable:sourceDockerY="40.00000000000001" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="444.94998971819916" y="114.99999754406792"></omgdi:waypoint>
        <omgdi:waypoint x="509.9999848891192" y="114.99999725686185"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B0B35507-1899-4F1A-A7CD-A463732BA283" id="BPMNEdge_sid-B0B35507-1899-4F1A-A7CD-A463732BA283" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.999999999999986" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="699.9499816644187" y="114.999996440278"></omgdi:waypoint>
        <omgdi:waypoint x="764.999954534904" y="114.99999471704199"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E9658B3D-6088-4202-A6A0-EAC1097A85AB" id="BPMNEdge_sid-E9658B3D-6088-4202-A6A0-EAC1097A85AB" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="673.3333294424748" y="255.0"></omgdi:waypoint>
        <omgdi:waypoint x="656.6583193379355" y="154.94999776482587"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B6D9BCDF-DC3D-47EA-B336-069CB305B1E8" id="BPMNEdge_sid-B6D9BCDF-DC3D-47EA-B336-069CB305B1E8" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.00000000000003" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="530.1466781874246" y="134.79608239693098"></omgdi:waypoint>
        <omgdi:waypoint x="531.0" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="214.0" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="214.70369678784814" y="154.94999776482592"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-31C8D748-F829-4A16-88C0-0CB7345F2E40" id="BPMNEdge_sid-31C8D748-F829-4A16-88C0-0CB7345F2E40" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="549.9416791229771" y="114.99999726787217"></omgdi:waypoint>
        <omgdi:waypoint x="599.9999810904269" y="114.9999975164732"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C91A5470-8791-4CD5-B9A0-CE6B33C09766" id="BPMNEdge_sid-C91A5470-8791-4CD5-B9A0-CE6B33C09766" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="804.9444151705702" y="114.99999483264985"></omgdi:waypoint>
        <omgdi:waypoint x="915.0" y="114.99999838570801"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FBAB9DC9-16D1-4504-86DB-E72BF1E17FB5" id="BPMNEdge_sid-FBAB9DC9-16D1-4504-86DB-E72BF1E17FB5" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.00000000000003" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="784.9999649800153" y="95.00000479262414"></omgdi:waypoint>
        <omgdi:waypoint x="785.0" y="29.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="29.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.53487820927387" y="74.99999776482586"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-32EC0414-138D-47D4-9162-76E9A31F5E47" id="BPMNEdge_sid-32EC0414-138D-47D4-9162-76E9A31F5E47" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1014.9499999999999" y="115.0"></omgdi:waypoint>
        <omgdi:waypoint x="1095.0" y="115.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>