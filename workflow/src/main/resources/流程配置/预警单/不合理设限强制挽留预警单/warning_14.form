{"key": "warning_14", "name": "不合理设限强制挽留预警单", "fields": [{"fieldType": "FormField", "id": "title", "name": "工单标题", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "content", "name": "工单内容", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"fieldType": "OptionFormField", "id": "userNameList", "name": "选择多个处理人", "type": "workflowHandlerList", "required": true, "placeholder": "empty", "params": {"showType": "1", "userRoleId": "warning_14_accept"}}, {"fieldType": "FormField", "id": "processTime", "name": "要求处理时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 11}}, {"fieldType": "OptionFormField", "id": "noticeSend", "name": "通知", "type": "select", "value": "11", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "11", "name": "短信"}, {"id": "00", "name": "不通知"}], "optionsExpression": null, "params": {"showType": "1"}}, {"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 2}}, {"fieldType": "FormField", "id": "originUnit", "name": "工单发起方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 5}}, {"fieldType": "FormField", "id": "receiver<PERSON><PERSON><PERSON>", "name": "工单接收方", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 6}}, {"fieldType": "FormField", "id": "creatTime", "name": "创建时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 8}}, {"fieldType": "FormField", "id": "creator", "name": "创建人", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 9}}, {"fieldType": "FormField", "id": "creatorContactInfo", "name": "创建人联系方式", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 10}}]}