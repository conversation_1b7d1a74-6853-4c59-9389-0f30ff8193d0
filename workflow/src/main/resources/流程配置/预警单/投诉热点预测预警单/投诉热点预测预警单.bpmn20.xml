<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="warning_03" name="投诉热点预测预警单" isExecutable="true">
    <startEvent id="startEvent1" name="开始" flowable:formKey="warning_03" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-EA529804-5165-482B-B9C7-09799CC33D82" name="品质管理部接口人" flowable:assignee="${userId}" flowable:formKey="warning_03_品质管理部接口人处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-3629318B-A360-4ED2-A448-DE84519FCA5B"></exclusiveGateway>
    <serviceTask id="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" name="生成附件" flowable:class="com.asiainfo.sound.service.flow.IdentiferAttachList"></serviceTask>
    <receiveTask id="warning_03_restartFlag" name="集团操作"></receiveTask>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <exclusiveGateway id="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></exclusiveGateway>
    <exclusiveGateway id="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6"></exclusiveGateway>
    <serviceTask id="sid-A69663AD-A21D-4B1D-92FA-4C7BA0E69FAD" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <sequenceFlow id="sid-5C8966AD-CB86-40EF-BC04-2D72DD9344B4" sourceRef="sid-A69663AD-A21D-4B1D-92FA-4C7BA0E69FAD" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985"></sequenceFlow>
    <userTask id="sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB" name="业务归口部门经理" flowable:assignee="${业务归口部门经理}" flowable:formKey="warning_03_业务归口部门经理处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-********-5A69-48F1-9228-D5BA8090B350"></exclusiveGateway>
    <userTask id="sid-DDDA89A3-2A7E-4905-A7DD-3CE9808E1CD5" name="业务归口部门三级经理" flowable:assignee="${业务归口部门三级经理}" flowable:formKey="warning_03_业务归口部门三级经理审核A" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-DDF1BD21-08A1-4574-A0FD-2B275EAEE4F6" name="品质管理部项目经理处理" flowable:assignee="${品质管理部项目经理处理}" flowable:formKey="warning_03_品质管理部项目经理处理1" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-64A13FF4-BB89-4E80-A4A6-AB88E5383F4B" name="品质管理部经理审核" flowable:assignee="${品质管理部经理审核}" flowable:formKey="warning_03_品质管理部经理审核A" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143"></exclusiveGateway>
    <sequenceFlow id="sid-992EB39D-6026-4D81-A4E6-8CFE0EC3EF58" sourceRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6" targetRef="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143"></sequenceFlow>
    <userTask id="sid-317D461D-8A3A-4123-80A3-F1C06C3EE796" name="品质管理部经理审核" flowable:assignee="${品质管理部经理审核}" flowable:formKey="warning_03_品质管理部经理审核B" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1" name="品质管理部项目经理上报归档" flowable:assignee="${品质管理部项目经理}" flowable:formKey="warning_03_品质管理部项目经理上报归档" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D" name="业务归口部门经理" flowable:assignee="${业务归口部门经理}" flowable:formKey="warning_03_业务归口部门经理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-A2382669-301F-4641-9A45-78EE8CA18294" sourceRef="sid-DD08E47A-2FF0-4859-B4F5-327F72520EAE" targetRef="sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D"></sequenceFlow>
    <userTask id="sid-DD08E47A-2FF0-4859-B4F5-327F72520EAE" name="业务归口部门三级经理" flowable:assignee="${业务归口部门三级经理}" flowable:formKey="warning_03_业务归口部门三级经理审核B" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-39345D33-1442-4915-9742-E518757C460D"></exclusiveGateway>
    <sequenceFlow id="sid-CA5D6C3F-7588-45FA-B2F7-159EFCAFCAFC" sourceRef="sid-DDF1BD21-08A1-4574-A0FD-2B275EAEE4F6" targetRef="sid-39345D33-1442-4915-9742-E518757C460D"></sequenceFlow>
    <exclusiveGateway id="sid-F175170D-BCAE-48C4-B829-958DC03FD64E"></exclusiveGateway>
    <sequenceFlow id="sid-F455946B-ED36-4183-9CD4-53500D6E295B" sourceRef="sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D" targetRef="sid-F175170D-BCAE-48C4-B829-958DC03FD64E"></sequenceFlow>
    <exclusiveGateway id="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></exclusiveGateway>
    <sequenceFlow id="sid-17F21F0D-63EE-4818-AF1C-89183E828E75" sourceRef="sid-********-5A69-48F1-9228-D5BA8090B350" targetRef="sid-DDDA89A3-2A7E-4905-A7DD-3CE9808E1CD5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='业务归口部门三级经理处理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-1653F505-DCE5-49B8-9C5D-15073F189E05" sourceRef="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143" targetRef="sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='业务归口部门经理处理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" sourceRef="sid-EA529804-5165-482B-B9C7-09799CC33D82" targetRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6"></sequenceFlow>
    <sequenceFlow id="sid-F679C731-A430-463C-8109-0BEA8F390084" sourceRef="sid-317D461D-8A3A-4123-80A3-F1C06C3EE796" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6"></sequenceFlow>
    <sequenceFlow id="sid-FDCF767B-E5FA-4391-9010-399C4298203B" sourceRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" sourceRef="startEvent1" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82"></sequenceFlow>
    <sequenceFlow id="sid-A3AF66F7-3CBD-420F-B6E7-4BE5106ABB99" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='回复集团'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C616DF08-C5C1-4015-BFF8-8A5AD4AC9FA6" sourceRef="sid-DDDA89A3-2A7E-4905-A7DD-3CE9808E1CD5" targetRef="sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB"></sequenceFlow>
    <sequenceFlow id="sid-42893461-3C3A-4501-BDB8-7D63788AF52F" sourceRef="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143" targetRef="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部项目经理上报归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" sourceRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" targetRef="sid-A69663AD-A21D-4B1D-92FA-4C7BA0E69FAD">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${type==0}]]></conditionExpression>
    </sequenceFlow>
    <receiveTask id="warning_03_replyFlag" name="回复集团"></receiveTask>
    <sequenceFlow id="sid-A141FFEE-6B0D-40A2-A688-D1FC523928D3" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='业务归口部门经理'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C"></exclusiveGateway>
    <sequenceFlow id="sid-D04B1D0A-E9B0-4FF5-A23D-222794143C38" sourceRef="warning_03_replyFlag" targetRef="sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C"></sequenceFlow>
    <sequenceFlow id="sid-26EC7907-7C09-4741-B655-2691E4709755" sourceRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" targetRef="warning_03_replyFlag"></sequenceFlow>
    <sequenceFlow id="sid-BBC14921-F309-4F69-B96A-FD6D783FDDDD" sourceRef="sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-120FEC28-AFF8-4CAE-96AE-E8C0457D3811" sourceRef="sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C" targetRef="warning_03_restartFlag">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" sourceRef="warning_03_restartFlag" targetRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></sequenceFlow>
    <sequenceFlow id="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-8DC0E93C-A98D-42B5-9F39-7E72596B7FB2" sourceRef="sid-F175170D-BCAE-48C4-B829-958DC03FD64E" targetRef="sid-DD08E47A-2FF0-4859-B4F5-327F72520EAE">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='业务归口部门三级经理处理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" sourceRef="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1" targetRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533"></sequenceFlow>
    <sequenceFlow id="sid-0F06257A-767B-41D9-A965-556E3A1493E7" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-3629318B-A360-4ED2-A448-DE84519FCA5B">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='回复集团'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-97CFCF2F-5069-4B81-968D-2E33768B7E97" sourceRef="sid-39345D33-1442-4915-9742-E518757C460D" targetRef="sid-64A13FF4-BB89-4E80-A4A6-AB88E5383F4B">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部经理审核'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-FE3FFF2F-7683-4FEE-AEA3-D926E24372A8" sourceRef="sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB" targetRef="sid-********-5A69-48F1-9228-D5BA8090B350"></sequenceFlow>
    <sequenceFlow id="sid-B32A136D-3352-42CB-BF8A-D484E8B8A3A3" sourceRef="sid-********-5A69-48F1-9228-D5BA8090B350" targetRef="sid-DDF1BD21-08A1-4574-A0FD-2B275EAEE4F6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部项目经理处理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" sourceRef="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部接口人处理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-80DDAC4A-2A76-45CD-82E4-A0D9D202CAE3" sourceRef="sid-64A13FF4-BB89-4E80-A4A6-AB88E5383F4B" targetRef="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1"></sequenceFlow>
    <sequenceFlow id="sid-9409C096-2669-4888-8A14-6690A376E236" sourceRef="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部项目经理处理'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-940DECE4-AF31-4639-AEE7-406E766611B6" name="品质管理部项目经理处理" flowable:assignee="${品质管理部项目经理处理}" flowable:formKey="warning_03_品质管理部项目经理处理2" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-27C11555-465D-4F93-9F50-173010F3A5EC" sourceRef="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143" targetRef="sid-317D461D-8A3A-4123-80A3-F1C06C3EE796">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部经理审核'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-F88C800B-CDC0-4FB8-9AAD-1CAF25E0A841" sourceRef="sid-F175170D-BCAE-48C4-B829-958DC03FD64E" targetRef="sid-940DECE4-AF31-4639-AEE7-406E766611B6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='回复品质管理部项目经理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-37FACF19-0AF9-411E-81B7-2AF405028D0D" sourceRef="sid-39345D33-1442-4915-9742-E518757C460D" targetRef="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部项目经理上报归档'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_warning_03">
    <bpmndi:BPMNPlane bpmnElement="warning_03" id="BPMNPlane_warning_03">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="136.36363340803422" y="409.0909002241026"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EA529804-5165-482B-B9C7-09799CC33D82" id="BPMNShape_sid-EA529804-5165-482B-B9C7-09799CC33D82">
        <omgdc:Bounds height="80.0" width="99.99999999999999" x="95.45454338562395" y="490.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3629318B-A360-4ED2-A448-DE84519FCA5B" id="BPMNShape_sid-3629318B-A360-4ED2-A448-DE84519FCA5B">
        <omgdc:Bounds height="40.0" width="40.0" x="855.0" y="890.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" id="BPMNShape_sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
        <omgdc:Bounds height="80.0" width="100.0" x="600.0" y="870.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_03_restartFlag" id="BPMNShape_warning_03_restartFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="279.0" y="1120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="28.0" x="315.0" y="1061.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" id="BPMNShape_sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE">
        <omgdc:Bounds height="40.0" width="40.0" x="135.0" y="975.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6" id="BPMNShape_sid-A2EA1A8A-5C12-4E1D-9734-D050C85F38C6">
        <omgdc:Bounds height="40.0" width="40.0" x="240.0" y="510.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A69663AD-A21D-4B1D-92FA-4C7BA0E69FAD" id="BPMNShape_sid-A69663AD-A21D-4B1D-92FA-4C7BA0E69FAD">
        <omgdc:Bounds height="80.0" width="100.0" x="705.0" y="1035.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB" id="BPMNShape_sid-4F87FC0D-5650-44E3-9CAE-262FDB985CBB">
        <omgdc:Bounds height="79.99999999999997" width="100.0" x="40.90909002241026" y="135.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-********-5A69-48F1-9228-D5BA8090B350" id="BPMNShape_sid-********-5A69-48F1-9228-D5BA8090B350">
        <omgdc:Bounds height="40.0" width="40.0" x="240.0" y="155.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DDDA89A3-2A7E-4905-A7DD-3CE9808E1CD5" id="BPMNShape_sid-DDDA89A3-2A7E-4905-A7DD-3CE9808E1CD5">
        <omgdc:Bounds height="80.0" width="100.0" x="210.0" y="13.63636334080342"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DDF1BD21-08A1-4574-A0FD-2B275EAEE4F6" id="BPMNShape_sid-DDF1BD21-08A1-4574-A0FD-2B275EAEE4F6">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-64A13FF4-BB89-4E80-A4A6-AB88E5383F4B" id="BPMNShape_sid-64A13FF4-BB89-4E80-A4A6-AB88E5383F4B">
        <omgdc:Bounds height="80.0" width="100.0" x="474.9999999999998" y="218.18181345285473"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143" id="BPMNShape_sid-34721A9E-0F46-4EC8-AC24-B3C9556BD143">
        <omgdc:Bounds height="40.0" width="40.0" x="505.0" y="510.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-317D461D-8A3A-4123-80A3-F1C06C3EE796" id="BPMNShape_sid-317D461D-8A3A-4123-80A3-F1C06C3EE796">
        <omgdc:Bounds height="80.0" width="100.0" x="475.0" y="327.2727201792821"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1" id="BPMNShape_sid-35B61B3A-87B6-46FD-BD38-35F5B20670D1">
        <omgdc:Bounds height="80.0" width="100.0" x="818.1818004482052" y="218.18181345285473"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D" id="BPMNShape_sid-014AA2B8-C5EC-434E-B8A9-2A3702A8757D">
        <omgdc:Bounds height="80.0" width="100.0" x="474.9999999999998" y="605.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DD08E47A-2FF0-4859-B4F5-327F72520EAE" id="BPMNShape_sid-DD08E47A-2FF0-4859-B4F5-327F72520EAE">
        <omgdc:Bounds height="80.0" width="100.00000000000011" x="474.9999999999998" y="722.7272570625813"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-39345D33-1442-4915-9742-E518757C460D" id="BPMNShape_sid-39345D33-1442-4915-9742-E518757C460D">
        <omgdc:Bounds height="40.0" width="40.0" x="505.0" y="155.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F175170D-BCAE-48C4-B829-958DC03FD64E" id="BPMNShape_sid-F175170D-BCAE-48C4-B829-958DC03FD64E">
        <omgdc:Bounds height="40.0" width="40.0" x="390.0" y="625.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533" id="BPMNShape_sid-EC2CADE6-DCC3-4795-BA81-82BD4BE85533">
        <omgdc:Bounds height="40.0" width="40.0" x="855.0" y="810.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="warning_03_replyFlag" id="BPMNShape_warning_03_replyFlag">
        <omgdc:Bounds height="80.0" width="100.0" x="600.0" y="1120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C" id="BPMNShape_sid-88C8189E-D5DE-4FEC-8663-2E4AAC85EA9C">
        <omgdc:Bounds height="40.0" width="40.0" x="480.0" y="1140.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-940DECE4-AF31-4639-AEE7-406E766611B6" id="BPMNShape_sid-940DECE4-AF31-4639-AEE7-406E766611B6">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="490.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-A3AF66F7-3CBD-420F-B6E7-4BE5106ABB99" id="BPMNEdge_sid-A3AF66F7-3CBD-420F-B6E7-4BE5106ABB99" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="260.0" y="550.4473722339304"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="910.0"></omgdi:waypoint>
        <omgdi:waypoint x="600.0" y="910.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-120FEC28-AFF8-4CAE-96AE-E8C0457D3811" id="BPMNEdge_sid-120FEC28-AFF8-4CAE-96AE-E8C0457D3811" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="480.4415204678162" y="1160.4415204678362"></omgdi:waypoint>
        <omgdi:waypoint x="378.95000000000005" y="1160.1456268221575"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" id="BPMNEdge_sid-62E0A5DD-0B42-4816-85C9-00D26D586116" flowable:sourceDockerX="49.99999999999999" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="195.40454338562392" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="240.0" y="530.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-992EB39D-6026-4D81-A4E6-8CFE0EC3EF58" id="BPMNEdge_sid-992EB39D-6026-4D81-A4E6-8CFE0EC3EF58" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="459.9499999998728" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="505.0" y="530.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0F06257A-767B-41D9-A965-556E3A1493E7" id="BPMNEdge_sid-0F06257A-767B-41D9-A965-556E3A1493E7" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="875.0" y="849.9375468164795"></omgdi:waypoint>
        <omgdi:waypoint x="875.0" y="890.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F455946B-ED36-4183-9CD4-53500D6E295B" id="BPMNEdge_sid-F455946B-ED36-4183-9CD4-53500D6E295B" flowable:sourceDockerX="50.00000000000001" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="474.9999999999998" y="645.0"></omgdi:waypoint>
        <omgdi:waypoint x="429.9086379460402" y="645.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" id="BPMNEdge_sid-91D975B0-5CBD-41AA-994B-9AE75AACF441" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="875.0" y="929.9439582071471"></omgdi:waypoint>
        <omgdi:waypoint x="875.0" y="1075.0"></omgdi:waypoint>
        <omgdi:waypoint x="804.9499999999999" y="1075.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-97CFCF2F-5069-4B81-968D-2E33768B7E97" id="BPMNEdge_sid-97CFCF2F-5069-4B81-968D-2E33768B7E97" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.000000000000014" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="525.0" y="194.9380225951065"></omgdi:waypoint>
        <omgdi:waypoint x="524.9999999999999" y="218.18181345285473"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" id="BPMNEdge_sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="174.94427053396936" y="995.0"></omgdi:waypoint>
        <omgdi:waypoint x="329.0" y="995.0"></omgdi:waypoint>
        <omgdi:waypoint x="329.0" y="1061.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-17F21F0D-63EE-4818-AF1C-89183E828E75" id="BPMNEdge_sid-17F21F0D-63EE-4818-AF1C-89183E828E75" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="260.0" y="155.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="93.58636334080343"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A2382669-301F-4641-9A45-78EE8CA18294" id="BPMNEdge_sid-A2382669-301F-4641-9A45-78EE8CA18294" flowable:sourceDockerX="50.00000000000007" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.00000000000001" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="524.9999999999999" y="722.7272570625813"></omgdi:waypoint>
        <omgdi:waypoint x="524.9999999999998" y="684.9499999999999"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FDCF767B-E5FA-4391-9010-399C4298203B" id="BPMNEdge_sid-FDCF767B-E5FA-4391-9010-399C4298203B" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="855.0" y="910.0"></omgdi:waypoint>
        <omgdi:waypoint x="699.9499999999999" y="910.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CA5D6C3F-7588-45FA-B2F7-159EFCAFCAFC" id="BPMNEdge_sid-CA5D6C3F-7588-45FA-B2F7-159EFCAFCAFC" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="444.95000000000005" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="505.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BBC14921-F309-4F69-B96A-FD6D783FDDDD" id="BPMNEdge_sid-BBC14921-F309-4F69-B96A-FD6D783FDDDD" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="49.99999999999999" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="500.5" y="1179.4389778919463"></omgdi:waypoint>
        <omgdi:waypoint x="500.5" y="1246.3635920256625"></omgdi:waypoint>
        <omgdi:waypoint x="9.5" y="1246.3635920256625"></omgdi:waypoint>
        <omgdi:waypoint x="9.5" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="95.45454338562395" y="530.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9409C096-2669-4888-8A14-6690A376E236" id="BPMNEdge_sid-9409C096-2669-4888-8A14-6690A376E236" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="279.94335443029337" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="360.0" y="530.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FE3FFF2F-7683-4FEE-AEA3-D926E24372A8" id="BPMNEdge_sid-FE3FFF2F-7683-4FEE-AEA3-D926E24372A8" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.999999999999986" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="140.8590900223879" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="240.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-37FACF19-0AF9-411E-81B7-2AF405028D0D" id="BPMNEdge_sid-37FACF19-0AF9-411E-81B7-2AF405028D0D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="56.0">
        <omgdi:waypoint x="544.9470942240495" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="868.1818004482052" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="868.1818004482052" y="218.18181345285473"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5C8966AD-CB86-40EF-BC04-2D72DD9344B4" id="BPMNEdge_sid-5C8966AD-CB86-40EF-BC04-2D72DD9344B4" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="705.0" y="1075.0"></omgdi:waypoint>
        <omgdi:waypoint x="342.9499175899095" y="1075.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C616DF08-C5C1-4015-BFF8-8A5AD4AC9FA6" id="BPMNEdge_sid-C616DF08-C5C1-4015-BFF8-8A5AD4AC9FA6" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="210.0" y="53.63636334080342"></omgdi:waypoint>
        <omgdi:waypoint x="90.90909002241025" y="53.63636334080342"></omgdi:waypoint>
        <omgdi:waypoint x="90.90909002241025" y="135.00000000000003"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A141FFEE-6B0D-40A2-A688-D1FC523928D3" id="BPMNEdge_sid-A141FFEE-6B0D-40A2-A688-D1FC523928D3" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="260.0" y="510.5"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="383.0"></omgdi:waypoint>
        <omgdi:waypoint x="90.90909002241025" y="383.0"></omgdi:waypoint>
        <omgdi:waypoint x="90.90909002241025" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" id="BPMNEdge_sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="278.9999999996985" y="1160.0"></omgdi:waypoint>
        <omgdi:waypoint x="155.0" y="1160.0"></omgdi:waypoint>
        <omgdi:waypoint x="155.0" y="1014.9060187992723"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F679C731-A430-463C-8109-0BEA8F390084" id="BPMNEdge_sid-F679C731-A430-463C-8109-0BEA8F390084" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="475.0" y="367.2727201792821"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="367.2727201792821"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="490.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" id="BPMNEdge_sid-359F56DF-D2A8-4A2C-9402-818411D0FC7D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="49.99999999999999" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="135.0" y="995.0"></omgdi:waypoint>
        <omgdi:waypoint x="57.5" y="995.0"></omgdi:waypoint>
        <omgdi:waypoint x="57.5" y="565.4545446860889"></omgdi:waypoint>
        <omgdi:waypoint x="95.45454338562395" y="550.3509302630922"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-26EC7907-7C09-4741-B655-2691E4709755" id="BPMNEdge_sid-26EC7907-7C09-4741-B655-2691E4709755" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="650.0" y="949.9499999999999"></omgdi:waypoint>
        <omgdi:waypoint x="650.0" y="1120.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" id="BPMNEdge_sid-33352B1F-A4C5-447B-BAE6-8AFC2F2C7294" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="49.99999999999999" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="855.0" y="830.0"></omgdi:waypoint>
        <omgdi:waypoint x="145.45454338562394" y="830.0"></omgdi:waypoint>
        <omgdi:waypoint x="145.45454338562394" y="570.45"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-27C11555-465D-4F93-9F50-173010F3A5EC" id="BPMNEdge_sid-27C11555-465D-4F93-9F50-173010F3A5EC" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="525.0" y="510.5"></omgdi:waypoint>
        <omgdi:waypoint x="525.0" y="407.2227201792821"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D04B1D0A-E9B0-4FF5-A23D-222794143C38" id="BPMNEdge_sid-D04B1D0A-E9B0-4FF5-A23D-222794143C38" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.5" flowable:targetDockerY="20.5">
        <omgdi:waypoint x="600.0" y="1160.1670568561872"></omgdi:waypoint>
        <omgdi:waypoint x="519.4696272666156" y="1160.4362416107383"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F88C800B-CDC0-4FB8-9AAD-1CAF25E0A841" id="BPMNEdge_sid-F88C800B-CDC0-4FB8-9AAD-1CAF25E0A841" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="410.0" y="625.0"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="570.45"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1653F505-DCE5-49B8-9C5D-15073F189E05" id="BPMNEdge_sid-1653F505-DCE5-49B8-9C5D-15073F189E05" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.00000000000001" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="525.0" y="550.4412958115186"></omgdi:waypoint>
        <omgdi:waypoint x="524.9999999999999" y="605.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" id="BPMNEdge_sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="918.1318004482051" y="258.1818134528547"></omgdi:waypoint>
        <omgdi:waypoint x="943.5" y="258.1818134528547"></omgdi:waypoint>
        <omgdi:waypoint x="943.5" y="830.0"></omgdi:waypoint>
        <omgdi:waypoint x="894.9145102339181" y="830.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" id="BPMNEdge_sid-3B4C5ACC-B8AC-4829-84A6-382A64F93B26" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="49.99999999999999" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="150.53191507273846" y="439.0183071503242"></omgdi:waypoint>
        <omgdi:waypoint x="147.67303923694175" y="490.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-80DDAC4A-2A76-45CD-82E4-A0D9D202CAE3" id="BPMNEdge_sid-80DDAC4A-2A76-45CD-82E4-A0D9D202CAE3" flowable:sourceDockerX="50.000000000000014" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="574.9499999999998" y="258.1818134528547"></omgdi:waypoint>
        <omgdi:waypoint x="818.1818004482052" y="258.1818134528547"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-42893461-3C3A-4501-BDB8-7D63788AF52F" id="BPMNEdge_sid-42893461-3C3A-4501-BDB8-7D63788AF52F" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="544.9470942240495" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="868.1818004482052" y="530.5"></omgdi:waypoint>
        <omgdi:waypoint x="868.1818004482052" y="298.1318134528548"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8DC0E93C-A98D-42B5-9F39-7E72596B7FB2" id="BPMNEdge_sid-8DC0E93C-A98D-42B5-9F39-7E72596B7FB2" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.00000000000007" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="410.0" y="664.9415342169132"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="762.7272570625813"></omgdi:waypoint>
        <omgdi:waypoint x="474.9999999999998" y="762.7272570625813"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B32A136D-3352-42CB-BF8A-D484E8B8A3A3" id="BPMNEdge_sid-B32A136D-3352-42CB-BF8A-D484E8B8A3A3" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="279.9426165803081" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="344.99999999997203" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>