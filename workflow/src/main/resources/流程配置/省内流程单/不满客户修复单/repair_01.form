{"key": "repair_01", "name": "不满客户修复单", "fields": [{"fieldType": "FormField", "id": "0487F81F-D4CE-4B33-9E8B-2D0995448F77-ALLOW-FORCE-JUMP", "name": "允许强制跳转", "type": "text", "value": "false", "overrideId": true, "params": {"hidden": true}}, {"fieldType": "FormField", "id": "title", "name": "工单标题", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "name", "name": "客群名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"id": "品质部项目经理", "name": "品质部项目经理", "type": "workflowHandler", "required": true, "placeholder": null, "params": {"showType": "1", "userRoleId": "global_品质部项目经理"}}, {"fieldType": "FormField", "id": "batch", "name": "数据批次", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"fieldType": "FormField", "id": "number", "name": "客群数量", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 5}}, {"fieldType": "FormField", "id": "label", "name": "客群组合标签", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 6}}, {"fieldType": "FormField", "id": "code", "name": "客群编码", "type": "text", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 7}}, {"fieldType": "FormField", "id": "processTime", "name": "要求处理时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 11}}, {"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 8}}, {"fieldType": "FormField", "id": "creatTime", "name": "创建时间", "type": "date", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 11}}]}