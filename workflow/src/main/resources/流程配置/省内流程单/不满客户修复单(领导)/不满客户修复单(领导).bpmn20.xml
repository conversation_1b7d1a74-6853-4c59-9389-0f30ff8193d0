<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="repair_02" name="不满客户修复单(领导)" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="repair_02" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047" name="地市服务管理审批" flowable:assignee="${地市服务管理审批}" flowable:formKey="repair_02_地市服务管理审批" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></exclusiveGateway>
    <userTask id="sid-6B165718-1AD4-4190-B566-24321FC132F5" name="区县服务管理" flowable:assignee="${区县服务管理}" flowable:formKey="repair_02_区县服务管理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-7F66B6FA-A057-4ECC-8B99-BC8E10348D8C" sourceRef="sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047" targetRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></sequenceFlow>
    <userTask id="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" name="区县三级经理审批" flowable:assignee="${区县三级经理审批}" flowable:formKey="repair_02_区县三级经理审批" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-6D31BAF3-5B32-4DFB-A28A-C06BE693C392" name="地市三级经理审批" flowable:assignee="${地市三级经理审批}" flowable:formKey="repair_02_地市三级经理审批" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" name="结束"></endEvent>
    <exclusiveGateway id="sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B"></exclusiveGateway>
    <exclusiveGateway id="sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B"></exclusiveGateway>
    <sequenceFlow id="sid-4786277D-5962-4E03-AA5E-D44E4ACC6C25" sourceRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" targetRef="sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B"></sequenceFlow>
    <sequenceFlow id="sid-7F749439-9420-4C6B-A586-7CF9C1015939" sourceRef="sid-6D31BAF3-5B32-4DFB-A28A-C06BE693C392" targetRef="sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B"></sequenceFlow>
    <sequenceFlow id="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='区县服务管理'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="sid-B190FC06-33BB-478F-862E-12E8D6B7F838" name="结果回传大音" flowable:class="com.asiainfo.sound.service.flow.ResultReplyDY"></serviceTask>
    <sequenceFlow id="sid-60E93724-44C2-4883-9B80-8DD01CC001A7" sourceRef="sid-B190FC06-33BB-478F-862E-12E8D6B7F838" targetRef="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1"></sequenceFlow>
    <sequenceFlow id="sid-E366420B-F5D0-4421-803B-97A777A71299" sourceRef="sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B" targetRef="sid-B190FC06-33BB-478F-862E-12E8D6B7F838">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='结果回传大音'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-80382D95-2A63-402C-9E95-3684953E408F" sourceRef="sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B" targetRef="sid-B190FC06-33BB-478F-862E-12E8D6B7F838">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='结果回传大音'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-C96C1E82-3949-4BAD-AFBB-75FD91C56F48" name="品质部项目经理" flowable:assignee="${品质部项目经理}" flowable:formKey="repair_02_品质部项目经理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-CEB24690-CC6C-483F-8504-42E5718E989A" sourceRef="sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='区县服务管理'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B1EF72DA-3010-4FCB-B66F-F3F2E854BBE9" sourceRef="sid-6B165718-1AD4-4190-B566-24321FC132F5" targetRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1"></sequenceFlow>
    <sequenceFlow id="sid-057C52C5-7121-4133-9B9C-114B456F4C87" sourceRef="sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B" targetRef="sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='地市服务管理审批'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-6D31BAF3-5B32-4DFB-A28A-C06BE693C392">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='地市三级经理审批'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" sourceRef="startEvent1" targetRef="sid-C96C1E82-3949-4BAD-AFBB-75FD91C56F48"></sequenceFlow>
    <userTask id="sid-CCA04228-2DFA-44ED-8E09-9CFC59C425D1" name="分公司领导审批_二级副" flowable:assignee="${分公司领导审批_二级副}" flowable:formKey="repair_02_分公司领导审批_二级副" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-103241B7-ECCC-4F87-B227-0C958ACE88F1" sourceRef="sid-C96C1E82-3949-4BAD-AFBB-75FD91C56F48" targetRef="sid-CCA04228-2DFA-44ED-8E09-9CFC59C425D1"></sequenceFlow>
    <sequenceFlow id="sid-7C813D73-DFC0-47E9-BE81-5C3B1578E73C" sourceRef="sid-CCA04228-2DFA-44ED-8E09-9CFC59C425D1" targetRef="sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_repair_02">
    <bpmndi:BPMNPlane bpmnElement="repair_02" id="BPMNPlane_repair_02">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="30.0" y="191.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047" id="BPMNShape_sid-B5C5E1BA-7F17-4EC7-8175-751CEAF17047">
        <omgdc:Bounds height="80.0" width="100.0" x="355.0" y="166.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" id="BPMNShape_sid-E9C01A12-B666-4A87-82F6-E7BA26030892">
        <omgdc:Bounds height="40.0" width="40.0" x="505.0" y="186.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6B165718-1AD4-4190-B566-24321FC132F5" id="BPMNShape_sid-6B165718-1AD4-4190-B566-24321FC132F5">
        <omgdc:Bounds height="80.0" width="100.0" x="610.0" y="165.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" id="BPMNShape_sid-C78C18CA-4894-4861-922B-BCB0F98600E1">
        <omgdc:Bounds height="80.0" width="100.0" x="1055.5" y="165.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6D31BAF3-5B32-4DFB-A28A-C06BE693C392" id="BPMNShape_sid-6D31BAF3-5B32-4DFB-A28A-C06BE693C392">
        <omgdc:Bounds height="80.0" width="100.0" x="865.0" y="263.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" id="BPMNShape_sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1">
        <omgdc:Bounds height="28.0" width="28.0" x="1210.0" y="473.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B" id="BPMNShape_sid-9B3DAFA9-669A-40D3-A1B7-B94C4347190B">
        <omgdc:Bounds height="40.0" width="40.0" x="895.0" y="383.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B" id="BPMNShape_sid-0F895B70-5B26-40DB-8BA1-78E9BBA5911B">
        <omgdc:Bounds height="40.0" width="40.0" x="1085.5" y="75.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B190FC06-33BB-478F-862E-12E8D6B7F838" id="BPMNShape_sid-B190FC06-33BB-478F-862E-12E8D6B7F838">
        <omgdc:Bounds height="80.0" width="100.0" x="1174.0" y="363.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C96C1E82-3949-4BAD-AFBB-75FD91C56F48" id="BPMNShape_sid-C96C1E82-3949-4BAD-AFBB-75FD91C56F48">
        <omgdc:Bounds height="80.0" width="100.0" x="90.0" y="166.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-CCA04228-2DFA-44ED-8E09-9CFC59C425D1" id="BPMNShape_sid-CCA04228-2DFA-44ED-8E09-9CFC59C425D1">
        <omgdc:Bounds height="80.0" width="100.0" x="225.0" y="166.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-057C52C5-7121-4133-9B9C-114B456F4C87" id="BPMNEdge_sid-057C52C5-7121-4133-9B9C-114B456F4C87" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="895.0" y="403.0"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="403.0"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="245.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B1EF72DA-3010-4FCB-B66F-F3F2E854BBE9" id="BPMNEdge_sid-B1EF72DA-3010-4FCB-B66F-F3F2E854BBE9" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="709.9499999999999" y="205.0"></omgdi:waypoint>
        <omgdi:waypoint x="1055.5" y="205.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" id="BPMNEdge_sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="525.0" y="225.93916938110755"></omgdi:waypoint>
        <omgdi:waypoint x="525.0" y="298.0"></omgdi:waypoint>
        <omgdi:waypoint x="864.9999999999968" y="302.35897435897436"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" id="BPMNEdge_sid-62E0A5DD-0B42-4816-85C9-00D26D586116" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="59.94999797575196" y="206.0"></omgdi:waypoint>
        <omgdi:waypoint x="89.99999999999278" y="206.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7C813D73-DFC0-47E9-BE81-5C3B1578E73C" id="BPMNEdge_sid-7C813D73-DFC0-47E9-BE81-5C3B1578E73C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="324.949999999978" y="206.0"></omgdi:waypoint>
        <omgdi:waypoint x="354.9999999999902" y="206.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" id="BPMNEdge_sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="544.8033088235268" y="205.85294117647058"></omgdi:waypoint>
        <omgdi:waypoint x="609.9999999999948" y="205.37"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7F66B6FA-A057-4ECC-8B99-BC8E10348D8C" id="BPMNEdge_sid-7F66B6FA-A057-4ECC-8B99-BC8E10348D8C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="454.95000000000005" y="206.0"></omgdi:waypoint>
        <omgdi:waypoint x="505.0" y="206.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4786277D-5962-4E03-AA5E-D44E4ACC6C25" id="BPMNEdge_sid-4786277D-5962-4E03-AA5E-D44E4ACC6C25" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="1105.5" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="1105.5" y="114.90903093721568"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7F749439-9420-4C6B-A586-7CF9C1015939" id="BPMNEdge_sid-7F749439-9420-4C6B-A586-7CF9C1015939" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="915.0" y="342.95000000000005"></omgdi:waypoint>
        <omgdi:waypoint x="915.0" y="383.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-60E93724-44C2-4883-9B80-8DD01CC001A7" id="BPMNEdge_sid-60E93724-44C2-4883-9B80-8DD01CC001A7" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1224.0" y="442.95000000000005"></omgdi:waypoint>
        <omgdi:waypoint x="1224.0" y="473.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E366420B-F5D0-4421-803B-97A777A71299" id="BPMNEdge_sid-E366420B-F5D0-4421-803B-97A777A71299" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="934.9467728889763" y="403.0"></omgdi:waypoint>
        <omgdi:waypoint x="1173.999999999875" y="403.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-103241B7-ECCC-4F87-B227-0C958ACE88F1" id="BPMNEdge_sid-103241B7-ECCC-4F87-B227-0C958ACE88F1" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="189.9499999999527" y="206.0"></omgdi:waypoint>
        <omgdi:waypoint x="224.99999999997203" y="206.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-80382D95-2A63-402C-9E95-3684953E408F" id="BPMNEdge_sid-80382D95-2A63-402C-9E95-3684953E408F" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1125.441589376054" y="95.0"></omgdi:waypoint>
        <omgdi:waypoint x="1224.0" y="95.0"></omgdi:waypoint>
        <omgdi:waypoint x="1224.0" y="363.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CEB24690-CC6C-483F-8504-42E5718E989A" id="BPMNEdge_sid-CEB24690-CC6C-483F-8504-42E5718E989A" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1085.5" y="95.0"></omgdi:waypoint>
        <omgdi:waypoint x="663.0" y="95.0"></omgdi:waypoint>
        <omgdi:waypoint x="661.0895454545455" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>