{"key": "apply_71_marketManger3_2", "name": "市场部三级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "品质管理部负责人审核"}], "params": {"setVisible": {"1": ["qualityLower"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "品质管理部", "postName": "负责人"}, "itemId": "qualityLower", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "qualityLower", "name": "审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}