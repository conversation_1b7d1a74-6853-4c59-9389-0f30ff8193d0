{"key": "apply_71_marketManger3_1", "name": "市场部三级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "地市公司副总经理审核"}], "params": {"setVisible": {"1": ["citySecondManger"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "-1", "postName": "部门副总经理"}, "itemId": "citySecondManger", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "citySecondManger", "name": "审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}