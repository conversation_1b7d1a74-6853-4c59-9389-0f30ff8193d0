{"key": "apply_71_qualityManger3", "name": "品质管理部三级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "品质管理部二级经理审核"}], "params": {"setVisible": {"1": ["qualityManger2"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "品质管理部", "postName": "二级经理"}, "itemId": "qualityManger2", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "qualityManger2", "name": "审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}