{"key": "apply_71_createManger3_1", "name": "业务部门三级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "业务部门副总经理审核"}], "params": {"setVisible": {"1": ["createSecondManger"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "-1", "postName": "部门副总经理"}, "itemId": "createSecondManger", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "createSecondManger", "name": "选择审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}