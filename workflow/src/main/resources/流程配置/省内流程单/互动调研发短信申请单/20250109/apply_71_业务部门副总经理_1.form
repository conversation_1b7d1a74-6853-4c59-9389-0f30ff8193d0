{"key": "apply_71_createSecondManger_1", "name": "业务部门副总经理_1", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "业务部门总经理审核"}], "params": {"setVisible": {"1": ["createFirstManger"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "-1", "postName": "部门总经理"}, "itemId": "createFirstManger", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "createFirstManger", "name": "选择审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}