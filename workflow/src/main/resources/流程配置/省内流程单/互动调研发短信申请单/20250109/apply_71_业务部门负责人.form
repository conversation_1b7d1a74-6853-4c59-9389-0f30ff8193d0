{"key": "apply_71_createLower", "name": "业务部门负责人", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "业务部门三级经理审核"}], "params": {"setVisible": {"1": ["createManger3"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "-1", "postName": "三级经理"}, "itemId": "createManger3", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "createManger3", "name": "选择审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}