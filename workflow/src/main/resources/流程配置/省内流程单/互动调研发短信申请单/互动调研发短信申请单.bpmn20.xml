<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="apply_71" name="互动调研发短信申请单" isExecutable="true">
    <documentation>新增申请人发送短信节点</documentation>
    <serviceTask id="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F" name="回复外部系统" flowable:class="com.asiainfo.sound.service.flow.OtherSystemReply"></serviceTask>
    <startEvent id="sid-AB4B3E2C-680E-4808-BFD0-E392853588E5" flowable:formKey="apply_71" flowable:formFieldValidation="true"></startEvent>
    <endEvent id="sid-1BFF381C-5203-4C37-9608-E3F3DD1376A6"></endEvent>
    <userTask id="sid-1FE2D3D9-8471-4495-993E-CA64BE22C86E" name="品质管理部负责人" flowable:assignee="${userId}" flowable:formKey="apply_71_qualityLower" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <userTask id="sid-CC422C99-B1C1-4B60-AE59-B0B0C42E7C72" name="品质管理部三级经理" flowable:assignee="${qualityManger3}" flowable:formKey="apply_71_qualityManger3" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A" name="品质管理部二级经理" flowable:assignee="${qualityManger2}" flowable:formKey="apply_71_qualityManger2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-261C88EF-DFCA-4881-975D-93AD6EF77BF4" sourceRef="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F" targetRef="sid-1BFF381C-5203-4C37-9608-E3F3DD1376A6"></sequenceFlow>
    <exclusiveGateway id="sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64"></exclusiveGateway>
    <sequenceFlow id="sid-B5A069E8-8A34-4AD1-80DF-02CBC5E0D1AC" sourceRef="sid-CC422C99-B1C1-4B60-AE59-B0B0C42E7C72" targetRef="sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64"></sequenceFlow>
    <exclusiveGateway id="sid-2584052C-917F-42AF-A959-2EC45A1B2C51"></exclusiveGateway>
    <sequenceFlow id="sid-0D52329E-7FE6-4C0A-9A78-67D232BFB4EA" sourceRef="sid-AB4B3E2C-680E-4808-BFD0-E392853588E5" targetRef="sid-2584052C-917F-42AF-A959-2EC45A1B2C51"></sequenceFlow>
    <userTask id="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67" name="品质管理部负责人" flowable:assignee="${qualityLower}" flowable:formKey="apply_71_qualityLower" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8" name="品质管理部三级经理" flowable:assignee="${qualityManger3}" flowable:formKey="apply_71_qualityManger3" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-72D9EAA7-E6BD-4934-8943-080FA1025891" name="品质管理部二级经理" flowable:assignee="${qualityManger2}" flowable:formKey="apply_71_qualityManger2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA" name="业务部门负责人" flowable:assignee="${userId}" flowable:formKey="apply_71_createLower" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <userTask id="sid-45C26A73-4ECC-4A74-841E-6BBCCCE9DDD0" name="业务部门三级经理" flowable:assignee="${createManger3}" flowable:formKey="apply_71_createManger3_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-6D931008-D140-4BA8-8D12-3617FE27EA18" name="业务部门副总经理" flowable:assignee="${createSecondManger}" flowable:formKey="apply_71_createSecondManger_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-605C3FDB-7AA3-42C7-83DF-1CCB76548600" name="业务部门总经理" flowable:assignee="${createFirstManger}" flowable:formKey="apply_71_createFirstManger" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-157E123C-690D-4527-906B-EA645451A861" sourceRef="sid-605C3FDB-7AA3-42C7-83DF-1CCB76548600" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <userTask id="sid-68FA8176-0950-4E7E-9020-2A9896D246E1" name="地市公司副总经理" flowable:assignee="${citySecondManger}" flowable:formKey="apply_71_citySecondManger_2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-BB0E203E-0DBD-4C9D-9A68-3FC3FA939BE2" name="市场部三级经理" flowable:assignee="${cityThirdManger}" flowable:formKey="apply_71_marketManger3_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-B27BF76B-9C0E-4351-AAC6-8736D605EAA5" name="市场部三级经理" flowable:assignee="${cityThirdManger}" flowable:formKey="apply_71_marketManger3_2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-7D2249AE-849E-436F-899B-0EE14573567C" name="地市公司总经理" flowable:assignee="${cityFirstManger}" flowable:formKey="apply_71_cityFirstManger" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-F64E6B15-FCFD-46F7-9EF1-CFABFE8CB258" name="地市公司副总经理" flowable:assignee="${citySecondManger}" flowable:formKey="apply_71_citySecondManger_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-4F906922-6BDD-4880-A5B0-C36FAD459C31" name="市场部三级经理" flowable:assignee="${cityThirdManger}" flowable:formKey="apply_71_marketManger3_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-530A153B-9C2B-4695-A928-EAB7FB999BAD" name="业务部门三级经理" flowable:assignee="${createManger3}" flowable:formKey="apply_71_createManger3_1" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-B8DB66CC-038A-4FFE-9727-F5FE5E8C12D0" name="业务部门副总经理" flowable:assignee="${createSecondManger}" flowable:formKey="apply_71_createSecondManger_2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-A8173258-A0EC-4BF5-BC72-C90BFDD0D926" name="业务部门三级经理" flowable:assignee="${createManger3}" flowable:formKey="apply_71_createManger3_2" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-A85F1CE7-4907-44AB-B590-1324A3113B38" sourceRef="sid-6D931008-D140-4BA8-8D12-3617FE27EA18" targetRef="sid-605C3FDB-7AA3-42C7-83DF-1CCB76548600"></sequenceFlow>
    <sequenceFlow id="sid-2636A30F-4B9E-4DF4-9262-E052AA120AF9" sourceRef="sid-530A153B-9C2B-4695-A928-EAB7FB999BAD" targetRef="sid-B8DB66CC-038A-4FFE-9727-F5FE5E8C12D0"></sequenceFlow>
    <sequenceFlow id="sid-0DC35924-D598-44DC-8486-209E80B94476" sourceRef="sid-45C26A73-4ECC-4A74-841E-6BBCCCE9DDD0" targetRef="sid-6D931008-D140-4BA8-8D12-3617FE27EA18"></sequenceFlow>
    <sequenceFlow id="sid-DC2CCE42-B63D-438C-B59E-EBD46EF1E4E7" sourceRef="sid-4F906922-6BDD-4880-A5B0-C36FAD459C31" targetRef="sid-F64E6B15-FCFD-46F7-9EF1-CFABFE8CB258"></sequenceFlow>
    <sequenceFlow id="sid-1103D92B-A70B-4AED-9C99-2E053691FDB6" sourceRef="sid-F64E6B15-FCFD-46F7-9EF1-CFABFE8CB258" targetRef="sid-7D2249AE-849E-436F-899B-0EE14573567C"></sequenceFlow>
    <sequenceFlow id="sid-12BED5D1-9BC4-4181-848A-0146B3845A75" sourceRef="sid-BB0E203E-0DBD-4C9D-9A68-3FC3FA939BE2" targetRef="sid-68FA8176-0950-4E7E-9020-2A9896D246E1"></sequenceFlow>
    <sequenceFlow id="sid-C6C75D14-C8A3-49E8-9233-E1AF1BAD5BDE" sourceRef="sid-A8173258-A0EC-4BF5-BC72-C90BFDD0D926" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <sequenceFlow id="sid-18AB76FB-EF31-4AC3-9DF9-092EA3BF15F5" sourceRef="sid-B27BF76B-9C0E-4351-AAC6-8736D605EAA5" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <sequenceFlow id="sid-85D58A06-D5A5-46B2-905E-12EB8253F684" sourceRef="sid-7D2249AE-849E-436F-899B-0EE14573567C" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <sequenceFlow id="sid-2B090DC3-E9D5-4B89-8185-5E60A6A16E0C" sourceRef="sid-1FE2D3D9-8471-4495-993E-CA64BE22C86E" targetRef="sid-CC422C99-B1C1-4B60-AE59-B0B0C42E7C72"></sequenceFlow>
    <sequenceFlow id="sid-EC116D79-4AAE-485E-9171-FA00D60EE1A5" sourceRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67" targetRef="sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8"></sequenceFlow>
    <sequenceFlow id="sid-106F1309-0A4D-4A48-8A88-8B79F0BDF8F3" sourceRef="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA" targetRef="sid-530A153B-9C2B-4695-A928-EAB7FB999BAD">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number>=50000&& number<200000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2920B4A1-886A-451B-B7D4-76CAA1A9EB1A" sourceRef="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA" targetRef="sid-45C26A73-4ECC-4A74-841E-6BBCCCE9DDD0">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number>=200000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-E2A0E9A0-EADF-4471-AD20-92CE433E7C49" sourceRef="sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8" targetRef="sid-72D9EAA7-E6BD-4934-8943-080FA1025891">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number>=50000}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-C8E2917D-840A-47E7-A503-85F83DD69725" name="地市公司服务管理" flowable:assignee="${userId}" flowable:formKey="apply_71_marketFirst" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="sid-D57E3C2C-EE60-482F-B52A-B5FCF585FE4F" sourceRef="sid-2584052C-917F-42AF-A959-2EC45A1B2C51" targetRef="sid-1FE2D3D9-8471-4495-993E-CA64BE22C86E">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${path==3}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-239858B6-8AA4-4511-8AF3-7354E5D22004" sourceRef="sid-2584052C-917F-42AF-A959-2EC45A1B2C51" targetRef="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${path==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B1182401-86A6-4550-AE0C-FECFBE4D49AB" sourceRef="sid-2584052C-917F-42AF-A959-2EC45A1B2C51" targetRef="sid-C8E2917D-840A-47E7-A503-85F83DD69725">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${path==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-932DEBED-BA5B-475D-8E70-7E06E9CCDE5D" sourceRef="sid-C8E2917D-840A-47E7-A503-85F83DD69725" targetRef="sid-BB0E203E-0DBD-4C9D-9A68-3FC3FA939BE2">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${number>=50000&& number<200000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-45D62D17-202E-4EDE-BD7B-3BCB80BCA4B7" sourceRef="sid-C8E2917D-840A-47E7-A503-85F83DD69725" targetRef="sid-4F906922-6BDD-4880-A5B0-C36FAD459C31">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${number>=200000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-6EC8CB5B-8B32-4E0F-B9EE-852B2390DD0C" sourceRef="sid-C8E2917D-840A-47E7-A503-85F83DD69725" targetRef="sid-B27BF76B-9C0E-4351-AAC6-8736D605EAA5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${number<50000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-AF1CB3C0-8543-443A-BB26-DECFF1D76AE5" sourceRef="sid-B8DB66CC-038A-4FFE-9727-F5FE5E8C12D0" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <sequenceFlow id="sid-7A64DB14-BE53-4917-9D80-0B63FE85FCD4" sourceRef="sid-68FA8176-0950-4E7E-9020-2A9896D246E1" targetRef="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67"></sequenceFlow>
    <userTask id="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80" name="申请人发送短信" flowable:assignee="${userId}" flowable:formKey="apply_71_start_user" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-F0B5A1F3-77E3-44ED-936B-2BF3DBE5CCEE" sourceRef="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA" targetRef="sid-A8173258-A0EC-4BF5-BC72-C90BFDD0D926">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number<50000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-135A7B18-0135-48D5-8A15-0D5DE8650A7F" sourceRef="sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8" targetRef="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number<50000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-20EC6283-8B97-4AE2-A33B-C104A741BD44" sourceRef="sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A" targetRef="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='拒绝'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2A3FC1A2-F75B-4E5E-9C8C-B80A74AE0CE7" sourceRef="sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A" targetRef="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='同意'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-5D708101-7EBE-4BFD-B3B5-B15070B926FE" sourceRef="sid-72D9EAA7-E6BD-4934-8943-080FA1025891" targetRef="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='拒绝'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-E401E057-EFC6-4859-9AD6-44BBA89450A3" sourceRef="sid-72D9EAA7-E6BD-4934-8943-080FA1025891" targetRef="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='同意'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-3749DEEE-2015-43C9-9950-41799C53CBE9" sourceRef="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80" targetRef="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F"></sequenceFlow>
    <sequenceFlow id="sid-96121242-C900-4A2B-8A0C-4120E2BBBA6D" sourceRef="sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64" targetRef="sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number>=50000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-04CA6E73-824C-4EB7-8F5C-16E5934FE3A6" sourceRef="sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64" targetRef="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='1'&& number<50000}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_apply_71">
    <bpmndi:BPMNPlane bpmnElement="apply_71" id="BPMNPlane_apply_71">
      <bpmndi:BPMNShape bpmnElement="sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F" id="BPMNShape_sid-6DC50561-2E23-41BE-B88B-2E7F967F3D2F">
        <omgdc:Bounds height="80.0" width="100.0" x="1215.0" y="690.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-AB4B3E2C-680E-4808-BFD0-E392853588E5" id="BPMNShape_sid-AB4B3E2C-680E-4808-BFD0-E392853588E5">
        <omgdc:Bounds height="30.0" width="30.0" x="28.0" y="565.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1BFF381C-5203-4C37-9608-E3F3DD1376A6" id="BPMNShape_sid-1BFF381C-5203-4C37-9608-E3F3DD1376A6">
        <omgdc:Bounds height="28.0" width="28.0" x="1350.0" y="716.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1FE2D3D9-8471-4495-993E-CA64BE22C86E" id="BPMNShape_sid-1FE2D3D9-8471-4495-993E-CA64BE22C86E">
        <omgdc:Bounds height="80.0" width="100.0" x="313.5" y="690.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-CC422C99-B1C1-4B60-AE59-B0B0C42E7C72" id="BPMNShape_sid-CC422C99-B1C1-4B60-AE59-B0B0C42E7C72">
        <omgdc:Bounds height="80.0" width="100.0" x="463.5" y="690.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A" id="BPMNShape_sid-B5DAE095-61E0-46AE-8849-DDE5EF666D5A">
        <omgdc:Bounds height="80.0" width="100.0" x="706.5" y="690.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64" id="BPMNShape_sid-6EDA9FB9-A375-4E02-8C85-8C6767FE0B64">
        <omgdc:Bounds height="40.0" width="40.0" x="613.5" y="710.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-2584052C-917F-42AF-A959-2EC45A1B2C51" id="BPMNShape_sid-2584052C-917F-42AF-A959-2EC45A1B2C51">
        <omgdc:Bounds height="40.0" width="40.0" x="106.0" y="560.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67" id="BPMNShape_sid-02FCFF56-0EE7-4EBC-BA6B-443ED611AE67">
        <omgdc:Bounds height="80.0" width="100.0" x="765.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8" id="BPMNShape_sid-A4F4DF09-77AF-4553-B737-83EF42D80FC8">
        <omgdc:Bounds height="80.0" width="100.0" x="900.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-72D9EAA7-E6BD-4934-8943-080FA1025891" id="BPMNShape_sid-72D9EAA7-E6BD-4934-8943-080FA1025891">
        <omgdc:Bounds height="80.0" width="100.0" x="1035.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-08E35750-1675-4A96-A888-5A61DA6FB9EA" id="BPMNShape_sid-08E35750-1675-4A96-A888-5A61DA6FB9EA">
        <omgdc:Bounds height="80.0" width="100.0" x="173.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-45C26A73-4ECC-4A74-841E-6BBCCCE9DDD0" id="BPMNShape_sid-45C26A73-4ECC-4A74-841E-6BBCCCE9DDD0">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6D931008-D140-4BA8-8D12-3617FE27EA18" id="BPMNShape_sid-6D931008-D140-4BA8-8D12-3617FE27EA18">
        <omgdc:Bounds height="80.0" width="100.0" x="480.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-605C3FDB-7AA3-42C7-83DF-1CCB76548600" id="BPMNShape_sid-605C3FDB-7AA3-42C7-83DF-1CCB76548600">
        <omgdc:Bounds height="80.0" width="100.0" x="615.0" y="540.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-68FA8176-0950-4E7E-9020-2A9896D246E1" id="BPMNShape_sid-68FA8176-0950-4E7E-9020-2A9896D246E1">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="150.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-BB0E203E-0DBD-4C9D-9A68-3FC3FA939BE2" id="BPMNShape_sid-BB0E203E-0DBD-4C9D-9A68-3FC3FA939BE2">
        <omgdc:Bounds height="80.0" width="100.0" x="145.0" y="150.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B27BF76B-9C0E-4351-AAC6-8736D605EAA5" id="BPMNShape_sid-B27BF76B-9C0E-4351-AAC6-8736D605EAA5">
        <omgdc:Bounds height="80.0" width="100.0" x="210.0" y="240.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-7D2249AE-849E-436F-899B-0EE14573567C" id="BPMNShape_sid-7D2249AE-849E-436F-899B-0EE14573567C">
        <omgdc:Bounds height="80.0" width="100.0" x="465.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F64E6B15-FCFD-46F7-9EF1-CFABFE8CB258" id="BPMNShape_sid-F64E6B15-FCFD-46F7-9EF1-CFABFE8CB258">
        <omgdc:Bounds height="80.0" width="100.0" x="245.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-4F906922-6BDD-4880-A5B0-C36FAD459C31" id="BPMNShape_sid-4F906922-6BDD-4880-A5B0-C36FAD459C31">
        <omgdc:Bounds height="80.0" width="100.0" x="76.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-530A153B-9C2B-4695-A928-EAB7FB999BAD" id="BPMNShape_sid-530A153B-9C2B-4695-A928-EAB7FB999BAD">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="435.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B8DB66CC-038A-4FFE-9727-F5FE5E8C12D0" id="BPMNShape_sid-B8DB66CC-038A-4FFE-9727-F5FE5E8C12D0">
        <omgdc:Bounds height="80.0" width="100.0" x="480.0" y="435.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A8173258-A0EC-4BF5-BC72-C90BFDD0D926" id="BPMNShape_sid-A8173258-A0EC-4BF5-BC72-C90BFDD0D926">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="330.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C8E2917D-840A-47E7-A503-85F83DD69725" id="BPMNShape_sid-C8E2917D-840A-47E7-A503-85F83DD69725">
        <omgdc:Bounds height="80.0" width="100.0" x="76.0" y="345.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80" id="BPMNShape_sid-AF8D187F-365F-4FF2-82C7-C57AEE94BB80">
        <omgdc:Bounds height="80.0" width="100.0" x="1035.0" y="690.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-2920B4A1-886A-451B-B7D4-76CAA1A9EB1A" id="BPMNEdge_sid-2920B4A1-886A-451B-B7D4-76CAA1A9EB1A" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="272.9499999998496" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2636A30F-4B9E-4DF4-9262-E052AA120AF9" id="BPMNEdge_sid-2636A30F-4B9E-4DF4-9262-E052AA120AF9" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="429.95000000000005" y="475.0"></omgdi:waypoint>
        <omgdi:waypoint x="480.0" y="475.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E2A0E9A0-EADF-4471-AD20-92CE433E7C49" id="BPMNEdge_sid-E2A0E9A0-EADF-4471-AD20-92CE433E7C49" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="999.9499999999327" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="1035.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D57E3C2C-EE60-482F-B52A-B5FCF585FE4F" id="BPMNEdge_sid-D57E3C2C-EE60-482F-B52A-B5FCF585FE4F" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="126.0" y="599.9433544303797"></omgdi:waypoint>
        <omgdi:waypoint x="126.0" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="313.5" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-239858B6-8AA4-4511-8AF3-7354E5D22004" id="BPMNEdge_sid-239858B6-8AA4-4511-8AF3-7354E5D22004" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="145.93972708547886" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="173.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-261C88EF-DFCA-4881-975D-93AD6EF77BF4" id="BPMNEdge_sid-261C88EF-DFCA-4881-975D-93AD6EF77BF4" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1314.9499999998811" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="1350.0" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3749DEEE-2015-43C9-9950-41799C53CBE9" id="BPMNEdge_sid-3749DEEE-2015-43C9-9950-41799C53CBE9" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1134.9499999999998" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="1215.0" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EC116D79-4AAE-485E-9171-FA00D60EE1A5" id="BPMNEdge_sid-EC116D79-4AAE-485E-9171-FA00D60EE1A5" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="864.9499999999327" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="900.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-85D58A06-D5A5-46B2-905E-12EB8253F684" id="BPMNEdge_sid-85D58A06-D5A5-46B2-905E-12EB8253F684" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="564.95" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="815.0" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="815.0" y="540.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B5A069E8-8A34-4AD1-80DF-02CBC5E0D1AC" id="BPMNEdge_sid-B5A069E8-8A34-4AD1-80DF-02CBC5E0D1AC" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="563.45" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="613.5" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-135A7B18-0135-48D5-8A15-0D5DE8650A7F" id="BPMNEdge_sid-135A7B18-0135-48D5-8A15-0D5DE8650A7F" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="25.0" flowable:targetDockerY="1.0">
        <omgdi:waypoint x="989.59009009009" y="619.9499999999999"></omgdi:waypoint>
        <omgdi:waypoint x="1059.009009009009" y="690.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-12BED5D1-9BC4-4181-848A-0146B3845A75" id="BPMNEdge_sid-12BED5D1-9BC4-4181-848A-0146B3845A75" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="244.94999999986192" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="345.0" y="190.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7A64DB14-BE53-4917-9D80-0B63FE85FCD4" id="BPMNEdge_sid-7A64DB14-BE53-4917-9D80-0B63FE85FCD4" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="444.95000000000005" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="726.0" y="190.0"></omgdi:waypoint>
        <omgdi:waypoint x="805.8717948717949" y="540.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2A3FC1A2-F75B-4E5E-9C8C-B80A74AE0CE7" id="BPMNEdge_sid-2A3FC1A2-F75B-4E5E-9C8C-B80A74AE0CE7" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="1.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="806.4499999994337" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="1034.999999999882" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-932DEBED-BA5B-475D-8E70-7E06E9CCDE5D" id="BPMNEdge_sid-932DEBED-BA5B-475D-8E70-7E06E9CCDE5D" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="140.13615384615383" y="345.0"></omgdi:waypoint>
        <omgdi:waypoint x="180.84615384615384" y="229.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5D708101-7EBE-4BFD-B3B5-B15070B926FE" id="BPMNEdge_sid-5D708101-7EBE-4BFD-B3B5-B15070B926FE" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="1.0">
        <omgdi:waypoint x="1134.9499999999998" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="1265.0" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="1265.0" y="690.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-45D62D17-202E-4EDE-BD7B-3BCB80BCA4B7" id="BPMNEdge_sid-45D62D17-202E-4EDE-BD7B-3BCB80BCA4B7" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="126.0" y="345.0"></omgdi:waypoint>
        <omgdi:waypoint x="126.0" y="109.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-157E123C-690D-4527-906B-EA645451A861" id="BPMNEdge_sid-157E123C-690D-4527-906B-EA645451A861" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="714.9499999997589" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="765.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-DC2CCE42-B63D-438C-B59E-EBD46EF1E4E7" id="BPMNEdge_sid-DC2CCE42-B63D-438C-B59E-EBD46EF1E4E7" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="175.94999999999607" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="245.0" y="70.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-20EC6283-8B97-4AE2-A33B-C104A741BD44" id="BPMNEdge_sid-20EC6283-8B97-4AE2-A33B-C104A741BD44" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="756.5" y="769.9499999999999"></omgdi:waypoint>
        <omgdi:waypoint x="756.5" y="830.5"></omgdi:waypoint>
        <omgdi:waypoint x="1265.0" y="830.5"></omgdi:waypoint>
        <omgdi:waypoint x="1265.0" y="769.9499999999999"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-04CA6E73-824C-4EB7-8F5C-16E5934FE3A6" id="BPMNEdge_sid-04CA6E73-824C-4EB7-8F5C-16E5934FE3A6" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="1.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="633.5" y="710.0"></omgdi:waypoint>
        <omgdi:waypoint x="633.5" y="656.5"></omgdi:waypoint>
        <omgdi:waypoint x="884.0" y="656.5"></omgdi:waypoint>
        <omgdi:waypoint x="1035.0" y="709.6480263157895"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1103D92B-A70B-4AED-9C99-2E053691FDB6" id="BPMNEdge_sid-1103D92B-A70B-4AED-9C99-2E053691FDB6" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="344.94999999999425" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="465.0" y="70.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C6C75D14-C8A3-49E8-9233-E1AF1BAD5BDE" id="BPMNEdge_sid-C6C75D14-C8A3-49E8-9233-E1AF1BAD5BDE" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="429.95000000000005" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="658.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="785.0952380952381" y="540.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6EC8CB5B-8B32-4E0F-B9EE-852B2390DD0C" id="BPMNEdge_sid-6EC8CB5B-8B32-4E0F-B9EE-852B2390DD0C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="1.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="158.34047619047618" y="345.0"></omgdi:waypoint>
        <omgdi:waypoint x="210.0" y="281.1735294117647"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-18AB76FB-EF31-4AC3-9DF9-092EA3BF15F5" id="BPMNEdge_sid-18AB76FB-EF31-4AC3-9DF9-092EA3BF15F5" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="309.94999999982656" y="280.0"></omgdi:waypoint>
        <omgdi:waypoint x="675.0" y="280.0"></omgdi:waypoint>
        <omgdi:waypoint x="796.3333333333334" y="540.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-106F1309-0A4D-4A48-8A88-8B79F0BDF8F3" id="BPMNEdge_sid-106F1309-0A4D-4A48-8A88-8B79F0BDF8F3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="272.95" y="546.5605095541401"></omgdi:waypoint>
        <omgdi:waypoint x="329.9999999999999" y="508.40605095541406"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E401E057-EFC6-4859-9AD6-44BBA89450A3" id="BPMNEdge_sid-E401E057-EFC6-4859-9AD6-44BBA89450A3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="1.0">
        <omgdi:waypoint x="1085.0" y="619.9499999999999"></omgdi:waypoint>
        <omgdi:waypoint x="1085.0" y="690.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-96121242-C900-4A2B-8A0C-4120E2BBBA6D" id="BPMNEdge_sid-96121242-C900-4A2B-8A0C-4120E2BBBA6D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="653.4418968318439" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="706.5" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0D52329E-7FE6-4C0A-9A78-67D232BFB4EA" id="BPMNEdge_sid-0D52329E-7FE6-4C0A-9A78-67D232BFB4EA" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="57.94999734852612" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="106.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A85F1CE7-4907-44AB-B590-1324A3113B38" id="BPMNEdge_sid-A85F1CE7-4907-44AB-B590-1324A3113B38" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="579.9499999999327" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="615.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AF1CB3C0-8543-443A-BB26-DECFF1D76AE5" id="BPMNEdge_sid-AF1CB3C0-8543-443A-BB26-DECFF1D76AE5" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="579.949999999978" y="475.0"></omgdi:waypoint>
        <omgdi:waypoint x="660.0" y="475.0"></omgdi:waypoint>
        <omgdi:waypoint x="765.0" y="546.1290322580645"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2B090DC3-E9D5-4B89-8185-5E60A6A16E0C" id="BPMNEdge_sid-2B090DC3-E9D5-4B89-8185-5E60A6A16E0C" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="413.45000000000005" y="730.0"></omgdi:waypoint>
        <omgdi:waypoint x="463.5" y="730.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B1182401-86A6-4550-AE0C-FECFBE4D49AB" id="BPMNEdge_sid-B1182401-86A6-4550-AE0C-FECFBE4D49AB" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="126.0" y="560.0"></omgdi:waypoint>
        <omgdi:waypoint x="126.0" y="424.95000000000005"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0DC35924-D598-44DC-8486-209E80B94476" id="BPMNEdge_sid-0DC35924-D598-44DC-8486-209E80B94476" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="429.949999999759" y="580.0"></omgdi:waypoint>
        <omgdi:waypoint x="480.0" y="580.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F0B5A1F3-77E3-44ED-936B-2BF3DBE5CCEE" id="BPMNEdge_sid-F0B5A1F3-77E3-44ED-936B-2BF3DBE5CCEE" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="1.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="243.54571428571433" y="540.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.0" y="371.8472222222223"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>