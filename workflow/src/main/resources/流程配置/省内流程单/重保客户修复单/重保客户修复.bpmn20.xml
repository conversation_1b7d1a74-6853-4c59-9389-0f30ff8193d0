<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="repair_99" name="重保客户修复" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="repair_99" flowable:formFieldValidation="true"></startEvent>
    <exclusiveGateway id="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></exclusiveGateway>
    <userTask id="sid-6B165718-1AD4-4190-B566-24321FC132F5" name="地市接单人处理" flowable:assignee="${areaHandler}" flowable:formKey="repair_99_地市接单人处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachmentHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" name="市场部审核处理" flowable:assignee="wanglimin" flowable:formKey="repair_99_市场部审核处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" name="结束"></endEvent>
    <userTask id="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" name="品质管理部归档" flowable:assignee="matingting" flowable:formKey="repair_99_品质管理部归档" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachListDataHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <serviceTask id="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <sequenceFlow id="sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" sourceRef="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" targetRef="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1"></sequenceFlow>
    <sequenceFlow id="sid-919DD768-FCE4-4F5C-8186-359284053719" sourceRef="startEvent1" targetRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></sequenceFlow>
    <sequenceFlow id="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${unsatisfiedReason=='市场部'||unsatisfiedReason=='其他'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${unsatisfiedReason=='网络部'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14"></exclusiveGateway>
    <sequenceFlow id="sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" sourceRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" targetRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14"></sequenceFlow>
    <sequenceFlow id="sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" sourceRef="sid-6B165718-1AD4-4190-B566-24321FC132F5" targetRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1"></sequenceFlow>
    <exclusiveGateway id="sid-E3321540-404F-4068-A50A-5C18A01EDBF0"></exclusiveGateway>
    <sequenceFlow id="sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" sourceRef="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" targetRef="sid-E3321540-404F-4068-A50A-5C18A01EDBF0"></sequenceFlow>
    <sequenceFlow id="sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" sourceRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" sourceRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" targetRef="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" sourceRef="sid-E3321540-404F-4068-A50A-5C18A01EDBF0" targetRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" sourceRef="sid-E3321540-404F-4068-A50A-5C18A01EDBF0" targetRef="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='省内归档'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_repair_99">
    <bpmndi:BPMNPlane bpmnElement="repair_99" id="BPMNPlane_repair_99">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="45.0" y="190.99998876452497"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" id="BPMNShape_sid-E9C01A12-B666-4A87-82F6-E7BA26030892">
        <omgdc:Bounds height="40.0" width="40.0" x="105.0" y="185.99998876452497"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6B165718-1AD4-4190-B566-24321FC132F5" id="BPMNShape_sid-6B165718-1AD4-4190-B566-24321FC132F5">
        <omgdc:Bounds height="79.99999999999999" width="100.0" x="210.0" y="29.999998211860714"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" id="BPMNShape_sid-C78C18CA-4894-4861-922B-BCB0F98600E1">
        <omgdc:Bounds height="80.00000000000001" width="100.0" x="465.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" id="BPMNShape_sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1">
        <omgdc:Bounds height="28.0" width="28.0" x="575.999950647357" y="295.99994134903636"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" id="BPMNShape_sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF">
        <omgdc:Bounds height="79.99999999999997" width="100.0" x="855.0" y="29.999998211860728"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" id="BPMNShape_sid-FF12C778-5454-440A-8ABB-FA8D98E4E786">
        <omgdc:Bounds height="80.00000000000006" width="100.00000000000011" x="855.0" y="269.99994134903636"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" id="BPMNShape_sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14">
        <omgdc:Bounds height="40.0" width="40.0" x="690.0" y="50.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E3321540-404F-4068-A50A-5C18A01EDBF0" id="BPMNShape_sid-E3321540-404F-4068-A50A-5C18A01EDBF0">
        <omgdc:Bounds height="40.0" width="40.0" x="1010.5" y="187.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" id="BPMNEdge_sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" flowable:sourceDockerX="50.00000000000006" flowable:sourceDockerY="40.00000000000003" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="855.0" y="309.99994134903636"></omgdi:waypoint>
        <omgdi:waypoint x="603.949870060738" y="309.99994134903636"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" id="BPMNEdge_sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.999999999999986" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="941.5965323690533" y="109.9499982118607"></omgdi:waypoint>
        <omgdi:waypoint x="1020.9380953032305" y="196.55988188429626"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" id="BPMNEdge_sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="729.9448874201139" y="69.99999981660109"></omgdi:waypoint>
        <omgdi:waypoint x="854.9999998410543" y="69.99999866989945"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" id="BPMNEdge_sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="125.0" y="225.9403136928345"></omgdi:waypoint>
        <omgdi:waypoint x="125.0" y="309.0"></omgdi:waypoint>
        <omgdi:waypoint x="575.999950647357" y="309.96983565304504"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" id="BPMNEdge_sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="710.0" y="89.94221311475408"></omgdi:waypoint>
        <omgdi:waypoint x="710.0" y="198.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="198.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="109.9499982118607"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" id="BPMNEdge_sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.00000000000001" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="564.9499999999944" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="690.0" y="70.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" id="BPMNEdge_sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.99999999999999" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="309.9499993705749" y="69.99999856212564"></omgdi:waypoint>
        <omgdi:waypoint x="464.9999993642171" y="69.99999964938445"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-919DD768-FCE4-4F5C-8186-359284053719" id="BPMNEdge_sid-919DD768-FCE4-4F5C-8186-359284053719" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="74.949995678156" y="205.99998876452497"></omgdi:waypoint>
        <omgdi:waypoint x="105.0" y="205.99998876452497"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" id="BPMNEdge_sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="1014.6983445738811" y="202.8007662835249"></omgdi:waypoint>
        <omgdi:waypoint x="564.95" y="83.27478176527643"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" id="BPMNEdge_sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.00000000000006" flowable:targetDockerY="40.00000000000003">
        <omgdi:waypoint x="1019.5153144671231" y="215.99277618095522"></omgdi:waypoint>
        <omgdi:waypoint x="953.6769694655453" y="269.99994134903636"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" id="BPMNEdge_sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="125.0" y="185.99998876452497"></omgdi:waypoint>
        <omgdi:waypoint x="125.0" y="69.0"></omgdi:waypoint>
        <omgdi:waypoint x="209.99999999999991" y="69.62962850376415"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>