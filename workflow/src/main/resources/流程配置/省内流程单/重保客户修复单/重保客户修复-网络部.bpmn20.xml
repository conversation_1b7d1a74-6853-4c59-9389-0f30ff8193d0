<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="repair_99" name="重保客户修复" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="repair_99" flowable:formFieldValidation="true"></startEvent>
    <exclusiveGateway id="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></exclusiveGateway>
    <userTask id="sid-6B165718-1AD4-4190-B566-24321FC132F5" name="地市接单人处理" flowable:assignee="${areaHandler}" flowable:formKey="repair_99_地市接单人处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachmentHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" name="市场部审核处理" flowable:assignee="wanglimin" flowable:formKey="repair_99_市场部审核处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" name="结束"></endEvent>
    <userTask id="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" name="品质管理部归档" flowable:assignee="matingting" flowable:formKey="repair_99_品质管理部归档" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachListDataHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <serviceTask id="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" name="省内归档" flowable:class="com.asiainfo.sound.service.flow.IdentyTaskStatementHandle"></serviceTask>
    <sequenceFlow id="sid-919DD768-FCE4-4F5C-8186-359284053719" sourceRef="startEvent1" targetRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892"></sequenceFlow>
    <exclusiveGateway id="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14"></exclusiveGateway>
    <sequenceFlow id="sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" sourceRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" targetRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14"></sequenceFlow>
    <exclusiveGateway id="sid-********-404F-4068-A50A-5C18A01EDBF0"></exclusiveGateway>
    <sequenceFlow id="sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" sourceRef="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" targetRef="sid-********-404F-4068-A50A-5C18A01EDBF0"></sequenceFlow>
    <sequenceFlow id="sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" sourceRef="sid-********-404F-4068-A50A-5C18A01EDBF0" targetRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8" name="网络部审核处理" flowable:assignee="pq_liujie" flowable:formKey="repair_99_市场部审核处理" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE" name="地市接单人处理" flowable:assignee="${areaHandler}" flowable:formKey="repair_99_地市接单人处理2" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachmentHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" sourceRef="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" targetRef="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1"></sequenceFlow>
    <exclusiveGateway id="sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB"></exclusiveGateway>
    <userTask id="sid-6BFF6977-CF1C-491E-989A-AC78E5CF9FDB" name="品质管理部归档" flowable:assignee="matingting" flowable:formKey="repair_99_品质管理部归档" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.AttachListDataHandleEvent"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5"></exclusiveGateway>
    <sequenceFlow id="sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" sourceRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" sourceRef="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" targetRef="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" sourceRef="sid-********-404F-4068-A50A-5C18A01EDBF0" targetRef="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='省内归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-1F4D2A27-1E80-4F62-977F-732AD20E2BCD" sourceRef="sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5" targetRef="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='省内归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" sourceRef="sid-6B165718-1AD4-4190-B566-24321FC132F5" targetRef="sid-C78C18CA-4894-4861-922B-BCB0F98600E1"></sequenceFlow>
    <sequenceFlow id="sid-7E21C724-A423-498D-943B-B3BA0035AD73" sourceRef="sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE" targetRef="sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8"></sequenceFlow>
    <sequenceFlow id="sid-AAB2B74A-F541-4B8A-8620-1BD8DA7FB15A" sourceRef="sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8" targetRef="sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB"></sequenceFlow>
    <sequenceFlow id="sid-7F5017D0-E2D3-401F-84B7-A79CAA2A75F3" sourceRef="sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB" targetRef="sid-6BFF6977-CF1C-491E-989A-AC78E5CF9FDB">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='品质管理部归档'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-AF202EC1-97C3-4CCC-8D9A-FDAD794719C6" sourceRef="sid-6BFF6977-CF1C-491E-989A-AC78E5CF9FDB" targetRef="sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5"></sequenceFlow>
    <sequenceFlow id="sid-C963459B-AD99-4C9C-A370-1BD7BFC905F6" sourceRef="sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB" targetRef="sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-6B165718-1AD4-4190-B566-24321FC132F5">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${unsatisfiedReason=='市场部'||unsatisfiedReason=='其他'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-1C7F3B8B-BF5C-4F17-9163-52D5FEA7DAA1" sourceRef="sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5" targetRef="sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${acceptType=='驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" sourceRef="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" targetRef="sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${unsatisfiedReason=='网络部'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_repair_99">
    <bpmndi:BPMNPlane bpmnElement="repair_99" id="BPMNPlane_repair_99">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="45.0" y="190.99998876452497"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E9C01A12-B666-4A87-82F6-E7BA26030892" id="BPMNShape_sid-E9C01A12-B666-4A87-82F6-E7BA26030892">
        <omgdc:Bounds height="40.0" width="40.0" x="105.0" y="185.99998876452497"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6B165718-1AD4-4190-B566-24321FC132F5" id="BPMNShape_sid-6B165718-1AD4-4190-B566-24321FC132F5">
        <omgdc:Bounds height="79.99999999999999" width="100.0" x="210.0" y="29.999998211860714"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C78C18CA-4894-4861-922B-BCB0F98600E1" id="BPMNShape_sid-C78C18CA-4894-4861-922B-BCB0F98600E1">
        <omgdc:Bounds height="80.00000000000001" width="100.0" x="465.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1" id="BPMNShape_sid-54BE71EC-F774-45DE-9D7E-020B31ABCDF1">
        <omgdc:Bounds height="28.0" width="28.0" x="555.0" y="251.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF" id="BPMNShape_sid-05F965CA-A962-4E93-AA32-8BF33C6BE6CF">
        <omgdc:Bounds height="79.99999999999997" width="100.0" x="855.0" y="29.999998211860728"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-FF12C778-5454-440A-8ABB-FA8D98E4E786" id="BPMNShape_sid-FF12C778-5454-440A-8ABB-FA8D98E4E786">
        <omgdc:Bounds height="80.00000000000006" width="100.0" x="855.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14" id="BPMNShape_sid-B6D87A7B-2DC1-45E0-A5E5-4F5A7D6C1E14">
        <omgdc:Bounds height="40.0" width="40.0" x="690.0" y="50.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-********-404F-4068-A50A-5C18A01EDBF0" id="BPMNShape_sid-********-404F-4068-A50A-5C18A01EDBF0">
        <omgdc:Bounds height="40.0" width="40.0" x="1010.5" y="187.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8" id="BPMNShape_sid-C0F8DAF5-BEDA-45FD-A235-3386F4E29BF8">
        <omgdc:Bounds height="80.0" width="100.0" x="465.0" y="390.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE" id="BPMNShape_sid-67986CA3-4571-4EC4-86A9-C0B822E06DAE">
        <omgdc:Bounds height="80.0" width="100.0" x="210.0" y="390.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB" id="BPMNShape_sid-2F6E60B6-3839-41B6-ADBC-5F6115DAE0FB">
        <omgdc:Bounds height="40.0" width="40.0" x="660.0" y="410.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6BFF6977-CF1C-491E-989A-AC78E5CF9FDB" id="BPMNShape_sid-6BFF6977-CF1C-491E-989A-AC78E5CF9FDB">
        <omgdc:Bounds height="80.0" width="100.0" x="855.0" y="390.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5" id="BPMNShape_sid-B208EC11-A93F-4B2D-AFF4-712D19EF70C5">
        <omgdc:Bounds height="40.0" width="40.0" x="1010.5" y="300.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-1C7F3B8B-BF5C-4F17-9163-52D5FEA7DAA1" id="BPMNEdge_sid-1C7F3B8B-BF5C-4F17-9163-52D5FEA7DAA1" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1014.5011990407672" y="323.9924460431655"></omgdi:waypoint>
        <omgdi:waypoint x="564.95" y="419.389534883721"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AAB2B74A-F541-4B8A-8620-1BD8DA7FB15A" id="BPMNEdge_sid-AAB2B74A-F541-4B8A-8620-1BD8DA7FB15A" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="564.95" y="430.0"></omgdi:waypoint>
        <omgdi:waypoint x="660.0" y="430.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" id="BPMNEdge_sid-EA1E0A7F-F8BE-40FE-8058-BA674EB4B145" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="1.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="125.0" y="225.94553762686328"></omgdi:waypoint>
        <omgdi:waypoint x="125.0" y="430.0"></omgdi:waypoint>
        <omgdi:waypoint x="209.99999999997954" y="430.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" id="BPMNEdge_sid-9761AFEA-09F2-4D5E-8674-29B2AB4B04FC" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.00000000000001" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="564.9499999999944" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="690.0" y="70.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" id="BPMNEdge_sid-EBF208AD-1D60-4DC6-9E03-E8708712CABA" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000003">
        <omgdi:waypoint x="1016.8215258855586" y="213.30572207084472"></omgdi:waypoint>
        <omgdi:waypoint x="954.9499999999998" y="241.89243027888446"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7E21C724-A423-498D-943B-B3BA0035AD73" id="BPMNEdge_sid-7E21C724-A423-498D-943B-B3BA0035AD73" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="309.9499999999562" y="430.0"></omgdi:waypoint>
        <omgdi:waypoint x="464.9999999998755" y="430.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7F5017D0-E2D3-401F-84B7-A79CAA2A75F3" id="BPMNEdge_sid-7F5017D0-E2D3-401F-84B7-A79CAA2A75F3" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="699.4880522088274" y="430.4575892857143"></omgdi:waypoint>
        <omgdi:waypoint x="855.0" y="430.11124721603574"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" id="BPMNEdge_sid-2D4D4051-AE4F-48E5-B461-8AFB1EFD460E" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="125.0" y="185.99998876452497"></omgdi:waypoint>
        <omgdi:waypoint x="125.0" y="69.0"></omgdi:waypoint>
        <omgdi:waypoint x="209.99999999999991" y="69.62962850376415"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" id="BPMNEdge_sid-9CA0958B-03D5-44F9-813C-749C9E5A0EFD" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.00000000000003" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="854.9999999998688" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="582.9499189784087" y="265.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" id="BPMNEdge_sid-B68B92BA-2BC0-4F27-9A5D-6035EDC05EBA" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.999999999999986" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="941.5965323690533" y="109.9499982118607"></omgdi:waypoint>
        <omgdi:waypoint x="1020.9380953032305" y="196.55988188429626"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1F4D2A27-1E80-4F62-977F-732AD20E2BCD" id="BPMNEdge_sid-1F4D2A27-1E80-4F62-977F-732AD20E2BCD" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000003">
        <omgdi:waypoint x="1016.4192392502755" y="314.0785123966943"></omgdi:waypoint>
        <omgdi:waypoint x="954.9499999999999" y="287.0017857142857"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C963459B-AD99-4C9C-A370-1BD7BFC905F6" id="BPMNEdge_sid-C963459B-AD99-4C9C-A370-1BD7BFC905F6" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="680.5" y="410.5"></omgdi:waypoint>
        <omgdi:waypoint x="680.5" y="340.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="340.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="390.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" id="BPMNEdge_sid-A027DC48-A504-4B35-8DB4-0E2B1991F0A2" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="729.9448874201139" y="69.99999981660109"></omgdi:waypoint>
        <omgdi:waypoint x="854.9999998410543" y="69.99999866989945"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" id="BPMNEdge_sid-CFB1FA75-2495-4D02-8C65-3DBD1A4E9F38" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="710.0" y="89.94221311475408"></omgdi:waypoint>
        <omgdi:waypoint x="710.0" y="198.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="198.0"></omgdi:waypoint>
        <omgdi:waypoint x="260.0" y="109.9499982118607"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" id="BPMNEdge_sid-3FCCF2C3-C616-4FED-9D84-38EE52C3FA47" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.99999999999999" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="309.9499993705749" y="69.99999856212564"></omgdi:waypoint>
        <omgdi:waypoint x="464.9999993642171" y="69.99999964938445"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-919DD768-FCE4-4F5C-8186-359284053719" id="BPMNEdge_sid-919DD768-FCE4-4F5C-8186-359284053719" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="74.949995678156" y="205.99998876452497"></omgdi:waypoint>
        <omgdi:waypoint x="105.0" y="205.99998876452497"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" id="BPMNEdge_sid-8034B428-DB41-4DB6-99CA-EA9781D0D4A8" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.00000000000001">
        <omgdi:waypoint x="1014.6983445738811" y="202.8007662835249"></omgdi:waypoint>
        <omgdi:waypoint x="564.95" y="83.27478176527643"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AF202EC1-97C3-4CCC-8D9A-FDAD794719C6" id="BPMNEdge_sid-AF202EC1-97C3-4CCC-8D9A-FDAD794719C6" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.99999999999999" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="950.5793181818182" y="390.0"></omgdi:waypoint>
        <omgdi:waypoint x="1019.8418259023355" y="329.31847133757964"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>