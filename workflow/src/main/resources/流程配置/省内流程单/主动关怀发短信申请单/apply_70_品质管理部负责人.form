{"key": "apply_70_step2", "name": "品质管理部负责人", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "品质管理部三级经理审核"}, {"id": "驳回到发起人", "name": "驳回到发起人"}, {"id": "驳回到上一步", "name": "驳回到上一步"}], "params": {"setVisible": {"1": ["step3User"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "品质管理部", "postName": "三级经理"}, "itemId": "step3User", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "step3User", "name": "选择审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}