{"key": "apply_70_step4", "name": "申请部门二级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "品质管理部二级经理审核"}, {"id": "驳回到发起人", "name": "驳回到发起人"}, {"id": "驳回到上一步", "name": "驳回到上一步"}], "params": {"setVisible": {"1": ["step5User"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "品质管理部", "postName": "二级经理"}, "itemId": "step5User", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "step5User", "name": "审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}