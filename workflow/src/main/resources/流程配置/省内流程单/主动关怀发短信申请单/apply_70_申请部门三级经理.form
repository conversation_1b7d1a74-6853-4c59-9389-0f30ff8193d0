{"key": "apply_70_check", "name": "申请部门三级经理", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "品质管理部负责人审核"}, {"id": "驳回到上一步", "name": "驳回到上一步"}], "params": {"setVisible": {"1": ["step2User"]}, "queryApiAndSetResultToComponent": {"1": {"api": "/user/getUsersByPostAndOrg", "method": "post", "param": {"orgName": "品质管理部", "postName": "负责人"}, "itemId": "step2User", "itemParamKey": "options"}}}, "optionsExpression": null}, {"id": "step2User", "name": "选择审核人", "type": "apiDropdown", "required": true, "placeholder": null, "params": {"hidden": true, "order": 1}}]}