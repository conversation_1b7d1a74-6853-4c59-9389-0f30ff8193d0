{"key": "apply_70", "name": "主动关怀发短信申请单", "fields": [{"fieldType": "FormField", "id": "identifier", "name": "工单编号", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "0", "order": 2}}, {"fieldType": "FormField", "id": "batch", "name": "批次", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 3}}, {"fieldType": "FormField", "id": "userCareId", "name": "用户id", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 4}}, {"fieldType": "FormField", "id": "phoneNo", "name": "电话号码", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 5}}, {"fieldType": "FormField", "id": "title", "name": "模板名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 6}}, {"fieldType": "FormField", "id": "content", "name": "短信内容", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 7}}, {"fieldType": "FormField", "id": "code", "name": "客群编码", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 8}}, {"fieldType": "FormField", "id": "name", "name": "客群名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 9}}, {"fieldType": "FormField", "id": "number", "name": "客群数量", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 10}}, {"fieldType": "FormField", "id": "cityName", "name": "地市名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 11}}, {"fieldType": "FormField", "id": "countyName", "name": "区县名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 12}}, {"fieldType": "FormField", "id": "gridName", "name": "网格名称", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 13}}, {"fieldType": "FormField", "id": "phoneNoList", "name": "手机号码集合", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 14}}, {"fieldType": "FormField", "id": "templateId", "name": "模板id", "type": "text", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 15}}, {"fieldType": "FormField", "id": "templateContent", "name": "模板内容", "type": "fullText", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 17, "col": 3}}, {"fieldType": "FormField", "id": "attachList", "name": "附件", "type": "upload", "value": null, "required": false, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"showType": "2", "order": 16, "col": 2}}, {"id": "userNameList", "name": "部门领导审批", "type": "workflowHandlerList", "required": true, "placeholder": "请选择部门领导", "params": {"showType": "1", "userRoleId": "apply_70_accept"}}]}