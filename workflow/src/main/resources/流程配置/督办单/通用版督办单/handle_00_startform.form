{"key": "handle_00_startform", "name": "工单派发", "fields": [{"fieldType": "OptionFormField", "id": "acceptType", "name": "受理类型", "type": "dropdown", "value": "1", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "1", "name": "处理并提交一级领导审批"}], "params": {"setVisible": {"1": ["yjld<PERSON>ser"]}}, "optionsExpression": null}, {"fieldType": "OptionFormField", "id": "yjld<PERSON>ser", "name": "选择一级领导", "type": "dropdown", "value": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "optionType": null, "hasEmptyValue": true, "options": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "余云峰"}], "optionsExpression": null}, {"fieldType": "FormField", "id": "trackType", "name": "是否审核节点", "type": "text", "value": "02", "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true}}, {"fieldType": "OptionFormField", "id": "launchCompany", "name": "发起省专", "type": "codeSelect", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "default": "951"}}, {"fieldType": "OptionFormField", "id": "forwardCompany", "name": "转发省专", "type": "codeSelect", "value": null, "required": true, "readOnly": false, "overrideId": true, "placeholder": null, "layout": null, "params": {"hidden": true, "selectId": "unitCode", "defaultvari": "originUnit"}}]}