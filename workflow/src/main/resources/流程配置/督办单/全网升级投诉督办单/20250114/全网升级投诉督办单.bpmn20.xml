<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.7">
  <process id="handle_02" name="全网升级投诉督办单" isExecutable="true">
    <startEvent id="startEvent1" flowable:formKey="handle_02" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-EA529804-5165-482B-B9C7-09799CC33D82" name="省侧接口人" flowable:assignee="${userId}" flowable:formKey="handle_02_sendform" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="complete" class="com.asiainfo.sound.service.flow.MesgSaveEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${userNameList}" flowable:elementVariable="userId">
        <extensionElements></extensionElements>
        <completionCondition>${nrOfCompletedInstances&gt;=1 }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <serviceTask id="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" name="生成附件" flowable:class="com.asiainfo.sound.service.flow.IdentiferAttachList"></serviceTask>
    <receiveTask id="handle_02_restartFlag" name="集团操作"></receiveTask>
    <endEvent id="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" name="结束"></endEvent>
    <exclusiveGateway id="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></exclusiveGateway>
    <sequenceFlow id="sid-3EC12877-3A7A-486E-BFF8-78A50FAC88A8" sourceRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" targetRef="handle_02_replyFlag"></sequenceFlow>
    <userTask id="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" name="三级领导审批" flowable:assignee="${taskUser}" flowable:formKey="handle_02_check" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="assignment" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <flowable:taskListener event="delete" class="com.asiainfo.sound.service.flow.DyPushEventListener"></flowable:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <receiveTask id="handle_02_replyFlag" name="回复集团"></receiveTask>
    <exclusiveGateway id="sid-3D48F368-3E5C-4723-B951-53A58D040966"></exclusiveGateway>
    <sequenceFlow id="sid-CFF9E99A-CB69-4EAF-B371-C2C39645DBBC" sourceRef="startEvent1" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82"></sequenceFlow>
    <sequenceFlow id="sid-8B36F875-2C5B-4005-A268-0C52A4DEFF2F" sourceRef="handle_02_replyFlag" targetRef="sid-3D48F368-3E5C-4723-B951-53A58D040966"></sequenceFlow>
    <sequenceFlow id="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" sourceRef="handle_02_restartFlag" targetRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE"></sequenceFlow>
    <sequenceFlow id="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2371365D-E51F-4097-B281-F289682364FA" sourceRef="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${restartFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-10A0670F-A864-4D20-9A32-F4D7DE07A8CB" sourceRef="sid-3D48F368-3E5C-4723-B951-53A58D040966" targetRef="handle_02_restartFlag">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" sourceRef="sid-EA529804-5165-482B-B9C7-09799CC33D82" targetRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD"></sequenceFlow>
    <sequenceFlow id="sid-FC6920CE-C596-4ED8-822C-0E190700C1ED" sourceRef="sid-3D48F368-3E5C-4723-B951-53A58D040966" targetRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${replyFlag==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-BE073C85-3FCC-4956-82E0-52A36D847F89" sourceRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" targetRef="sid-EA529804-5165-482B-B9C7-09799CC33D82">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" sourceRef="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" targetRef="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${passFlag==1}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_handle_02">
    <bpmndi:BPMNPlane bpmnElement="handle_02" id="BPMNPlane_handle_02">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="29.999999999999986" x="105.00000000000001" y="145.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EA529804-5165-482B-B9C7-09799CC33D82" id="BPMNShape_sid-EA529804-5165-482B-B9C7-09799CC33D82">
        <omgdc:Bounds height="79.99999999999999" width="100.0" x="315.00000000000006" y="120.00000000000001"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D" id="BPMNShape_sid-668F9E21-ADA1-4671-A97D-4CB05A4A297D">
        <omgdc:Bounds height="80.0" width="100.0" x="630.0000000000002" y="390.00000000000006"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="handle_02_restartFlag" id="BPMNShape_handle_02_restartFlag">
        <omgdc:Bounds height="80.0" width="99.99999999999997" x="195.00000000000003" y="390.00000000000017"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3DB2CA56-0014-4753-80FE-DEF1E9031985" id="BPMNShape_sid-3DB2CA56-0014-4753-80FE-DEF1E9031985">
        <omgdc:Bounds height="28.0" width="27.999999999999986" x="106.00000000000001" y="506.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE" id="BPMNShape_sid-C8CB25E6-07BF-4198-BB26-F57BA4896FAE">
        <omgdc:Bounds height="40.0" width="39.999999999999986" x="100.00000000000001" y="410.00000000000006"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD" id="BPMNShape_sid-1B0B4FFD-B491-4DF0-BBD3-50849B3F67DD">
        <omgdc:Bounds height="79.99999999999999" width="100.0" x="630.0000000000001" y="120.00000000000001"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="handle_02_replyFlag" id="BPMNShape_handle_02_replyFlag">
        <omgdc:Bounds height="80.0" width="99.99999999999994" x="450.00000000000006" y="390.0000000000001"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3D48F368-3E5C-4723-B951-53A58D040966" id="BPMNShape_sid-3D48F368-3E5C-4723-B951-53A58D040966">
        <omgdc:Bounds height="40.0" width="40.0" x="360.00000000000006" y="410.0000000000002"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" id="BPMNEdge_sid-43D4A6A9-A0F4-40F1-BCBA-B9F3E5FB77CF" flowable:sourceDockerX="19.999999999999993" flowable:sourceDockerY="20.0" flowable:targetDockerX="13.999999999999993" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="120.0" y="449.9389289678135"></omgdi:waypoint>
        <omgdi:waypoint x="120.0" y="506.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CFF9E99A-CB69-4EAF-B371-C2C39645DBBC" id="BPMNEdge_sid-CFF9E99A-CB69-4EAF-B371-C2C39645DBBC" flowable:sourceDockerX="14.999999999999993" flowable:sourceDockerY="15.0" flowable:targetDockerX="11.277755737304688" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="134.94995700248612" y="160.46364136112228"></omgdi:waypoint>
        <omgdi:waypoint x="315.00000000000006" y="160.02721513935708"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2371365D-E51F-4097-B281-F289682364FA" id="BPMNEdge_sid-2371365D-E51F-4097-B281-F289682364FA" flowable:sourceDockerX="19.999999999999993" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="129.4907766990291" y="419.51456310679623"></omgdi:waypoint>
        <omgdi:waypoint x="328.70370370370375" y="199.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3EC12877-3A7A-486E-BFF8-78A50FAC88A8" id="BPMNEdge_sid-3EC12877-3A7A-486E-BFF8-78A50FAC88A8" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="49.99999999999997" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="630.0000000000002" y="430.00000000000006"></omgdi:waypoint>
        <omgdi:waypoint x="549.9499999998199" y="430.0000000000001"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62E0A5DD-0B42-4816-85C9-00D26D586116" id="BPMNEdge_sid-62E0A5DD-0B42-4816-85C9-00D26D586116" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.99999999999999" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="414.9499999997753" y="160.0"></omgdi:waypoint>
        <omgdi:waypoint x="630.0000000000001" y="160.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-10A0670F-A864-4D20-9A32-F4D7DE07A8CB" id="BPMNEdge_sid-10A0670F-A864-4D20-9A32-F4D7DE07A8CB" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="49.999999999999986" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="360.00000000000006" y="430.0000000000002"></omgdi:waypoint>
        <omgdi:waypoint x="245.0" y="430.00000000000017"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" id="BPMNEdge_sid-B1B6E4B4-C5E1-40F2-B414-93FDDA8C2AC3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="78.99999999999999" flowable:targetDockerX="50.0" flowable:targetDockerY="1.0">
        <omgdi:waypoint x="680.0000000000001" y="199.95"></omgdi:waypoint>
        <omgdi:waypoint x="680.0000000000002" y="390.00000000000006"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8B36F875-2C5B-4005-A268-0C52A4DEFF2F" id="BPMNEdge_sid-8B36F875-2C5B-4005-A268-0C52A4DEFF2F" flowable:sourceDockerX="49.99999999999997" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="449.9999999998938" y="430.00000000000017"></omgdi:waypoint>
        <omgdi:waypoint x="399.90827773130263" y="430.00000000000017"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" id="BPMNEdge_sid-46C888CA-1E26-4F9E-AAFC-AF4BBA532A6C" flowable:sourceDockerX="49.999999999999986" flowable:sourceDockerY="40.0" flowable:targetDockerX="19.999999999999993" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="182.5" y="430.0000000000001"></omgdi:waypoint>
        <omgdi:waypoint x="139.9079463570632" y="430.0000000000001"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BE073C85-3FCC-4956-82E0-52A36D847F89" id="BPMNEdge_sid-BE073C85-3FCC-4956-82E0-52A36D847F89" flowable:sourceDockerX="50.0" flowable:sourceDockerY="39.99999999999999" flowable:targetDockerX="50.0" flowable:targetDockerY="39.99999999999999">
        <omgdi:waypoint x="680.0000000000001" y="120.00000000000001"></omgdi:waypoint>
        <omgdi:waypoint x="680.0000000000001" y="48.01388549804685"></omgdi:waypoint>
        <omgdi:waypoint x="365.00000000000006" y="48.01388549804685"></omgdi:waypoint>
        <omgdi:waypoint x="365.00000000000006" y="120.00000000000001"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FC6920CE-C596-4ED8-822C-0E190700C1ED" id="BPMNEdge_sid-FC6920CE-C596-4ED8-822C-0E190700C1ED" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="78.99999999999999">
        <omgdi:waypoint x="391.271186440678" y="421.2994350282487"></omgdi:waypoint>
        <omgdi:waypoint x="678.7012987012988" y="199.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>