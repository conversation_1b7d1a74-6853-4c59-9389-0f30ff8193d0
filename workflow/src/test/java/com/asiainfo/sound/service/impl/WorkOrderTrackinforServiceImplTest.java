package com.asiainfo.sound.service.impl;

import com.asiainfo.sound.dao.IdentyInfoDao;
import com.asiainfo.sound.dao.OrderTrackRecordMapper;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.enums.IdentySubtype;
import com.asiainfo.sound.domain.req.ParaList;
import com.asiainfo.sound.domain.req.ReplyCssReq;
import com.asiainfo.sound.service.SoundSendService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * WorkOrderTrackinforServiceImpl 测试类
 * 主要测试 MeasuresName 处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class WorkOrderTrackinforServiceImplTest {

    @Mock
    private IdentyInfoDao identyInfoDao;

    @Mock
    private OrderTrackRecordMapper orderTrackRecordMapper;

    @Mock
    private SoundSendService soundSendService;

    @InjectMocks
    private WorkOrderTrackinforServiceImpl workOrderTrackinforService;

    private ReplyCssReq cssReq;
    private String testIdentifier = "TEST_WO_001";

    @BeforeEach
    void setUp() {
        cssReq = new ReplyCssReq();
        cssReq.setIdentifier(testIdentifier);
        cssReq.setLaunchCompany("951");
        cssReq.setForwardCompany("952");
    }

    /**
     * 测试智能研判预警单(0024)添加MeasuresName参数
     */
    @Test
    void testHandleMeasuresNameForIntelligentJudgmentWarning() {
        // 准备测试数据
        when(identyInfoDao.getIdentySubType(testIdentifier))
            .thenReturn(IdentySubtype.warning_24.getValue());

        // 设置附件信息
        cssReq.setAttachNameList("整改报告.pdf|附件2.doc");
        cssReq.setParaList(new ArrayList<>());

        // 执行测试 - 通过反射调用私有方法
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, cssReq);

            // 验证结果
            assertNotNull(cssReq.getParaList());
            assertEquals(1, cssReq.getParaList().size());
            
            ParaList measuresNamePara = cssReq.getParaList().get(0);
            assertEquals("MeasuresName", measuresNamePara.getParaID());
            assertEquals("整改报告.pdf", measuresNamePara.getParaVal());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试非智能研判预警单不添加MeasuresName参数
     */
    @Test
    void testHandleMeasuresNameForNonIntelligentJudgmentWarning() {
        // 准备测试数据
        when(identyInfoDao.getIdentySubType(testIdentifier))
            .thenReturn("0001"); // 非智能研判预警单

        cssReq.setAttachNameList("普通附件.pdf");
        cssReq.setParaList(new ArrayList<>());

        // 执行测试
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, cssReq);

            // 验证结果 - 不应该添加MeasuresName参数
            assertTrue(cssReq.getParaList().isEmpty());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试已存在MeasuresName参数的情况
     */
    @Test
    void testHandleMeasuresNameWhenAlreadyExists() {
        // 准备测试数据
        when(identyInfoDao.getIdentySubType(testIdentifier))
            .thenReturn(IdentySubtype.warning_24.getValue());

        // 设置已存在的MeasuresName参数
        List<ParaList> paraList = new ArrayList<>();
        ParaList existingPara = new ParaList("MeasuresName", "已存在的附件.pdf");
        paraList.add(existingPara);
        cssReq.setParaList(paraList);

        cssReq.setAttachNameList("新附件.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, cssReq);

            // 验证结果 - 不应该重复添加
            assertEquals(1, cssReq.getParaList().size());
            assertEquals("已存在的附件.pdf", cssReq.getParaList().get(0).getParaVal());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试从轨迹记录中提取附件名称
     */
    @Test
    void testExtractMeasuresNameFromTrackingInfo() {
        // 准备测试数据
        OrderTrackRecord trackRecord = new OrderTrackRecord();
        trackRecord.setAttachNameList("轨迹附件.pdf");
        trackRecord.setAttachList("track_file.pdf");

        when(orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(testIdentifier))
            .thenReturn(Arrays.asList(trackRecord));

        // cssReq中没有附件信息
        cssReq.setAttachNameList(null);
        cssReq.setAttachList(null);

        // 执行测试
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("extractMeasuresNameFromTrackingInfo", String.class, ReplyCssReq.class);
            method.setAccessible(true);
            String result = (String) method.invoke(workOrderTrackinforService, testIdentifier, cssReq);

            // 验证结果
            assertEquals("轨迹附件.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件路径处理
     */
    @Test
    void testExtractMeasuresNameFromFilePath() {
        // 准备测试数据
        cssReq.setAttachNameList("/upload/files/2024/整改方案.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("extractMeasuresNameFromTrackingInfo", String.class, ReplyCssReq.class);
            method.setAccessible(true);
            String result = (String) method.invoke(workOrderTrackinforService, testIdentifier, cssReq);

            // 验证结果 - 应该提取文件名
            assertEquals("整改方案.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试多个附件的处理
     */
    @Test
    void testExtractMeasuresNameFromMultipleAttachments() {
        // 准备测试数据
        cssReq.setAttachNameList("第一个附件.pdf|第二个附件.doc|第三个附件.xlsx");

        // 执行测试
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("extractMeasuresNameFromTrackingInfo", String.class, ReplyCssReq.class);
            method.setAccessible(true);
            String result = (String) method.invoke(workOrderTrackinforService, testIdentifier, cssReq);

            // 验证结果 - 应该返回第一个附件
            assertEquals("第一个附件.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试异常处理
     */
    @Test
    void testHandleMeasuresNameWithException() {
        // 准备测试数据 - 模拟数据库异常
        when(identyInfoDao.getIdentySubType(testIdentifier))
            .thenThrow(new RuntimeException("数据库连接异常"));

        cssReq.setParaList(new ArrayList<>());

        // 执行测试 - 异常应该被捕获，不影响主流程
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, cssReq);

            // 验证结果 - 异常被捕获，ParaList保持为空
            assertTrue(cssReq.getParaList().isEmpty());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试空值处理
     */
    @Test
    void testHandleMeasuresNameWithNullValues() {
        // 测试 cssReq 为 null
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, (ReplyCssReq) null);
            // 应该正常执行，不抛出异常
        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }

        // 测试 identifier 为空
        cssReq.setIdentifier(null);
        try {
            java.lang.reflect.Method method = WorkOrderTrackinforServiceImpl.class
                .getDeclaredMethod("handleMeasuresNameForTrackinfor", ReplyCssReq.class);
            method.setAccessible(true);
            method.invoke(workOrderTrackinforService, cssReq);
            // 应该正常执行，不抛出异常
        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }
}
