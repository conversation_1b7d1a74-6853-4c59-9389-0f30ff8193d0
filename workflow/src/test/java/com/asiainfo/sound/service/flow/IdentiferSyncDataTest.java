package com.asiainfo.sound.service.flow;

import com.asiainfo.sound.dao.OrderTrackRecordMapper;
import com.asiainfo.sound.domain.OrderTrackRecord;
import com.asiainfo.sound.domain.enums.IdentySubtype;
import com.asiainfo.sound.domain.req.ParaList;
import com.asiainfo.sound.domain.req.SyncDataCssReq;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * IdentiferSyncData 测试类
 * 主要测试 MeasuresName 处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class IdentiferSyncDataTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private OrderTrackRecordMapper orderTrackRecordMapper;

    @InjectMocks
    private IdentiferSyncData identiferSyncData;

    private SyncDataCssReq syncDataCssReq;
    private Map<String, Object> variableMap;
    private String testIdentifier = "TEST_SYNC_001";

    @BeforeEach
    void setUp() {
        syncDataCssReq = new SyncDataCssReq();
        syncDataCssReq.setIdentifier(testIdentifier);
        syncDataCssReq.setIdentySubtype(IdentySubtype.warning_24.getValue());
        syncDataCssReq.setParaList(new ArrayList<>());

        variableMap = new HashMap<>();
        variableMap.put("identifier", testIdentifier);
        variableMap.put("identySubtype", IdentySubtype.warning_24.getValue());

        // 设置ApplicationContext的mock行为
        when(applicationContext.getBean(OrderTrackRecordMapper.class))
            .thenReturn(orderTrackRecordMapper);
    }

    /**
     * 测试智能研判预警单(0024)添加MeasuresName参数
     */
    @Test
    void testHandleMeasuresNameForIntelligentJudgmentWarning() {
        // 准备测试数据
        variableMap.put("attachNameList", "整改方案.pdf|附件2.doc");

        // 执行测试 - 通过反射调用私有方法
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, syncDataCssReq, variableMap);

            // 验证结果
            assertNotNull(syncDataCssReq.getParaList());
            assertEquals(1, syncDataCssReq.getParaList().size());
            
            ParaList measuresNamePara = syncDataCssReq.getParaList().get(0);
            assertEquals("MeasuresName", measuresNamePara.getParaID());
            assertEquals("整改方案.pdf", measuresNamePara.getParaVal());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试非智能研判预警单不添加MeasuresName参数
     */
    @Test
    void testHandleMeasuresNameForNonIntelligentJudgmentWarning() {
        // 准备测试数据 - 设置为非智能研判预警单
        syncDataCssReq.setIdentySubtype("0001");
        variableMap.put("attachNameList", "普通附件.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, syncDataCssReq, variableMap);

            // 验证结果 - 不应该添加MeasuresName参数
            assertTrue(syncDataCssReq.getParaList().isEmpty());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试已存在MeasuresName参数的情况
     */
    @Test
    void testHandleMeasuresNameWhenAlreadyExists() {
        // 准备测试数据 - 设置已存在的MeasuresName参数
        List<ParaList> paraList = new ArrayList<>();
        ParaList existingPara = new ParaList("MeasuresName", "已存在的附件.pdf");
        paraList.add(existingPara);
        syncDataCssReq.setParaList(paraList);

        variableMap.put("attachNameList", "新附件.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, syncDataCssReq, variableMap);

            // 验证结果 - 不应该重复添加
            assertEquals(1, syncDataCssReq.getParaList().size());
            assertEquals("已存在的附件.pdf", syncDataCssReq.getParaList().get(0).getParaVal());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试从流程变量attachList获取附件名称
     */
    @Test
    void testExtractMeasuresNameFromAttachList() {
        // 准备测试数据 - attachNameList为空，使用attachList
        variableMap.put("attachList", "sync_file.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("extractMeasuresNameFromSyncData", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(identiferSyncData, testIdentifier, variableMap);

            // 验证结果
            assertEquals("sync_file.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试从轨迹记录中提取附件名称
     */
    @Test
    void testExtractMeasuresNameFromTrackingRecord() {
        // 准备测试数据 - 流程变量中没有附件信息
        OrderTrackRecord trackRecord = new OrderTrackRecord();
        trackRecord.setAttachNameList("轨迹同步附件.pdf");
        trackRecord.setAttachList("track_sync_file.pdf");

        when(orderTrackRecordMapper.getOrderTrackRecordListByIdentifier(testIdentifier))
            .thenReturn(Arrays.asList(trackRecord));

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("extractMeasuresNameFromSyncData", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(identiferSyncData, testIdentifier, variableMap);

            // 验证结果
            assertEquals("轨迹同步附件.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件路径处理
     */
    @Test
    void testExtractMeasuresNameFromFilePath() {
        // 准备测试数据
        variableMap.put("attachNameList", "/upload/sync/2024/整改计划.pdf");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("extractMeasuresNameFromSyncData", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(identiferSyncData, testIdentifier, variableMap);

            // 验证结果 - 应该提取文件名
            assertEquals("整改计划.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试多个附件的处理
     */
    @Test
    void testExtractMeasuresNameFromMultipleAttachments() {
        // 准备测试数据
        variableMap.put("attachNameList", "主要文档.pdf|辅助文档.doc|参考资料.xlsx");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("extractMeasuresNameFromSyncData", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(identiferSyncData, testIdentifier, variableMap);

            // 验证结果 - 应该返回第一个附件
            assertEquals("主要文档.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试异常处理
     */
    @Test
    void testHandleMeasuresNameWithException() {
        // 准备测试数据 - 模拟数据库异常
        when(applicationContext.getBean(OrderTrackRecordMapper.class))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试 - 异常应该被捕获，不影响主流程
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, syncDataCssReq, variableMap);

            // 验证结果 - 异常被捕获，ParaList保持为空
            assertTrue(syncDataCssReq.getParaList().isEmpty());

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试空值处理
     */
    @Test
    void testHandleMeasuresNameWithNullValues() {
        // 测试 syncDataCssReq 为 null
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, null, variableMap);
            // 应该正常执行，不抛出异常
        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }

        // 测试 identifier 为空
        syncDataCssReq.setIdentifier(null);
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("handleMeasuresNameForSyncData", SyncDataCssReq.class, Map.class);
            method.setAccessible(true);
            method.invoke(identiferSyncData, syncDataCssReq, variableMap);
            // 应该正常执行，不抛出异常
        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试优先级处理 - attachNameList优先于attachList
     */
    @Test
    void testExtractMeasuresNamePriority() {
        // 准备测试数据 - 同时设置attachNameList和attachList
        variableMap.put("attachNameList", "优先附件.pdf");
        variableMap.put("attachList", "次要附件.doc");

        // 执行测试
        try {
            java.lang.reflect.Method method = IdentiferSyncData.class
                .getDeclaredMethod("extractMeasuresNameFromSyncData", String.class, Map.class);
            method.setAccessible(true);
            String result = (String) method.invoke(identiferSyncData, testIdentifier, variableMap);

            // 验证结果 - 应该返回attachNameList的值
            assertEquals("优先附件.pdf", result);

        } catch (Exception e) {
            fail("测试执行失败: " + e.getMessage());
        }
    }
}
