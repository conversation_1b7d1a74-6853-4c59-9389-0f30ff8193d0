package com.asiainfo.sound.service.flow;

import com.ai.flow.dto.ProcessDefinition;
import com.ai.flow.service.IFlowService;
import com.asiainfo.sound.domain.vo.DispatchCssVo;
import org.flowable.common.engine.impl.util.IoUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

import static org.junit.Assert.*;

@SpringBootTest
@RunWith(SpringRunner.class)
public class OAPushEventListenerTest {

    @Autowired
    IFlowService flowService;

    @Test
    public void test() {
        flowService.deployProcessDefinition("test1.bpmn20.xml", IoUtil.readFileAsString("./test1.bpmn20.xml"));

        ProcessDefinition test1 = flowService.getLatestProcessDefinition("test1.bpmn20.xml", "test1");

        flowService.startProcess(test1.getId(), "zhaoyue17", new DispatchCssVo() {{
            this.setIdentifier(UUID.randomUUID().toString());
            this.setTitle("test1111111");
            this.setIdentyType("00");
        }}.toMap());

    }
}