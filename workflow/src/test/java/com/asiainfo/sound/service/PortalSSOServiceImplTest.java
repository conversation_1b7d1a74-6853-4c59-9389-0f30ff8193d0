package com.asiainfo.sound.service;

import com.asiainfo.sound.service.impl.PortalSSOServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static java.lang.System.out;
import static org.junit.Assert.*;
@RunWith(SpringRunner.class)
@SpringBootTest
public class PortalSSOServiceImplTest {
    @Autowired
    PortalSSOServiceImpl ssoService;

    @Test
    public void test() {
        String userIdByToken = ssoService.getUserIdByToken("eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjODExZWQxLWQ5OTAtNDA2ZC1hNGE0LWM5OWE1ZjBiZGY1NSJ9.VjkqTVn-isN8BRQmir6bYbWCBwVVhyxk5HUiWGLgFX9ZXUlnga6pzm5vYhOPICwSComg6FF0FL_mVmxrokepkw", null);
        assertNotNull(userIdByToken);
        out.println(userIdByToken);
    }
}