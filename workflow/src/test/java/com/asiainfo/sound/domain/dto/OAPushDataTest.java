package com.asiainfo.sound.domain.dto;


import lombok.SneakyThrows;
import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;

public class OAPushDataTest {
    @SneakyThrows
    @Test
    public void serializeTest() {

        OAPushData data = new OAPushData();
        data.setId("1");
        data.setName("test在正中");

        ArrayList<TaskForOAPush> taskForOAPushes = new ArrayList<TaskForOAPush>() {{
            for (int i = 0; i < 2; i++) {
                int finalI = i;
                add(new TaskForOAPush() {{
                    setDocId((finalI + 100) + "");
                    setDocType("type1");
                    setEndTime("2020101011111212");
                    setTitle("测试待办");
                }});
            }
        }};
        ArrayList<TaskForOAPush> taskForOAPushes1 = new ArrayList<TaskForOAPush>() {{
            for (int i = 0; i < 2; i++) {
                int finalI = i;
                add(new TaskForOAPush() {{
                    setDocId((finalI + 100) + "");
                    setDocType("type122");
                    setEndTime("2020101011111212");
                    setTitle ("测试待办22");
                }});
            }
        }};

        data.setCancel(taskForOAPushes);
        data.setClose(taskForOAPushes1);


        assertEquals("<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\"?>\n" +
                "<apps id=\"1\" name=\"test在正中\">\n" +
                "    <close>\n" +
                "        <work docId=\"100\" docType=\"type122\" title=\"测试待办22\" endTime=\"2020101011111212\"/>\n" +
                "        <work docId=\"101\" docType=\"type122\" title=\"测试待办22\" endTime=\"2020101011111212\"/>\n" +
                "    </close>\n" +
                "    <cancel>\n" +
                "        <work docId=\"100\" docType=\"type1\" title=\"测试待办\" endTime=\"2020101011111212\"/>\n" +
                "        <work docId=\"101\" docType=\"type1\" title=\"测试待办\" endTime=\"2020101011111212\"/>\n" +
                "    </cancel>\n" +
                "</apps>", data.toXMLString().trim());
    }
}