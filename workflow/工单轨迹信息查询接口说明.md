# 工单轨迹信息查询接口说明

## 概述

新增了用于查询工单轨迹信息上报信息的接口，支持基础查询和条件查询，同时提供原始数据和格式化数据两种响应格式。

## 接口列表

### 1. 基础查询接口

#### 1.1 查询工单轨迹信息上报信息
- **接口地址**: `POST /apply/queryTrackinforInfo`
- **功能描述**: 根据工单编号查询工单轨迹信息上报信息
- **请求参数**:
```json
{
  "identifier": "工单编号"
}
```
- **响应数据**: 返回 `List<OrderTrackRecord>` 原始轨迹记录列表

#### 1.2 查询工单轨迹信息上报信息（格式化）
- **接口地址**: `POST /apply/queryTrackinforInfoFormatted`
- **功能描述**: 根据工单编号查询工单轨迹信息上报信息，返回格式化的响应数据
- **请求参数**:
```json
{
  "identifier": "工单编号"
}
```
- **响应数据**: 返回 `List<TrackinforInfoResp>` 格式化轨迹记录列表

### 2. 条件查询接口

#### 2.1 根据条件查询工单轨迹信息上报信息
- **接口地址**: `POST /apply/queryTrackinforInfoByCondition`
- **功能描述**: 支持按轨迹类型、时间范围等条件查询工单轨迹信息上报信息
- **请求参数**:
```json
{
  "identifier": "工单编号",
  "includeAttachment": false,
  "trackType": "01",
  "startTime": "20250101000000",
  "endTime": "20251231235959"
}
```
- **响应数据**: 返回 `List<OrderTrackRecord>` 原始轨迹记录列表

#### 2.2 根据条件查询工单轨迹信息上报信息（格式化）
- **接口地址**: `POST /apply/queryTrackinforInfoByConditionFormatted`
- **功能描述**: 支持按轨迹类型、时间范围等条件查询工单轨迹信息上报信息，返回格式化的响应数据
- **请求参数**:
```json
{
  "identifier": "工单编号",
  "includeAttachment": false,
  "trackType": "01",
  "startTime": "20250101000000",
  "endTime": "20251231235959"
}
```
- **响应数据**: 返回 `List<TrackinforInfoResp>` 格式化轨迹记录列表

## 请求参数说明

### TrackinforQueryReq 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| identifier | String | 是 | 工单编号 |
| includeAttachment | Boolean | 否 | 是否包含附件信息，默认false |
| trackType | String | 否 | 轨迹类型过滤：01-流转环节，02-审核环节，03-其他环节 |
| startTime | String | 否 | 开始时间，格式：YYYYMMDDHHMMSS |
| endTime | String | 否 | 结束时间，格式：YYYYMMDDHHMMSS |

## 响应数据说明

### OrderTrackRecord（原始数据）
包含数据库中的原始字段，如：
- id: 主键
- identifier: 工单编号
- processInstanceId: 流程实例ID
- taskId: 任务ID
- trackType: 轨迹类型
- handingTime: 处理时间（YYYYMMDDHHMMSS格式）
- handler: 处理人
- handingOpinions: 处理意见
- attachNameList: 附件名称列表（|分隔）
- 等等...

### TrackinforInfoResp（格式化数据）
在原始数据基础上增加了格式化和描述字段：
- formattedHandingTime: 格式化的处理时间（yyyy-MM-dd HH:mm:ss）
- attachNameList: 解析后的附件名称列表
- attachList: 解析后的附件列表
- attachFileNameList: 解析后的附件文件名列表
- trackTypeDesc: 轨迹类型描述
- interfaceLinkDesc: 接口环节类型描述
- handlerLeadLevelDesc: 处理人领导级别描述
- hasAttachment: 是否包含附件

## 使用示例

### 查询指定工单的所有轨迹信息
```bash
curl -X POST "http://localhost:8080/apply/queryTrackinforInfoFormatted" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "WO202501110001"
  }'
```

### 查询指定工单在指定时间范围内的审核环节轨迹
```bash
curl -X POST "http://localhost:8080/apply/queryTrackinforInfoByConditionFormatted" \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "WO202501110001",
    "trackType": "02",
    "startTime": "20250101000000",
    "endTime": "20250131235959"
  }'
```

## 错误处理

所有接口都包含统一的错误处理：
- 工单编号为空时返回错误信息
- 查询异常时返回详细的错误描述
- 未找到数据时返回空列表

## 日志记录

所有接口调用都会记录详细的日志信息，包括：
- 查询开始和结束日志
- 查询参数和结果统计
- 异常情况的详细错误日志

## 性能考虑

- 建议在数据库中为 `identifier`、`track_type`、`handing_time` 字段建立索引
- 对于大量数据的查询，建议使用分页查询
- 时间范围查询建议限制在合理的范围内
